"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Database, FileUp } from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { SavedInvoiceCard } from "./saved-invoice-card";
import type { InvoiceData } from "@/types/invoice";

interface SavedTabProps {
  savedInvoices: InvoiceData[];
  onUpdateInvoice: (index: number, data: InvoiceData) => void;
  onGoToUpload: () => void;
}

export function SavedTab({
  savedInvoices,
  onUpdateInvoice,
  onGoToUpload
}: SavedTabProps) {
  if (savedInvoices.length === 0) {
    return (
      <div className="text-center py-12">
        <Database className="mx-auto h-12 w-12 text-muted-foreground" />
        <h3 className="mt-4 text-lg font-semibold">No saved invoices</h3>
        <p className="mt-2 text-sm text-muted-foreground">
          Process some invoices to see them here
        </p>
        <Button 
          variant="outline" 
          className="mt-4"
          onClick={onGoToUpload}
        >
          Go to Upload
        </Button>
      </div>
    );
  }
  
  return (
    <div className="space-y-6">
      <ScrollArea className="h-[600px] pr-4">
        {savedInvoices.map((invoice, index) => (
          <SavedInvoiceCard
            key={index}
            invoice={invoice}
            index={index}
            onUpdate={onUpdateInvoice}
          />
        ))}
      </ScrollArea>
      
      <Separator />
      
      <div className="flex justify-between items-center">
        <div>
          <p className="text-sm text-muted-foreground">
            {savedInvoices.length} invoice{savedInvoices.length !== 1 ? 's' : ''} saved
          </p>
        </div>
        <Button
          onClick={onGoToUpload}
          className="w-auto"
        >
          <FileUp className="mr-2 h-4 w-4" />
          Upload More
        </Button>
      </div>
    </div>
  );
} 