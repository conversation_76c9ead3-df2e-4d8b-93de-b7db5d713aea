"use client";

import { useState, useEffect } from "react";
import type React from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  MoreHorizontal,
  Calendar,
  Clock,
  Mail,
  Edit,
  Trash2,
  Pause,
  Play,
  Plus,
  Loader2,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { formatDistanceToNow, format } from "date-fns";
import { toast } from "sonner";
import {
  deleteSchedule,
  toggleScheduleStatus,
  getScheduledReports
} from "@/lib/actions/reports";
import { useRouter } from "next/navigation";
import { Report, ScheduledReport as PrismaScheduledReport } from "@prisma/client";

// Represents the raw data structure for scheduled reports before processing
interface InputReportData extends PrismaScheduledReport {
  report: Report & { name?: string }; // name is optional on input, title is from Prisma Report
  // Optional target fields that might exist on input or will be populated
  emailRecipients?: string[];
  isActive?: boolean;
  nextRunDate?: Date | null; // Aligning with nextRunTime which can be null
}

interface ScheduledReport extends PrismaScheduledReport {
  report: Report & { name: string }; // 'name' is now a required string
  // Add compatibility fields for backward compatibility
  nextRunDate?: Date | null; // Keep consistent with InputReportData and Prisma types
  emailRecipients?: string[];
  isActive?: boolean;
}

export function ReportScheduler() {
  const [scheduledReports, setScheduledReports] = useState<ScheduledReport[]>([]);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  // Process scheduled reports to ensure compatibility with both old and new field names
  const processScheduledReports = (reports: InputReportData[]): ScheduledReport[] => {
    return reports.map(report => {
      // Process report name/title
      if (!report.report.name && report.report.title) {
        report.report.name = report.report.title;
      }

      // Process email recipients
      if (!report.emailRecipients && report.emailAddresses) {
        report.emailRecipients = report.emailAddresses.split(',').filter(Boolean);
      } else if (!report.emailRecipients) {
        report.emailRecipients = [];
      }

      // Process active status
      if (report.isActive === undefined && report.active !== undefined) {
        report.isActive = report.active;
      }

      // Process next run date
      if (!report.nextRunDate && report.nextRunTime) {
        report.nextRunDate = report.nextRunTime;
      }

      return report as ScheduledReport;
    });
  };

  useEffect(() => {
    async function fetchData() {
      try {
        const reports = await getScheduledReports();
        setScheduledReports(processScheduledReports(reports));
      } catch (error) {
        toast.error("Failed to load scheduled reports");
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, []);

  const handleToggleStatus = async (id: string) => {
    try {
      // Assume toggleScheduleStatus returns the updated schedule, potentially without the full 'report' object.
      // It might return a PrismaScheduledReport or a subset of its fields (e.g., just the new active status).
      const updatedStatusFields = await toggleScheduleStatus(id);

      // Find the original full scheduled report from the state to access its 'report' object
      // and other details not returned by toggleScheduleStatus.
      const originalSchedule = scheduledReports.find(s => s.id === id);

      if (!originalSchedule) {
        toast.error("Failed to update schedule: Original schedule not found locally.");
        // Optionally, refetch or handle error more gracefully
        return;
      }

      // Construct the object for processing by merging updated fields with the original schedule data.
      // This ensures the 'report' object and other necessary fields are present.
      const scheduleToProcess: InputReportData = {
        ...originalSchedule, // Base with all original data, including the 'report' object.
        ...updatedStatusFields, // Override with any fields returned by toggleScheduleStatus (e.g., 'active').
        // The 'report' object from originalSchedule already conforms to (Report & { name: string })
        // or (Report & { name?: string }) if it was an InputReportData. Cast if necessary for safety,
        // but originalSchedule.report should be suitable if originalSchedule is from scheduledReports state.
        // Assuming originalSchedule.report is already Report & { name: string } as it's from ScheduledReport[] state.
        // For InputReportData, it needs Report & { name?: string }
        report: originalSchedule.report as (Report & { name?: string }),
      };

      const processedSchedule = processScheduledReports([scheduleToProcess])[0];

      setScheduledReports(scheduledReports.map(schedule =>
        schedule.id === id ? processedSchedule : schedule
      ));

      toast.success(`Schedule ${processedSchedule.isActive ? 'activated' : 'paused'}`);
    } catch (error) {
      toast.error("Failed to update schedule");
    }
  };

  const handleDeleteSchedule = async (id: string) => {
    try {
      await deleteSchedule(id);

      // Update the local state
      setScheduledReports(scheduledReports.filter(schedule => schedule.id !== id));

      toast.success("Schedule deleted");
    } catch (error) {
      toast.error("Failed to delete schedule");
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-medium">Scheduled Reports</h2>
          <Button disabled>
            <Plus className="mr-2 h-4 w-4" />
            Schedule New Report
          </Button>
        </div>
        <Card>
          <CardContent className="flex justify-center items-center py-16">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-medium">Scheduled Reports</h2>
        <Button onClick={() => router.push("/dashboard/reports/schedule/new")}>
          <Plus className="mr-2 h-4 w-4" />
          Schedule New Report
        </Button>
      </div>

      {scheduledReports.length === 0 ? (
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col items-center justify-center py-8 text-center">
              <Calendar className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium">No scheduled reports</h3>
              <p className="text-muted-foreground mt-2 mb-4">
                You haven&apos;t set up any scheduled reports yet. Select a report to schedule it.
              </p>
              <Button onClick={() => router.push("/dashboard/reports/schedule/new")}>
                <Plus className="mr-2 h-4 w-4" />
                Select a Report to Schedule
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Report Name</TableHead>
                  <TableHead>Frequency</TableHead>
                  <TableHead>Next Run</TableHead>
                  <TableHead>Recipients</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {scheduledReports.map((report) => (
                  <TableRow key={report.id}>
                    <TableCell className="font-medium">{report.report.name}</TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                        {report.frequency}
                      </div>
                    </TableCell>
                    <TableCell>
                      {report.nextRunTime ? (
                        <>
                          {format(new Date(report.nextRunTime), "MMM d, yyyy")}
                          <div className="text-xs text-muted-foreground">
                            {formatDistanceToNow(new Date(report.nextRunTime), { addSuffix: true })}
                          </div>
                        </>
                      ) : (
                        <span className="text-muted-foreground">Not scheduled</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <Mail className="mr-2 h-4 w-4 text-muted-foreground" />
                        {report.emailRecipients && report.emailRecipients.length > 0 ? (
                          <>
                            {report.emailRecipients.length} recipient{report.emailRecipients.length !== 1 ? 's' : ''}
                          </>
                        ) : report.emailAddresses ? (
                          <>
                            {report.emailAddresses.split(',').filter(Boolean).length} recipient{report.emailAddresses.split(',').filter(Boolean).length !== 1 ? 's' : ''}
                          </>
                        ) : (
                          <>0 recipients</>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          (report.isActive !== undefined ? report.isActive : report.active) ? "default" : "secondary"
                        }
                      >
                        {(report.isActive !== undefined ? report.isActive : report.active) ? "Active" : "Paused"}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                            <span className="sr-only">Open menu</span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={() => router.push(`/dashboard/reports/schedule/edit/${report.id}`)}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit Schedule
                          </DropdownMenuItem>
                          {(report.isActive !== undefined ? report.isActive : report.active) ? (
                            <DropdownMenuItem onClick={() => handleToggleStatus(report.id)}>
                              <Pause className="mr-2 h-4 w-4" />
                              Pause Schedule
                            </DropdownMenuItem>
                          ) : (
                            <DropdownMenuItem onClick={() => handleToggleStatus(report.id)}>
                              <Play className="mr-2 h-4 w-4" />
                              Resume Schedule
                            </DropdownMenuItem>
                          )}
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            className="text-red-600"
                            onClick={() => handleDeleteSchedule(report.id)}
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete Schedule
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Schedule a New Report</CardTitle>
          <CardDescription>
            Select a report, then set up automated delivery
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
            <ScheduleOption
              title="Daily Report"
              description="Generate reports every day at a specified time"
              icon={<Clock className="h-8 w-8 text-blue-500" />}
              onClick={() => router.push("/dashboard/reports/schedule/new?frequency=daily")}
            />
            <ScheduleOption
              title="Weekly Report"
              description="Generate reports on a specific day each week"
              icon={<Calendar className="h-8 w-8 text-purple-500" />}
              onClick={() => router.push("/dashboard/reports/schedule/new?frequency=weekly")}
            />
            <ScheduleOption
              title="Monthly Report"
              description="Generate reports on a specific day each month"
              icon={<Calendar className="h-8 w-8 text-emerald-500" />}
              onClick={() => router.push("/dashboard/reports/schedule/new?frequency=monthly")}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

interface ScheduleOptionProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  onClick?: () => void;
}

function ScheduleOption({ title, description, icon, onClick }: ScheduleOptionProps) {
  return (
    <Card className="flex flex-col">
      <CardHeader className="flex flex-row items-center gap-4 pb-2">
        {icon}
        <div>
          <CardTitle className="text-lg">{title}</CardTitle>
          <CardDescription>{description}</CardDescription>
        </div>
      </CardHeader>
      <CardContent className="mt-auto pt-4">
        <Button variant="outline" className="w-full" onClick={onClick}>
          <Plus className="mr-2 h-4 w-4" />
          Create Schedule
        </Button>
      </CardContent>
    </Card>
  );
}
