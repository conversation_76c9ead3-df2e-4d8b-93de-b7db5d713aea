"use client";

import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea_landing';
import { Button } from '@/components/ui/button';
import { useState } from 'react';
import { useUser } from '@clerk/nextjs';

export default function HelpSupportPage() {
  const { user } = useUser();
  const [form, setForm] = useState({
    name: '',
    email: '',
    phone_number: '',
    message: '',
  });
  const [submitted, setSubmitted] = useState(false);                                                   
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value });
    setError('');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!form.name || !form.email || !form.phone_number || !form.message) {
      setError('Please fill in all fields.');
      return;
    }
    if (!user?.id) {
      setError('User not authenticated.');
      return;
    }
    setLoading(true);
    setError('');
    setSubmitted(false);
    try {
      const res = await fetch(`https://rendering-pills-realm-realize.trycloudflare.com/api/v1/help-support/${user.id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': user.id,
        },
        body: JSON.stringify(form),
      });
      if (!res.ok) {
        throw new Error('Failed to submit.');
      }
      setSubmitted(true);
      setForm({ name: '', email: '', phone_number: '', message: '' });
    } catch (err) {
      setError('Submission failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-black text-white relative overflow-hidden min-h-screen">
      {/* Background text */}
      <div className="absolute inset-0 flex items-center justify-center pointer-events-none opacity-10 select-none">
        <h1 className="text-[8vw] font-bold tracking-tighter">HELP & SUPPORT</h1>
      </div>
      <div className="max-w-2xl mx-auto px-4 py-8 sm:py-12 md:py-16 relative z-10">
        <div className="bg-neutral-900/60 backdrop-blur-xl rounded-xl p-4 sm:p-8 shadow-[0_0_0_0,inset_0_0_30px_rgba(200,200,200,0.1)] border border-neutral-700">
          <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-2">Help & Support</h1>
          <p className="text-sm sm:text-base text-gray-400 mb-6">Need assistance? Fill out the form below and our team will get back to you as soon as possible.</p>
          <form className="space-y-4" onSubmit={handleSubmit}>
            <Input
              type="text"
              name="name"
              placeholder="Name"
              value={form.name}
              onChange={handleChange}
              className="bg-neutral-800/60 border-0 h-10 sm:h-12 md:h-14 text-white text-sm sm:text-base placeholder:text-gray-500"
            />
            <Input
              type="email"
              name="email"
              placeholder="Email"
              value={form.email}
              onChange={handleChange}
              className="bg-neutral-800/60 border-0 h-10 sm:h-12 md:h-14 text-white text-sm sm:text-base placeholder:text-gray-500"
            />
            <Input
              type="tel"
              name="phone_number"
              placeholder="Phone Number"
              value={form.phone_number}
              onChange={handleChange}
              className="bg-neutral-800/60 border-0 h-10 sm:h-12 md:h-14 text-white text-sm sm:text-base placeholder:text-gray-500"
            />
            <Textarea
              name="message"
              placeholder="Message"
              value={form.message}
              onChange={handleChange}
              className="bg-gray-800/60 min-h-[140px] sm:min-h-[180px] md:min-h-[200px] border border-neutral-700 text-white text-sm sm:text-base placeholder:text-gray-500 resize-none"
            />
            {error && <div className="text-red-500 text-sm">{error}</div>}
            {submitted && !error && (
              <div className="text-green-500 text-sm">Thank you! Your message has been submitted.</div>
            )}
            <Button
              type="submit"
              variant="default"
              className="w-full bg-white text-black hover:bg-gray-200 h-10 sm:h-12 md:h-14 mt-2 text-sm sm:text-base font-medium"
              disabled={loading}
            >
              {loading ? 'Submitting...' : 'Submit'}
            </Button>
          </form>
        </div>
      </div>
    </div>
  );
} 
//changes