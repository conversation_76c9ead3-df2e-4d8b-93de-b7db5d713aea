'use client'
import { useSearchParams } from 'next/navigation';

export default function CheckoutPage() {
  const searchParams = useSearchParams();
  const plan = searchParams.get('plan');

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-black text-white">
      <h1 className="text-3xl font-bold mb-4">Checkout</h1>
      {plan ? (
        <p className="text-lg">You selected plan: <span className="font-mono bg-neutral-900 px-2 py-1 rounded">{plan}</span></p>
      ) : (
        <p className="text-lg text-red-400">No plan selected.</p>
      )}
      {/* Add your checkout logic here */}
    </div>
  );
} 