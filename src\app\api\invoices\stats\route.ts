import {NextResponse } from 'next/server';
import db from '@/db/db';
import { auth } from '@clerk/nextjs/server';

export async function GET() {
  try {
    // Get authenticated user
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user's organization
    const user = await db.user.findUnique({
      where: { clerkId: userId },
      include: { organizations: true }
    });
    
    if (!user || !user.organizations.length) {
      return NextResponse.json(
        { error: 'No organization found' },
        { status: 404 }
      );
    }
    
    const organizationId = user.organizations[0].id;

    // Get stats by invoice status
    const invoiceStats = await db.invoice.groupBy({
      by: ['status'],
      where: {
        organizationId
      },
      _count: {
        id: true
      },
      _sum: {
        amount: true
      }
    });
    // Calculate totals
    let totalCount = 0;
    let totalAmount = 0;
    let paidCount = 0;
    let paidAmount = 0;
    let pendingCount = 0;
    let pendingAmount = 0;
    let overdueCount = 0;
    let overdueAmount = 0;
    
    invoiceStats.forEach(stat => {
      const count = stat._count.id;
      const amount = Number(stat._sum.amount) || 0;
      
      totalCount += count;
      totalAmount += amount;
      
      if (stat.status === 'PAID') {
        paidCount = count;
        paidAmount = amount;
      } else if (stat.status === 'PENDING') {
        pendingCount = count;
        pendingAmount = amount;
      } else if (stat.status === 'OVERDUE') {
        overdueCount = count;
        overdueAmount = amount;
      }
    });
    
    // Get monthly stats for the current year
    const currentYear = new Date().getFullYear();
    const startOfYear = new Date(currentYear, 0, 1); // January 1st
    
    const monthlyInvoices = await db.invoice.findMany({
      where: {
        organizationId,
        createdAt: {
          gte: startOfYear
        }
      },
      select: {
        amount: true,
        status: true,
        createdAt: true
      }
    });
    
    // Format monthly data
    const monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 
                       'July', 'August', 'September', 'October', 'November', 'December'];
    
    const monthlyData = Array(12).fill(0).map((_, i) => ({
      month: monthNames[i],
      count: 0,
      amount: 0,
      paid: 0,
      pending: 0,
      overdue: 0
    }));
    
    monthlyInvoices.forEach(invoice => {
      const month = invoice.createdAt.getMonth();
      const amount = Number(invoice.amount) || 0;
      
      monthlyData[month].count += 1;
      monthlyData[month].amount += amount;
      
      if (invoice.status === 'PAID') {
        monthlyData[month].paid += amount;
      } else if (invoice.status === 'PENDING') {
        monthlyData[month].pending += amount;
      } else if (invoice.status === 'OVERDUE') {
        monthlyData[month].overdue += amount;
      }
    });
    
    // Get top vendors by invoice count
    const vendorStats = await db.invoice.groupBy({
      by: ['vendorName'],
      where: {
        organizationId,
        vendorName: {
          not: null
        }
      },
      _count: {
        id: true
      },
      _sum: {
        amount: true
      },
      orderBy: [
        {
          _count: {
            id: 'desc'
          }
        }
      ]
    });
    
    const topVendors = vendorStats.map(vendor => ({
      name: vendor.vendorName || 'Unknown',
      count: vendor._count.id,
      amount: Number(vendor._sum.amount) || 0
    }));
    
    // Get recent invoices
    const recentInvoices = await db.invoice.findMany({
      where: { 
        organizationId 
      },
      orderBy: { 
        createdAt: 'desc' 
      },
      select: {
        id: true,
        title: true, 
        amount: true,
        currency: true,
        status: true,
        dueDate: true,
        vendorName: true,
        createdAt: true
      }
    });

    // Format recentInvoices for frontend
    const formattedRecentInvoices = recentInvoices.map(inv => ({
      id: inv.id,
      vendorName: inv.vendorName || "Unknown",
      amount: Number(inv.amount) || 0,
      status: inv.status,
      createdAt: inv.createdAt instanceof Date ? inv.createdAt.toISOString() : inv.createdAt,
    }));
    
    // Calculate payment performance using due dates for PAID invoices
    const paidInvoices = await db.invoice.findMany({
      where: {
        organizationId,
        status: 'PAID',
        dueDate: {
          not: null
        }
      },
      select: {
        status: true,
        dueDate: true,
        updatedAt: true  // Using updatedAt as a proxy for payment date
      }
    });
    
    let totalDaysToPayment = 0;
    let onTimePayments = 0;
    
    paidInvoices.forEach(invoice => {
      if (invoice.dueDate) {
        const paymentDate = new Date(invoice.updatedAt); // Assuming updatedAt reflects when payment was recorded
        const dueDate = new Date(invoice.dueDate);
        const daysDifference = Math.floor(
          (paymentDate.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24)
        );
        
        totalDaysToPayment += daysDifference;
        
        if (daysDifference <= 0) {
          onTimePayments++;
        }
      }
    });
    
    const avgDaysToPayment = paidInvoices.length > 0 
      ? totalDaysToPayment / paidInvoices.length 
      : 0;
      
    const onTimePaymentRate = paidInvoices.length > 0 
      ? (onTimePayments / paidInvoices.length) * 100 
      : 0;
    
    return NextResponse.json({
      summary: {
        total: {
          count: totalCount,
          amount: totalAmount
        },
        paid: {
          count: paidCount,
          amount: paidAmount
        },
        pending: {
          count: pendingCount,
          amount: pendingAmount
        },
        overdue: {
          count: overdueCount,
          amount: overdueAmount
        },
        paidPercentage: totalCount > 0 ? Math.round((paidCount / totalCount) * 100) : 0
      },
      monthly: monthlyData,
      topVendors: topVendors,
      recentInvoices: formattedRecentInvoices,
      paymentPerformance: {
        averageDaysToPayment: Math.round(avgDaysToPayment * 10) / 10,
        onTimePaymentRate: Math.round(onTimePaymentRate)
      }
    });
  } catch (error) {
    console.error('Invoice stats API error:', error);
    return NextResponse.json(
      { error: 'Error processing invoice statistics request' },
      { status: 500 }
    );
  }
} 