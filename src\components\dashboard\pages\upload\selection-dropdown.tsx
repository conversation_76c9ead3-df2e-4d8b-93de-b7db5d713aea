'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Check, ChevronDown } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  CategorySuggestion,
  VendorTypeSuggestion,
} from '@/types/invoice';

interface SelectionDropdownProps {
  label: string;
  options: string[] | CategorySuggestion[] | VendorTypeSuggestion[];
  selectedValue?: string | null;
  onSelect: (value: string) => void;
  className?: string;
  type?: 'category' | 'vendorType';
  invoiceId?: string; // Add invoice ID to ensure correct selection
}

export function SelectionDropdown({
  label,
  options,
  selectedValue,
  onSelect,
  className = '',
  type = 'category',
  invoiceId,
}: SelectionDropdownProps) {
  const [isOpen, setIsOpen] = useState(false);

  // Process options to ensure we have a consistent format
  const processedOptions = options.map((option) => {
    if (typeof option === 'string') {
      return { name: option, confidence: 0 };
    }
    return option;
  });

  // Sort options by confidence if available
  const sortedOptions = [...processedOptions].sort(
    (a, b) => b.confidence - a.confidence
  );

  // Check if there's just a single option with high confidence
  const isSingleDirectMatch =
    sortedOptions.length === 1 && sortedOptions[0].confidence >= 90;

  // If there's a direct match, select it automatically
  useEffect(() => {
    if (isSingleDirectMatch && !selectedValue) {
      onSelect(sortedOptions[0].name);
    }
  }, [isSingleDirectMatch, selectedValue, onSelect, sortedOptions]);

  const handleSelect = (value: string) => {
    console.log(
      'Selection dropdown - handleSelect called with value:',
      value,
      'for invoice:',
      invoiceId
    );
    onSelect(value);
    setIsOpen(false);

    // Update the data attributes to ensure they reflect the current selection
    const selector =
      type === 'category'
        ? '[data-selected-category]'
        : '[data-selected-vendor-type]';
    const element = document.querySelector(selector);
    if (element) {
      element.setAttribute('data-value', value);
      element.setAttribute(
        type === 'category'
          ? 'data-selected-category'
          : 'data-selected-vendor-type',
        value
      );
    }
  };

  // For a single match, just show it as selected
  if (isSingleDirectMatch) {
    return (
      <div className={className}>
        <Button
          variant="outline"
          className="w-full justify-between bg-[#081028] border-[#1B3848] text-white"
          disabled
          data-selected-category={
            type === 'category' ? sortedOptions[0].name : undefined
          }
          data-selected-vendor-type={
            type === 'vendorType' ? sortedOptions[0].name : undefined
          }
          data-value={sortedOptions[0].name}
          data-invoice-id={invoiceId || ''}
        >
          <span className="font-medium">{sortedOptions[0].name}</span>
          <Check className="h-4 w-4 ml-2 text-[#00BBFF]" />
        </Button>
      </div>
    );
  }

  return (
    <div className={`w-full ${className}`}>
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            className="w-full justify-between bg-[#081028] border-[#1B3848] hover:bg-[#1B3848]/50 focus:bg-[#1B3848]/50 text-white"
            onClick={() => setIsOpen(true)}
            data-selected-category={
              type === 'category' ? selectedValue : undefined
            }
            data-selected-vendor-type={
              type === 'vendorType' ? selectedValue : undefined
            }
            data-value={selectedValue}
            data-invoice-id={invoiceId || ''}
          >
            {selectedValue || label}
            <ChevronDown className="h-4 w-4 ml-2" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          align="start"
          className="w-[260px] p-0 bg-[#081028] border-[#1B3848] text-white"
        >
          <DropdownMenuLabel className="font-bold text-sm px-3 py-2 border-b border-[#1B3848]">
            {label}
          </DropdownMenuLabel>
          <div className="max-h-[220px] overflow-y-auto py-1">
            {sortedOptions.map((option) => (
              <DropdownMenuItem
                key={option.name}
                className="flex flex-col items-start px-3 py-2 cursor-pointer hover:bg-[#0B1739] focus:bg-[#0B1739]"
                onClick={() => handleSelect(option.name)}
                data-value={option.name}
              >
                <div className="flex items-center justify-between w-full">
                  <span className="font-medium">{option.name}</span>
                  <div className="flex items-center">
                    {option.confidence > 0 && (
                      <span className="text-xs font-medium">
                        {option.confidence}%
                      </span>
                    )}
                    {selectedValue === option.name && (
                      <Check className="h-4 w-4 text-[#00BBFF] ml-2" />
                    )}
                  </div>
                </div>
                {option.confidence > 0 && (
                  <div className="w-full mt-1">
                    <div className="h-1.5 w-full overflow-hidden rounded-full bg-primary/20">
                      <div
                        className="h-full bg-[#E6A417] transition-all"
                        style={{ width: `${option.confidence}%` }}
                      />
                    </div>
                  </div>
                )}
              </DropdownMenuItem>
            ))}
          </div>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
