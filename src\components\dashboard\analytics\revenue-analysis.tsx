"use client";

import { useEffect, useState } from "react";
import { <PERSON>, CardContent, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { ChartContainer, ChartTooltip } from "@/components/ui/chart";
import {
  Area,
  AreaChart,
  CartesianGrid,
  XAxis,
  YAxis,
  ResponsiveContainer,
  Bar,
  BarChart,
  Cell,
} from "recharts";
import { formatCurrency } from "@/lib/utils";
import { getRevenueAnalysis } from "@/lib/actions/analytics";
import type { FilterValues } from "./data-filters";
import type { RevenueAnalysis } from "@/types/analytics";
import { Skeleton } from "@/components/ui/skeleton";
import { motion } from "motion/react";
import Colors from '@/components/theme/Colors';

import { TrendingUp, Users, DollarSign } from "lucide-react";

interface RevenueAnalysisProps {
  dateRange: {
    from: Date;
    to: Date;
  };
  filters: FilterValues;
}

export function RevenueAnalysisComponent({
  dateRange,
  filters,
}: RevenueAnalysisProps) {
  const [revenueData, setRevenueData] = useState<RevenueAnalysis | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function loadRevenueData() {
      setLoading(true);
      setError(null);
      try {
        const data = await getRevenueAnalysis(
          dateRange.from,
          dateRange.to,
          filters
        );
        setRevenueData(data);
      } catch (err) {
        console.error("Failed to load revenue analysis:", err);
        setError("Failed to load revenue data");
      } finally {
        setLoading(false);
      }
    }

    loadRevenueData();
  }, [dateRange, filters]);

  if (loading) {
    return (
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-48" />
            <Skeleton className="h-4 w-32" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-[300px] w-full" />
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-48" />
            <Skeleton className="h-4 w-32" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-[300px] w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !revenueData) {
    return (
      <div className="grid gap-6 md:grid-cols-2">
        <Card className="border-destructive/20">
          <CardHeader>
            <CardTitle className="text-destructive">Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-destructive/80">
              {error || "Failed to load revenue data"}
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const chartConfig = {
    revenue: {
      label: "Revenue",
      color: "hsl(var(--chart-1))",
    },
  };

  const colors = [
    Colors.primary,
    Colors.success,
    Colors.warning,
    Colors.accent,
    Colors.info,
    Colors.error,
    Colors.primaryDark,
    Colors.text,
    Colors.textLight,
    Colors.border,
  ];

  return (
    <div className="space-y-6">
      {/* Revenue Overview Cards */}
      <div className="grid gap-4 md:grid-cols-3">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card className={`border border-[${Colors.primary}]/10 bg-[${Colors.background}]/50 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300`}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Revenue
              </CardTitle>
              <DollarSign className={`h-4 w-4 text-[${Colors.text}]`} />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold text-[${Colors.success}]`}>
                {formatCurrency(revenueData.totalRevenue)}
              </div>
              <p className="text-xs text-muted-foreground">
                From {revenueData.topCustomers.length} customers
              </p>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <Card className={`border border-[${Colors.primary}]/10 bg-[${Colors.background}]/50 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300`}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Growth Rate</CardTitle>
              <TrendingUp className={`h-4 w-4 text-[${Colors.text}]`} />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold text-[${Colors.primary}]`}>
                {revenueData.growthRate.toFixed(1)}%
              </div>
              <p className="text-xs text-muted-foreground">Month over month</p>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <Card className={`border border-[${Colors.primary}]/10 bg-[${Colors.background}]/50 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300`}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Avg Customer Value
              </CardTitle>
              <Users className={`h-4 w-4 text-[${Colors.text}]`} />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold text-[${Colors.accent}]`}>
                {revenueData.topCustomers.length > 0
                  ? formatCurrency(
                      revenueData.totalRevenue / revenueData.topCustomers.length
                    )
                  : formatCurrency(0)}
              </div>
              <p className="text-xs text-muted-foreground">Per customer</p>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Charts */}
      <div className="grid gap-6 md:grid-cols-3">
        {/* Monthly Revenue Trend */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Card className={`border border-[${Colors.primary}]/10 bg-[${Colors.background}]/50 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300`}>
            <CardHeader>
              <CardTitle className="text-xl font-bold flex items-center gap-2">
                <TrendingUp className={`h-5 w-5 text-[${Colors.primary}]`} />
                Monthly Revenue Trend
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Revenue performance over time
              </p>
            </CardHeader>
            <CardContent>
              <ChartContainer config={chartConfig} className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={revenueData.monthlyRevenue}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis tickFormatter={(value) => formatCurrency(value)} />
                    <ChartTooltip
                      formatter={(value) => [
                        formatCurrency(value as number),
                        "Revenue",
                      ]}
                    />
                    <Area
                      type="monotone"
                      dataKey="value"
                      stroke="#8884d8"
                      fill="#8884d8"
                      fillOpacity={0.3}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </ChartContainer>
            </CardContent>
          </Card>
        </motion.div>

        {/* Top Customers */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Card className={`border border-[${Colors.primary}]/10 bg-[${Colors.background}]/50 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300`}>
            <CardHeader>
              <CardTitle className="text-xl font-bold flex items-center gap-2">
                <Users className={`h-5 w-5 text-[${Colors.primary}]`} />
                Top Customers
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Highest revenue generating customers
              </p>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {revenueData.topCustomers.slice(0, 5).map((customer, index) => (
                  <div
                    key={customer.id}
                    className="flex items-center justify-between p-3 rounded-lg bg-muted/50"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                        <span className="text-sm font-medium">{index + 1}</span>
                      </div>
                      <div>
                        <p className="font-medium">{customer.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {customer.invoiceCount} invoices
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-green-600">
                        {formatCurrency(customer.totalRevenue)}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        Avg: {formatCurrency(customer.averageInvoiceValue)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Revenue by Category */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Card className={`border border-[${Colors.primary}]/10 bg-[${Colors.background}]/50 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300`}>
            <CardHeader>
              <CardTitle className="text-xl font-bold flex items-center gap-2">
                <DollarSign className={`h-5 w-5 text-[${Colors.primary}]`} />
                Revenue by Category
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Revenue distribution across categories
              </p>
            </CardHeader>
            <CardContent>
              {revenueData.revenueByCategory.length > 0 ? (
                <div className="space-y-4">
                  <ChartContainer config={{}} className="h-[200px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={revenueData.revenueByCategory}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" />
                        <YAxis
                          tickFormatter={(value) => formatCurrency(value)}
                        />
                        <ChartTooltip
                          formatter={(value) => [
                            formatCurrency(value as number),
                            "Revenue",
                          ]}
                        />
                        <Bar dataKey="value" fill="#8884d8">
                          {revenueData.revenueByCategory.map((_, index) => (
                            <Cell
                              key={`cell-${index}`}
                              fill={colors[index % colors.length]}
                            />
                          ))}
                        </Bar>
                      </BarChart>
                    </ResponsiveContainer>
                  </ChartContainer>

                  <div className="space-y-2">
                    {revenueData.revenueByCategory
                      .slice(0, 5)
                      .map((category, index) => (
                        <div
                          key={category.id}
                          className="flex items-center justify-between"
                        >
                          <div className="flex items-center space-x-2">
                            <div
                              className="w-3 h-3 rounded-full"
                              style={{
                                backgroundColor: colors[index % colors.length],
                              }}
                            />
                            <span className="text-sm font-medium">
                              {category.name}
                            </span>
                          </div>
                          <div className="text-right">
                            <div className="text-sm font-bold">
                              {formatCurrency(category.value)}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {category.percentage.toFixed(1)}%
                            </div>
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  No category data available
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}
