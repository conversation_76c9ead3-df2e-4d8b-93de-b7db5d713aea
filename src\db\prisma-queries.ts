import 'server-only';
import db from './db';
import { ArtifactKind } from '@/components/ai-agent/artifact';
import { Message } from '@prisma/client';
import { InputJsonValue } from '@prisma/client/runtime/library';
// User queries
export async function getUser(email: string) {
  try {
    return await db.user.findUnique({
      where: { email }
    });
  } catch (error) {
    console.error('Failed to get user from database');
    throw error;
  }
}

// Chat queries
export async function getChatById({ id }: { id: string }) {
  try {
    return await db.chat.findUnique({
      where: { id }
    });
  } catch (error) {
    console.error('Failed to get chat by id from database');
    throw error;
  }
}

export async function getChatsByUserId({ userId }: { userId: string }) {
  try {
    return await db.chat.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' }
    });
  } catch (error) {
    console.error('Failed to get chats by user id from database');
    throw error;
  }
}

export async function getPublicChats() {
  try {
    return await db.chat.findMany({
      where: { visibility: 'public' },
      orderBy: { createdAt: 'desc' }
    });
  } catch (error) {
    console.error('Failed to get public chats from database');
    throw error;
  }
}

export async function createChat({ userId, messages, title }: { userId: string; messages: Message[]; title: string }) {
  try {
    const chat = await db.chat.create({
      data: {
        userId,
        title,
        createdAt: new Date()
      }
    });

    // Create messages
    if (messages && messages.length > 0) {
      await db.message.createMany({
        data: messages.map((message: Message) => ({
          chatId: chat.id,
          role: message.role,
          content: message.content as InputJsonValue,
          createdAt: new Date(message.createdAt || Date.now())
        }))
      });
    }

    return chat;
  } catch (error) {
    console.error('Failed to create chat in database');
    throw error;
  }
}

export async function updateChatVisiblityById({ id, visibility }: { id: string; visibility: string }) {
  try {
    return await db.chat.update({
      where: { id },
      data: { visibility }
    });
  } catch (error) {
    console.error('Failed to update chat visibility in database');
    throw error;
  }
}

export async function deleteChatById({ id }: { id: string }) {
  try {
    return await db.chat.delete({
      where: { id }
    });
  } catch (error) {
    console.error('Failed to delete chat from database');
    throw error;
  }
}

// Message queries
export async function getMessagesByChatId({ chatId }: { chatId: string }) {
  try {
    return await db.message.findMany({
      where: { chatId },
      orderBy: { createdAt: 'asc' }
    });
  } catch (error) {
    console.error('Failed to get messages by chat id from database');
    throw error;
  }
}

export async function getMessageById({ id }: { id: string }) {
  try {
    return await db.message.findUnique({
      where: { id }
    });
  } catch (error) {
    console.error('Failed to get message by id from database');
    throw error;
  }
}

export async function createMessage({ chatId, role, content }: { chatId: string; role: string; content: string }) {
  try {
    return await db.message.create({
      data: {
        chatId,
        role,
        content,
        createdAt: new Date()
      }
    });
  } catch (error) {
    console.error('Failed to create message in database');
    throw error;
  }
}

export async function deleteMessagesByChatIdAfterTimestamp({ chatId, timestamp }: { chatId: string; timestamp: Date }) {
  try {
    return await db.message.deleteMany({
      where: {
        chatId,
        createdAt: {
          gt: timestamp
        }
      }
    });
  } catch (error) {
    console.error('Failed to delete messages by chat id after timestamp from database');
    throw error;
  }
}

// Vote queries
export async function getVotesByChatId({ id }: { id: string }) {
  try {
    return await db.vote.findMany({
      where: { chatId: id }
    });
  } catch (error) {
    console.error('Failed to get votes by chat id from database');
    throw error;
  }
}

// Document queries
export async function saveDocument({
  id,
  title,
  kind,
  content,
  userId,
}: {
  id: string;
  title: string;
  kind: ArtifactKind;
  content: string;
  userId: string;
}) {
  try {
    return await db.document.create({
      data: {
        id,
        title,
        kind,
        content,
        userId,
        createdAt: new Date(),
      }
    });
  } catch (error) {
    console.error('Failed to save document in database');
    throw error;
  }
}
