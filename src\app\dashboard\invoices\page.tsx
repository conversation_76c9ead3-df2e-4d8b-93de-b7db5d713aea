import { Suspense } from "react";
import type { Metadata } from "next";
import { InvoiceDataTable } from "@/components/dashboard/pages/invoices/invoice-data-table";
import { InvoiceTableSkeleton } from "@/components/dashboard/pages/invoices/invoice-table-skeleton";
import { InvoiceFilters } from "@/components/dashboard/pages/invoices/invoice-filters";
import DashboardLayout from "@/components/layout/DashboardLayout";
import { getInvoices } from "@/actions/invoice-actions";

export const metadata: Metadata = {
  title: "Invoices | Dashboard",
  description: "Manage your invoices",
};

export default async function InvoicesPage(
  props: {
    searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
  }
) {
  const searchParams = await props.searchParams;
  // Extract filter parameters
  const page =
    typeof searchParams.page === "string"
      ? Number.parseInt(searchParams.page)
      : 1;
  const limit =
    typeof searchParams.limit === "string"
      ? Number.parseInt(searchParams.limit)
      : 10;
  const status =
    typeof searchParams.status === "string" ? searchParams.status : undefined;
  const vendor =
    typeof searchParams.vendor === "string" ? searchParams.vendor : undefined;
  const category =
    typeof searchParams.category === "string"
      ? searchParams.category
      : undefined;
  const dateRange =
    typeof searchParams.dateRange === "string"
      ? searchParams.dateRange
      : undefined;
  const sort =
    typeof searchParams.sort === "string" ? searchParams.sort : "createdAt";
  const order =
    typeof searchParams.order === "string" ? searchParams.order : "desc";
  const search =
    typeof searchParams.search === "string" ? searchParams.search : "";

  return (
    <DashboardLayout>
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Invoices</h1>
          <p className="text-muted-foreground">
            Manage and track all your invoices in one place
          </p>
        </div>
      </div>

      <InvoiceFilters />

      <Suspense fallback={<InvoiceTableSkeleton />}>
        <InvoiceDataTable
          getInvoices={getInvoices}
          page={page}
          limit={limit}
          status={status}
          vendor={vendor}
          category={category}
          dateRange={dateRange}
          sort={sort}
          order={order}
          search={search}
        />
      </Suspense>
    </DashboardLayout>
  );
}
