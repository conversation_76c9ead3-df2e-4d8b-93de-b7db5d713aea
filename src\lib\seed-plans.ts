import db from '@/db/db';

// Plan data that matches the pricing page
const paddlePlans = [
  // Starter Plan - Monthly
  {
    productId: 1,
    productName: 'Starter',
    variantId: 101, // Unique integer ID
    paddlePriceId: 'pri_01jz83p3v1rs5r3xskt9z4ww50',
    name: 'Starter',
    description:
      'Perfect for small businesses processing fewer than 100 invoices per month.',
    price: '2900', // $29.00 in cents
    interval: 'month',
    intervalCount: 1,
    sort: 1,
  },
  // Starter Plan - Yearly
  {
    productId: 1,
    productName: 'Starter',
    variantId: 102, // Unique integer ID
    paddlePriceId: 'pri_01jz83xxfn40khba0cvdw42t1x',
    name: 'Starter (Yearly)',
    description:
      'Perfect for small businesses processing fewer than 100 invoices per month.',
    price: '29000', // $290.00 in cents
    interval: 'year',
    intervalCount: 1,
    sort: 2,
  },
  // Business Plan - Monthly
  {
    productId: 2,
    productName: 'Business',
    variantId: 201, // Unique integer ID
    paddlePriceId: 'pri_01jz83q8503fhrqh3gwsqa23zb',
    name: 'Business',
    description:
      'Ideal for growing businesses with advanced analytics needs and higher volume.',
    price: '7900', // $79.00 in cents
    interval: 'month',
    intervalCount: 1,
    sort: 3,
  },
  // Business Plan - Yearly
  {
    productId: 2,
    productName: 'Business',
    variantId: 202, // Unique integer ID
    paddlePriceId: 'pri_01jz83yvxaamh0t9zymtykbqcz',
    name: 'Business (Yearly)',
    description:
      'Ideal for growing businesses with advanced analytics needs and higher volume.',
    price: '79000', // $790.00 in cents
    interval: 'year',
    intervalCount: 1,
    sort: 4,
  },
  // Enterprise Plan - Monthly
  {
    productId: 3,
    productName: 'Enterprise',
    variantId: 301, // Unique integer ID
    paddlePriceId: 'pri_01jz83r7fw92rb1pb9a3qt7ty3',
    name: 'Enterprise',
    description:
      'Comprehensive solution for large organizations with custom requirements.',
    price: '19900', // $199.00 in cents
    interval: 'month',
    intervalCount: 1,
    sort: 5,
  },
  // Enterprise Plan - Yearly
  {
    productId: 3,
    productName: 'Enterprise',
    variantId: 302, // Unique integer ID
    paddlePriceId: 'pri_01jz83zhw7065b2dzyhcbx77h2',
    name: 'Enterprise (Yearly)',
    description:
      'Comprehensive solution for large organizations with custom requirements.',
    price: '199000', // $1990.00 in cents
    interval: 'year',
    intervalCount: 1,
    sort: 6,
  },
];

export async function seedPaddlePlans() {
  console.log('🌱 Seeding Paddle plans...');

  try {
    // First, let's check if plans already exist
    const existingPlans = await db.plan.findMany();
    console.log(`Found ${existingPlans.length} existing plans`);

    for (const planData of paddlePlans) {
      // Check if plan with this paddlePriceId already exists
      const existingPlan = await db.plan.findFirst({
        where: { paddlePriceId: planData.paddlePriceId },
      });

      if (existingPlan) {
        // Update existing plan with correct data
        await db.plan.update({
          where: { id: existingPlan.id },
          data: {
            productId: planData.productId,
            productName: planData.productName,
            variantId: planData.variantId,
            paddlePriceId: planData.paddlePriceId,
            name: planData.name,
            description: planData.description,
            price: planData.price,
            interval: planData.interval,
            intervalCount: planData.intervalCount,
            sort: planData.sort,
          },
        });
        console.log(
          `✅ Updated plan: ${planData.name} (${planData.paddlePriceId})`
        );
      } else {
        // Create new plan
        await db.plan.create({
          data: {
            productId: planData.productId,
            productName: planData.productName,
            variantId: planData.variantId,
            paddlePriceId: planData.paddlePriceId,
            name: planData.name,
            description: planData.description,
            price: planData.price,
            interval: planData.interval,
            intervalCount: planData.intervalCount,
            sort: planData.sort,
          },
        });
        console.log(
          `✅ Created plan: ${planData.name} (${planData.paddlePriceId})`
        );
      }
    }

    console.log('🎉 Successfully seeded all Paddle plans!');

    // Return summary
    const finalPlans = await db.plan.findMany({
      orderBy: { sort: 'asc' },
    });

    console.log('\n📊 Final plans in database:');
    finalPlans.forEach((plan) => {
      console.log(
        `  - ${plan.name}: $${(parseInt(plan.price) / 100).toFixed(2)}/${plan.interval} (${plan.variantId})`
      );
    });

    return { success: true, plansCreated: paddlePlans.length };
  } catch (error) {
    console.error('❌ Error seeding plans:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

// Function to run the seeding if this file is executed directly
if (require.main === module) {
  seedPaddlePlans()
    .then((result) => {
      console.log('Seeding result:', result);
      process.exit(0);
    })
    .catch((error) => {
      console.error('Seeding failed:', error);
      process.exit(1);
    });
}
