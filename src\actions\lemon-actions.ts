"use server";

import crypto from "node:crypto";
import {
  cancelSubscription,
  createCheckout,
  createWebhook,
  getProduct,
  getSubscription,
  listPrices,
  listProducts,
  listWebhooks,
  updateSubscription,
  type Variant,
} from "@lemonsqueezy/lemonsqueezy.js";
import { revalidatePath } from "next/cache";
import { notFound } from "next/navigation";
import db from "@/db/db";
import { configureLemonSqueezy } from "@/lib/lemonsqueezy";
import type { Prisma } from "@prisma/client";
import { auth, currentUser } from "@clerk/nextjs/server";
import { WebhookPayload } from "@/lib/typeguards";

// Add this interface near the top of the file
interface Plan {
  id: number;
  name: string;
  description: string | null;
  price: string;
  interval: string | null;
  intervalCount: number | null;
  isUsageBased: boolean | null;
  productId: number;
  productName: string | null;
  variantId: number;
  trialInterval: string | null;
  trialIntervalCount: number | null;
  sort: number | null;
}

// Add these interfaces near the top, after the Plan interface

// Represents the structure of the 'data' object in Lemon Squeezy webhooks
interface LemonSqueezyData {
  id?: string;
  type: string;
  attributes: Record<string, unknown>;
  relationships?: Record<string, unknown>;
  links?: Record<string, string>;
}

// Represents the Plan model structure from Prisma
interface DbPlan {
  id: number;
  productId: number;
  productName: string | null;
  variantId: number;
  name: string;
  description: string | null;
  price: string;
  isUsageBased: boolean | null;
  interval: string | null;
  intervalCount: number | null;
  trialInterval: string | null;
  trialIntervalCount: number | null;
  sort: number | null;
}

/**
 * This action will log out the current user.
 */
export async function logout() {
  const { userId } = await auth();
  if (!userId) {
    throw new Error("User is not authenticated.");
  }
  // Note: Sign out should be handled client-side
  return { success: true };
}

/**
 * This action will create a checkout on Lemon Squeezy.
 */
export async function getCheckoutURL(variantId: number, embed = false) {
  configureLemonSqueezy();

  const { userId } = await auth();

  if (!userId) {
    throw new Error("User is not authenticated.");
  }

  const user = await currentUser();
  // Find the user's internal database ID (not the Clerk ID)
  const dbUser = await db.user.findUnique({
    where: { clerkId: userId }
  });

  if (!dbUser) {
    console.error(`User with clerkId ${userId} not found in database`);
    throw new Error("User not found in database");
  }

  const checkout = await createCheckout(
    process.env.LEMONSQUEEZY_STORE_ID!,
    variantId,
    {
      checkoutOptions: {
        embed,
        media: false,
        logo: !embed,
      },
      checkoutData: {
        email: user?.emailAddresses[0]?.emailAddress ?? undefined,
        custom: {
          user_id: dbUser.id,     // This is the internal database ID
          clerk_id: userId,       // This is the Clerk ID
        },
      },
      productOptions: {
        enabledVariants: [variantId],
        redirectUrl: `${process.env.NEXT_PUBLIC_APP_URL}/confirmation`,
        receiptButtonText: "Go to Dashboard",
        receiptThankYouNote: "Thank you for signing up to Lemon Stand!",
      },
    },
  );

  return checkout.data?.data.attributes.url;
}

/**
 * This action will check if a webhook exists on Lemon Squeezy. It will return
 * the webhook if it exists, otherwise it will return undefined.
 * @param shouldRevalidate Whether to revalidate the path after checking for webhook
 */
export async function hasWebhook(shouldRevalidate = false) {
  configureLemonSqueezy();

  if (!process.env.WEBHOOK_URL) {
    console.error("❌ Missing required WEBHOOK_URL env variable");
    throw new Error(
      "Missing required WEBHOOK_URL env variable. Please set it in your .env file."
    );
  }

  // Check if a webhook exists on Lemon Squeezy.
  const allWebhooks = await listWebhooks({
    filter: { storeId: process.env.LEMONSQUEEZY_STORE_ID },
  });

  // Check if WEBHOOK_URL ends with a slash. If not, add it.
  let webhookUrl = process.env.WEBHOOK_URL;
  // Remove trailing slash if present
  if (webhookUrl.endsWith("/")) {
    webhookUrl = webhookUrl.slice(0, -1);
  }
  // Add /api/webhook
  const fullWebhookUrl = `${webhookUrl}/api/webhook`;
  
  const webhook = allWebhooks.data?.data.find(
    (wh) => wh.attributes.url === fullWebhookUrl
  );

  if (shouldRevalidate) {
    revalidatePath("/");
  }

  return webhook;
}

/**
 * This action will set up a webhook on Lemon Squeezy to listen to
 * Subscription events. It will only set up the webhook if it does not exist.
 * @param shouldRevalidate Whether to revalidate the path after setting up webhook
 */
export async function setupWebhook(shouldRevalidate = false) {
  try {
    console.log("🔄 Setting up Lemon Squeezy webhook...");
    configureLemonSqueezy();

    if (!process.env.WEBHOOK_URL) {
      console.error("❌ Missing required WEBHOOK_URL env variable");
      throw new Error(
        "Missing required WEBHOOK_URL env variable. Please set it in your .env file."
      );
    }

    if (!process.env.LEMONSQUEEZY_WEBHOOK_SECRET) {
      console.error("❌ Missing required LEMONSQUEEZY_WEBHOOK_SECRET env variable");
      throw new Error(
        "Missing required LEMONSQUEEZY_WEBHOOK_SECRET env variable. Please set it in your .env file."
      );
    }

    // Check if WEBHOOK_URL ends with a slash. If not, add it.
    let webhookUrl = process.env.WEBHOOK_URL;
    // Remove trailing slash if present
    if (webhookUrl.endsWith("/")) {
      webhookUrl = webhookUrl.slice(0, -1);
    }
    
    // Add /api/webhook
    const fullWebhookUrl = `${webhookUrl}/api/webhook`;
    
    console.log(`🔗 Will create webhook with URL: ${fullWebhookUrl}`);

    // Do not set a webhook on Lemon Squeezy if it already exists.
    let webhook = await hasWebhook(false);
    
    if (webhook) {
      console.log(`ℹ️ Webhook already exists with ID: ${webhook.id}`);
      return webhook;
    }

    // If the webhook does not exist, create it.
    try {
      console.log(`🔧 Creating new webhook for store: ${process.env.LEMONSQUEEZY_STORE_ID}`);
      console.log(`🔧 Using webhook secret: ${process.env.LEMONSQUEEZY_WEBHOOK_SECRET.substring(0, 5)}...`);
      
      const newWebhook = await createWebhook(process.env.LEMONSQUEEZY_STORE_ID!, {
        secret: process.env.LEMONSQUEEZY_WEBHOOK_SECRET!,
        url: fullWebhookUrl,
        testMode: true, // will create a webhook in Test mode only!
        events: [
          "subscription_created",
          "subscription_updated",
          "subscription_cancelled", // Use correct spelling
          "subscription_resumed",
          "subscription_expired",
          "subscription_paused",
          "subscription_unpaused",
          "subscription_payment_success",
          "subscription_payment_failed",
          "subscription_payment_recovered",
          "order_created",
          "order_refunded",
        ],
      });

      if (newWebhook.error) {
        console.error("❌ Error creating webhook:", newWebhook.error);
        throw new Error(`Failed to create webhook: ${newWebhook.error.message}`);
      }

      webhook = newWebhook.data?.data;
      console.log(`✅ Successfully created webhook with ID: ${webhook?.id}`);
      console.log(`📌 Webhook URL: ${fullWebhookUrl}`);
      console.log(`📌 Webhook events: ${newWebhook.data?.data.attributes.events.join(", ")}`);
    } catch (error) {
      console.error("❌ Error creating webhook:", error);
      throw error;
    }

    if (shouldRevalidate) {
      revalidatePath("/");
    }
    
    return webhook;
  } catch (error) {
    console.error("❌ Error in setupWebhook:", error);
    throw error;
  }
}

/**
 * This action will sync the product variants from Lemon Squeezy with the
 * Plans database model. It will only sync the 'subscription' variants.
 * @param shouldRevalidate Whether to revalidate the path after syncing plans
 */
export async function syncPlans(shouldRevalidate = false) {
  try {
    configureLemonSqueezy();

    // No need to clear existing plans; upsert will handle updates and new plans
    
    // Initialize empty array for product variants
    const productVariants: Plan[] = [];

    // For reference, fetch any existing plans (should be empty now)
    const existingPlans = await db.plan.findMany();

    // Helper function to add a variant to the productVariants array and sync it with the database.
    async function _addVariant(variant: {
      id: number;
      name: string;
      description: string | null;
      price: string;
      interval: string | null;
      intervalCount: number | null;
      isUsageBased: boolean | null;
      productId: number;
      productName: string | null;
      variantId: number;
      trialInterval: string | null;
      trialIntervalCount: number | null;
      sort: number | null;
    }) {
      try {
        // Sync the variant with the plan in the database.
        await db.plan.upsert({
          where: { variantId: variant.variantId },
          create: variant,
          update: variant,
        });

        productVariants.push(variant);
      } catch (error) {
        console.error(`Error upserting plan ${variant.name}:`, error);
      }
    }

    // Fetch products from the Lemon Squeezy store.
    const products = await listProducts({
      filter: { storeId: process.env.LEMONSQUEEZY_STORE_ID },
      include: ["variants"],
    });

    // Loop through all the variants.
    const allVariants = products.data?.included as Variant["data"][] | undefined;

    // for...of supports asynchronous operations, unlike forEach.
    if (allVariants && allVariants.length > 0) {
      for (const v of allVariants) {
        const variant = v.attributes;

        // Skip draft variants only
        if (variant.status === "draft") {
          continue;
        }

        // Fetch the Product name.
        const productResponse = await getProduct(variant.product_id);
        const productName = productResponse.data?.data.attributes.name ?? "";

        // Fetch the Price object.
        const variantPriceObject = await listPrices({
          filter: {
            variantId: v.id,
          },
        });

        const currentPriceObj = variantPriceObject.data?.data.at(0);
        
        if (!currentPriceObj) {
          continue;
        }
        
        const isUsageBased = currentPriceObj.attributes.usage_aggregation !== null;
        const interval = currentPriceObj.attributes.renewal_interval_unit ?? null;
        const intervalCount = currentPriceObj.attributes.renewal_interval_quantity ?? null;
        const trialInterval = currentPriceObj.attributes.trial_interval_unit ?? null;
        const trialIntervalCount = currentPriceObj.attributes.trial_interval_quantity ?? null;

        const price = isUsageBased
          ? currentPriceObj.attributes.unit_price_decimal
          : currentPriceObj.attributes.unit_price;

        const priceString = price !== null ? (price?.toString() ?? "") : "";

        const isSubscription = currentPriceObj.attributes.category === "subscription";

        // If not a subscription, skip it.
        if (!isSubscription) {
          continue;
        }

        await _addVariant({
          id: parseInt(v.id) as unknown as number,
          name: variant.name,
          description: variant.description,
          price: priceString,
          interval: interval?.toString() ?? null,
          intervalCount,
          isUsageBased,
          productId: variant.product_id,
          productName,
          variantId: parseInt(v.id) as unknown as number,
          trialInterval: trialInterval?.toString() ?? null,
          trialIntervalCount,
          sort: variant.sort,
        });
      }
    }

    if (shouldRevalidate) {
      revalidatePath("/");
    }

    return productVariants;
  } catch (error) {
    console.error("Error in syncPlans:", error);
    // Return an empty array instead of failing
    return [];
  }
}

/**
 * This action will store a webhook event in the database.
 * @param eventName - The name of the event.
 * @param body - The body of the event.
 */
export async function storeWebhookEvent(
  eventName: string,
  body: Prisma.InputJsonValue,
) {
  if (!process.env.DATABASE_URL) {
    console.error("❌ Database URL is not set. Cannot store webhook event.");
    throw new Error("DATABASE_URL is not set");
  }

  const id = crypto.randomInt(100000000, 1000000000);

  try {
    const returnedValue = await db.webhookEvent.create({
      data: {
        id,
        eventName,
        processed: false,
        body,
      },
    });
    return returnedValue;
  } catch (error) {
    console.error("❌ Error storing webhook event to database:", { 
      id, 
      eventName, 
      error: String(error) 
    });
    throw error; // Re-throw the error to be caught higher up if necessary
  }
}

/**
 * This action will process a webhook event in the database.
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export async function processWebhookEvent(webhookEvent: { id: number; body: Prisma.JsonValue; eventName: string }, payload: any = undefined) {
  let processingError: string | null = null;

  try {
    configureLemonSqueezy();
    
    // We trust the stored body more than the potentially modified payload
    const eventBody = webhookEvent.body as unknown as WebhookPayload;

    // Validation
    const hasValidMeta = eventBody && typeof eventBody === 'object' && eventBody.meta && typeof eventBody.meta === 'object' && 'event_name' in eventBody.meta;
    const hasValidData = eventBody && typeof eventBody === 'object' && eventBody.data && typeof eventBody.data === 'object' && 'attributes' in eventBody.data;

    if (!hasValidMeta || !hasValidData) {
      processingError = "Event body missing 'meta' or 'data' property or has invalid structure.";
    } else {
      // Processing Logic
      try {
        const data: LemonSqueezyData = eventBody.data as LemonSqueezyData;

        const relevantEvents = [
          "subscription_created", 
          "subscription_updated",
          "order_created", 
          "subscription_payment_success"
          // Add other events if they should modify the subscription record
        ];

        if (relevantEvents.includes(webhookEvent.eventName)) {
          const result = await processSubscriptionData(data, webhookEvent.eventName, payload || eventBody);
        } else {
        }

      } catch (innerProcessingErr) {
        processingError = `Error during specific event processing: ${String(innerProcessingErr)}`;
      }
    }

    // Update Webhook Event Record Status
    try {
        await db.webhookEvent.update({
            where: { id: webhookEvent.id },
            data: {
                processed: true,
                processingError: processingError,
            },
        });
    } catch (updateErr) {
        // Don't overwrite original processing error if update fails
        processingError = processingError ?? `Failed to update webhook status: ${String(updateErr)}`;
    }

    // Revalidation (always attempt)
    try {
      revalidatePath("/dashboard/subscription");
    } catch (revalidateErr) {
    }

  } catch (outerError) {
    // Catch errors like configureLemonSqueezy, initial DB connection, etc.
    processingError = `Outer error processing webhook: ${String(outerError)}`;
    // Attempt to update the record even if an outer error occurred
    try {
      await db.webhookEvent.update({
        where: { id: webhookEvent.id },
        data: { processed: true, processingError },
      });
    } catch (updateErr) {
    }
  }
}

/**
 * Helper function to process subscription data from webhooks
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
async function processSubscriptionData(data: LemonSqueezyData, eventName: string, payload: any = undefined) {
  if (!data.id) {
    throw new Error("Subscription data is missing required ID.");
  }

  // --- User ID Extraction --- 
  let userId: string | null = null;
  let clerkId: string | null = null;
  
  if (payload && typeof payload === 'object' && payload.meta?.custom_data) {
    const customData = payload.meta.custom_data as Record<string, unknown>;
    if (customData.user_id) userId = String(customData.user_id);
    if (customData.clerk_id) clerkId = String(customData.clerk_id);
  }

  if ((!userId || !clerkId) && data.attributes && data.attributes.custom_data) {
    const attrCustomData = data.attributes.custom_data as Record<string, unknown>;
    if (!userId && attrCustomData.user_id) userId = String(attrCustomData.user_id);
    if (!clerkId && attrCustomData.clerk_id) clerkId = String(attrCustomData.clerk_id);
  }

  // --- Database User Lookup --- 
  if (clerkId) {
    const user = await db.user.findUnique({ where: { clerkId } });
    if (user) {
      userId = user.id;
    }
  }

  if (!userId && data.attributes?.user_email) {
    const userEmail = String(data.attributes.user_email);
    const user = await db.user.findFirst({ where: { email: userEmail } });
    if (user) {
      userId = user.id;
    }
  }

  if (!userId) {
    throw new Error("Could not determine user ID from subscription data");
  }

  // --- Plan Lookup --- 
  let variantId: string | number | null | undefined = data.attributes?.variant_id as string | number | null | undefined;
  
  // Special handling for order_created - variant_id might be in first_order_item
  if (!variantId && eventName === 'order_created' && data.attributes?.first_order_item) {
    const firstOrderItem = data.attributes.first_order_item as Record<string, unknown>;
    // Use casting via unknown to satisfy the linter
    if (firstOrderItem.variant_id) {
      variantId = firstOrderItem.variant_id as unknown as string | number;
    }
  }

  if (!variantId) {
    throw new Error("Variant ID not found in subscription/order data");
  }

  let plan: DbPlan | null = null;
  try {
    // Safely convert variantId to Number for the query
    const numericVariantId = Number(variantId);
    if (isNaN(numericVariantId)) {
      throw new Error(`Invalid variantId format: ${variantId}`);
    }
    
    plan = await db.plan.findUnique({ where: { variantId: numericVariantId } });
    
    if (plan) {
    } else {
      const availablePlans = await db.plan.findMany({ select: { id: true, variantId: true, name: true } });
      
      await syncPlans(true); // Attempt sync
      plan = await db.plan.findUnique({ where: { variantId: numericVariantId } }); // Retry find
      
      if (plan) {
      } else {
        throw new Error(`Plan not found for variant ID: ${numericVariantId}`);
      }
    }
  } catch (dbError) {
    throw dbError;
  }

  // --- Create/Update Subscription --- 
  try {
    const subscription = await createOrUpdateSubscription(data, userId, plan, eventName);
    return subscription;
  } catch (error) {
    throw error; // Propagate error
  }
}

/**
 * Helper function to create or update a subscription in the database
 */
async function createOrUpdateSubscription(
  subscriptionData: LemonSqueezyData, 
  userId: string, 
  plan: DbPlan,
  eventName: string
) {
  if (!subscriptionData.id) {
    throw new Error("Subscription data is missing required ID for upsert.");
  }
  
  try {
    const attributes = subscriptionData.attributes;
    
    // Extract subscription item details if available
    let subItemId = 0;
    let isUsageBased = false;
    
    if (attributes.first_subscription_item && 
        typeof attributes.first_subscription_item === 'object') {
      
      // Safely access subscription item properties
      const subItem = attributes.first_subscription_item as Record<string, unknown>;
      
      if ('id' in subItem) {
        subItemId = Number(subItem.id || 0);
      }
      
      if ('is_usage_based' in subItem) {
        isUsageBased = Boolean(subItem.is_usage_based);
      }
    }
    
    // Get status correctly
    const status = attributes.status || "unknown";
    
    // Create subscription data object for database
    const updateData = {
      lemonSqueezyId: String(subscriptionData.id || ''),
      orderId: Number(attributes.order_id || 0),
      name: String(attributes.user_name || "Unknown"),
      email: String(attributes.user_email || "<EMAIL>"),
      status: String(status),
      statusFormatted: String(attributes.status_formatted || formatStatus(String(status))),
      renewsAt: attributes.renews_at ? String(attributes.renews_at) : null,
      endsAt: attributes.ends_at ? String(attributes.ends_at) : null,
      trialEndsAt: attributes.trial_ends_at ? String(attributes.trial_ends_at) : null,
      price: plan.price, // Use the plan price as default
      isPaused: Boolean(attributes.pause),
      subscriptionItemId: subItemId,
      isUsageBased: isUsageBased,
      userId: String(userId),
      planId: plan.id,
    };
    
    // Create/update subscription in the database
    try {
      const upsertedSub = await db.subscription.upsert({
        where: { lemonSqueezyId: updateData.lemonSqueezyId },
        create: updateData,
        update: updateData,
      });
      
      // Force a revalidation to update the UI
      revalidatePath("/dashboard/subscription");
      revalidatePath("/confirmation");
      return upsertedSub;
    } catch (dbError) {
      throw dbError; // Re-throw the error
    }
  } catch (error) {
    throw error; // Re-throw the error
  }
}

/**
 * Helper function to format status
 */
function formatStatus(status: string): string {
  switch (status.toLowerCase()) {
    case "active":
      return "Active";
    case "cancelled":
      return "Cancelled";
    case "expired":
      return "Expired";
    case "trialing":
      return "Trial";
    case "past_due":
      return "Past Due";
    case "unpaid":
      return "Unpaid";
    case "paused":
      return "Paused";
    default:
      return status.charAt(0).toUpperCase() + status.slice(1);
  }
}

/**
 * This action will get the user's subscriptions from the database.
 * @param shouldRevalidate Whether to revalidate the path after fetching subscriptions
 */
export async function getUserSubscriptions(shouldRevalidate = false) {
  const { userId } = await auth();

  if (!userId) {
    notFound();
  }

  const user = await db.user.findUnique({
    where: { clerkId: userId }
  });

  if (!user) {
    return [];
  }

  const userSubscriptions = await db.subscription.findMany({
    where: { userId: user.id },
  });

  if (shouldRevalidate) {
    revalidatePath("/dashboard/subscription");
  }

  return userSubscriptions;
}

/**
 * This action will get the subscription URLs (update_payment_method and
 * customer_portal) for the given subscription ID.
 */
export async function getSubscriptionURLs(id: string) {
  configureLemonSqueezy();
  const subscription = await getSubscription(id);

  if (subscription.error) {
    throw new Error(subscription.error.message);
  }

  revalidatePath("/dashboard/subscription");

  return subscription.data.data.attributes.urls;
}

/**
 * This action will cancel a subscription on Lemon Squeezy.
 */
export async function cancelSub(id: string) {
  configureLemonSqueezy();

  // Get user subscriptions
  const userSubscriptions = await getUserSubscriptions();

  // Check if the subscription exists
  const subscription = userSubscriptions.find(
    (sub) => sub.lemonSqueezyId === id,
  );

  if (!subscription) {
    throw new Error(`Subscription with ID ${id} not found.`);
  }

  // Cancel the subscription on Lemon Squeezy.
  const response = await cancelSubscription(id);

  if (response.error) {
    throw new Error(response.error.message);
  }

  // Update the subscription in the database.
  await db.subscription.update({
    where: { lemonSqueezyId: id },
    data: {
      status: "cancelled",
      statusFormatted: "Cancelled",
    },
  });

  revalidatePath("/dashboard/subscription");

  return response.data.data;
}

/**
 * This action will pause a user's subscription.
 */
export async function pauseUserSubscription(id: string) {
  configureLemonSqueezy();

  // Get user subscriptions
  const userSubscriptions = await getUserSubscriptions();

  // Check if the subscription exists
  const subscription = userSubscriptions.find(
    (sub) => sub.lemonSqueezyId === id,
  );

  if (!subscription) {
    throw new Error(`Subscription with ID ${id} not found.`);
  }

  // Pause the subscription on Lemon Squeezy for 1 month.
  const response = await updateSubscription(id, {
    pause: {
      mode: "void", // Don't bill while paused
      resumesAt: new Date(
        new Date().setMonth(new Date().getMonth() + 1),
      ).toISOString(), // Resume in 1 month
    },
  });

  if (response.error) {
    throw new Error(response.error.message);
  }

  // Update the subscription in the database.
  await db.subscription.update({
    where: { lemonSqueezyId: id },
    data: {
      isPaused: true,
    },
  });

  revalidatePath("/dashboard/subscription");

  return response.data.data;
}

/**
 * This action will unpause a user's subscription.
 */
export async function unpauseUserSubscription(id: string) {
  configureLemonSqueezy();

  // Get user subscriptions
  const userSubscriptions = await getUserSubscriptions();

  // Check if the subscription exists
  const subscription = userSubscriptions.find(
    (sub) => sub.lemonSqueezyId === id,
  );

  if (!subscription) {
    throw new Error(`Subscription with ID ${id} not found.`);
  }

  // Check if the subscription is paused
  if (!subscription.isPaused) {
    throw new Error(`Subscription with ID ${id} is not paused.`);
  }

  // Unpause the subscription on Lemon Squeezy.
  const response = await updateSubscription(id, {
    pause: null, // Remove the pause
  });

  if (response.error) {
    throw new Error(response.error.message);
  }

  // Update the subscription in the database.
  await db.subscription.update({
    where: { lemonSqueezyId: id },
    data: {
      isPaused: false,
    },
  });

  revalidatePath("/dashboard/subscription");

  return response.data.data;
}

/**
 * This action will change a user's subscription plan.
 */
export async function changePlan(currentPlanId: number, newPlanId: number) {
  configureLemonSqueezy();

  const { userId } = await auth();

  if (!userId) {
    throw new Error("User is not authenticated.");
  }

  // Get user subscriptions
  const userSubscriptions = await getUserSubscriptions();

  // Check if the user has an active subscription with the current plan
  const subscription = userSubscriptions.find(
    (sub) => sub.planId === currentPlanId && sub.status === "active",
  );

  if (!subscription) {
    throw new Error(
      `Active subscription with plan ID ${currentPlanId} not found.`,
    );
  }

  // Get the variant ID for the new plan
  const newPlan = await db.plan.findUnique({
    where: { id: newPlanId },
  });

  if (!newPlan) {
    throw new Error(`Plan with ID ${newPlanId} not found.`);
  }

  // Change the subscription's plan on Lemon Squeezy
  try {
    // First, get the subscription details to ensure we have the latest data
    const subscriptionData = await getSubscription(subscription.lemonSqueezyId);
    
    if (subscriptionData.error) {
      throw new Error(`Failed to fetch subscription: ${subscriptionData.error.message}`);
    }
    
    // Update the subscription with the new variant ID from the new plan
    const response = await updateSubscription(subscription.lemonSqueezyId, {
      variantId: newPlan.variantId,
    });

    if (response.error) {
      throw new Error(`Failed to update subscription: ${response.error.message}`);
    }
    
    // Update the subscription in our database
    await db.subscription.update({
      where: { lemonSqueezyId: subscription.lemonSqueezyId },
      data: {
        planId: newPlan.id,
        price: newPlan.price,
      },
    });

    return {
      success: true,
      subscription: response.data?.data,
      plan: newPlan
    };
  } catch (error) {
    throw error;
  }
}
