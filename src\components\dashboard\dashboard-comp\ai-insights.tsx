import type React from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON>bulb, TrendingUp, <PERSON><PERSON><PERSON>riangle, DollarSign } from "lucide-react"

interface Insight {
  id: string
  type: "tip" | "alert" | "trend" | "saving"
  icon: React.ReactNode
  title: string
  description: string
}

const insights: Insight[] = [
  {
    id: "insight-1",
    type: "trend",
    icon: <TrendingUp className="h-5 w-5 text-[#00b2ff]" />,
    title: "Spending Increase Detected",
    description:
      "Your spending on office supplies has increased by 23% compared to last quarter. Consider reviewing your procurement process.",
  },
  {
    id: "insight-2",
    type: "alert",
    icon: <AlertTriangle className="h-5 w-5 text-[#00f6ff]" />,
    title: "Duplicate Invoice Alert",
    description:
      "We've detected a potential duplicate invoice from TechSolutions (INV-002 and INV-012). Please verify before processing payment.",
  },
  {
    id: "insight-3",
    type: "saving",
    icon: <DollarSign className="h-5 w-5 text-[#00ff7e]" />,
    title: "Potential Savings Opportunity",
    description:
      "Based on your invoice history, consolidating orders from Acme Corp could save approximately $450 per month.",
  },
  {
    id: "insight-4",
    type: "tip",
    icon: <Lightbulb className="h-5 w-5 text-[#6a00ff]" />,
    title: "Payment Optimization",
    description:
      "Scheduling payments for the 25th of each month could improve your cash flow by an estimated 12% based on your current revenue patterns.",
  },
]

export function AIInsights() {
  return (
    <div className="grid gap-4 md:grid-cols-2">
      {insights.map((insight) => (
        <Card
          key={insight.id}
          className="dashboard-insight-card"
          style={{
            borderLeftColor:
              insight.type === "trend"
                ? "#00b2ff"
                : insight.type === "alert"
                  ? "#00f6ff"
                  : insight.type === "saving"
                    ? "#00ff7e"
                    : "#6a00ff",
          }}
        >
          <CardContent className="p-4">
            <div className="flex items-start gap-4">
              <div className="dashboard-insight-icon">{insight.icon}</div>
              <div>
                <h4 className="font-semibold">{insight.title}</h4>
                <p className="text-sm text-gray-400 mt-1">{insight.description}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
