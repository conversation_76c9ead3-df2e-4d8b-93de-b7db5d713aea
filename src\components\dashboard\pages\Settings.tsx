'use client';

import DashboardLayout from '@/components/layout/DashboardLayout';
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Ta<PERSON>List,
  TabsTrigger,
} from '@/components/ui/tabs';
import { useUser } from '@clerk/nextjs';

// Import tab components
import SecurityTab from '@/components/dashboard/settings/SecurityTab';
import AdvancedTab from '@/components/dashboard/settings/AdvancedTab';

interface ClerkUser {
  id: string;
  primaryEmailAddress?: { emailAddress: string } | null;
  firstName: string | null;
  lastName: string | null;
  imageUrl: string;
}

const adaptToAdvancedTabUser = (
  user: ClerkUser | null | undefined
) => {
  if (!user) return user;
  return {
    id: user.id,
    clerkId: user.id,
    email: user.primaryEmailAddress?.emailAddress || '',
    firstName: user.firstName,
    lastName: user.lastName,
    profileImageUrl: user.imageUrl,
    createdAt: new Date(),
    updatedAt: new Date(),
    role: 'USER' as const,
  };
};

const Settings = () => {
  const { user } = useUser();

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">
            Settings
          </h1>
          <p className="text-muted-foreground">
            Manage your account settings and preferences.
          </p>
        </div>

        <Tabs defaultValue="account" className="space-y-4">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="account">Account</TabsTrigger>
            <TabsTrigger value="advanced">Advanced</TabsTrigger>
          </TabsList>

          <TabsContent value="account">
            <SecurityTab />
          </TabsContent>

          <TabsContent value="advanced">
            <AdvancedTab user={adaptToAdvancedTabUser(user)} />
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default Settings;
