"use server";

import { auth, currentUser } from "@clerk/nextjs/server";
import db from "@/db/db";
import { UserRole } from "@prisma/client";
import { revalidatePath } from "next/cache";
import { generateToken } from "@/lib/utils";
import { sendTeamInviteEmail } from "@/lib/email/send-email";

// Get current organization and validate user has admin access
async function getCurrentOrgAndValidateAdmin() {
  const { userId } = await auth();
  if (!userId) {
    throw new Error("Unauthorized");
  }

  const user = await db.user.findUnique({
    where: { clerkId: userId },
    include: { organizations: true },
  });

  if (!user) {
    throw new Error("User not found");
  }

  if (user.organizations.length === 0) {
    throw new Error("No organization found");
  }

  // Currently we're supporting one organization per user
  const organization = user.organizations[0];

  // Check if user has admin role
  if (user.role !== "ADMIN") {
    throw new Error("Only admins can perform this action");
  }

  return { user, organization };
}

// Get all members of current organization
export async function getTeamMembers() {
  try {
    const { userId } = await auth();
    if (!userId) {
      return { error: "Unauthorized" };
    }

    const user = await db.user.findUnique({
      where: { clerkId: userId },
      include: { organizations: true },
    });

    if (!user || user.organizations.length === 0) {
      return { error: "No organization found" };
    }

    const organizationId = user.organizations[0].id;

    // Get all members of the organization
    const members = await db.user.findMany({
      where: {
        organizations: {
          some: {
            id: organizationId,
          },
        },
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        profileImageUrl: true,
        role: true,
        lastActive: true,
        status: true,
        createdAt: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return { members };
  } catch (error) {
    console.error("Error getting team members:", error);
    return { error: "Failed to get team members" };
  }
}

// Get all pending invites for the current organization
export async function getPendingInvites() {
  try {
    const { userId } = await auth();
    if (!userId) {
      return { error: "Unauthorized" };
    }

    const user = await db.user.findUnique({
      where: { clerkId: userId },
      include: { organizations: true },
    });

    if (!user || user.organizations.length === 0) {
      return { error: "No organization found" };
    }

    const organizationId = user.organizations[0].id;

    // Get all pending invites for the organization
    const invites = await db.teamInvite.findMany({
      where: {
        organizationId,
        status: "PENDING",
      },
      include: {
        invitedBy: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Format invites
    const formattedInvites = invites.map((invite) => ({
      id: invite.id,
      email: invite.email,
      role: invite.role,
      invitedBy: `${invite.invitedBy.firstName || ""} ${invite.invitedBy.lastName || ""}`.trim() || invite.invitedBy.email,
      invitedOn: invite.createdAt.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }),
      expiresAt: invite.expiresAt,
    }));

    return { invites: formattedInvites };
  } catch (error) {
    console.error("Error getting pending invites:", error);
    return { error: "Failed to get pending invites" };
  }
}

// Invite a new team member
export async function inviteTeamMember(email: string, role: UserRole) {
  try {
    // Validate and get current organization
    const { user, organization } = await getCurrentOrgAndValidateAdmin();

    // Check if user is already a member of the organization
    const existingUser = await db.user.findUnique({
      where: { email },
      include: {
        organizations: {
          where: { id: organization.id },
        },
      },
    });

    if (existingUser && existingUser.organizations.length > 0) {
      return { error: "User is already a member of this organization" };
    }

    // Check if there's already a pending invite for this email
    const existingInvite = await db.teamInvite.findFirst({
      where: {
        email,
        organizationId: organization.id,
        status: "PENDING",
      },
    });

    if (existingInvite) {
      return { error: "An invitation has already been sent to this email" };
    }

    // Create a new invite with a token that expires in 7 days
    const token = await generateToken();
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7);

    const invite = await db.teamInvite.create({
      data: {
        email,
        role,
        token,
        expiresAt,
        organization: { connect: { id: organization.id } },
        invitedBy: { connect: { id: user.id } },
        status: "PENDING",
      },
    });

    // Send invitation email
    await sendTeamInviteEmail({
      email,
      inviterName: `${user.firstName || ""} ${user.lastName || ""}`.trim() || user.email,
      organizationName: organization.name,
      role: role,
      token: token,
      expiresAt: expiresAt,
    });

    revalidatePath("/dashboard/team");
    return { success: true, invite };
  } catch (error) {
    console.error("Error inviting team member:", error);
    return { error: "Failed to invite team member" };
  }
}

// Resend an invitation
export async function resendInvite(inviteId: string) {
  try {
    // Validate and get current organization
    const { user, organization } = await getCurrentOrgAndValidateAdmin();

    // Get the invite
    const invite = await db.teamInvite.findUnique({
      where: {
        id: inviteId,
        organizationId: organization.id,
      },
    });

    if (!invite) {
      return { error: "Invite not found" };
    }

    if (invite.status !== "PENDING") {
      return { error: "Invite is no longer pending" };
    }

    // Update the expiration date
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7);

    const updatedInvite = await db.teamInvite.update({
      where: { id: inviteId },
      data: {
        expiresAt,
        updatedAt: new Date(),
      },
    });

    // Resend invitation email
    await sendTeamInviteEmail({
      email: invite.email,
      inviterName: `${user.firstName || ""} ${user.lastName || ""}`.trim() || user.email,
      organizationName: organization.name,
      role: invite.role,
      token: invite.token,
      expiresAt: expiresAt,
    });

    revalidatePath("/dashboard/team");
    return { success: true, invite: updatedInvite };
  } catch (error) {
    console.error("Error resending invite:", error);
    return { error: "Failed to resend invite" };
  }
}

// Cancel an invitation
export async function cancelInvite(inviteId: string) {
  try {
    // Validate and get current organization
    const { organization } = await getCurrentOrgAndValidateAdmin();

    // Check if invite exists and belongs to the organization
    const invite = await db.teamInvite.findUnique({
      where: {
        id: inviteId,
        organizationId: organization.id,
      },
    });

    if (!invite) {
      return { error: "Invite not found" };
    }

    // Delete the invite
    await db.teamInvite.delete({
      where: { id: inviteId },
    });

    revalidatePath("/dashboard/team");
    return { success: true };
  } catch (error) {
    console.error("Error canceling invite:", error);
    return { error: "Failed to cancel invite" };
  }
}

// Update team member role
export async function updateMemberRole(memberId: string, role: UserRole) {
  try {
    // Validate and get current organization
    const { user, organization } = await getCurrentOrgAndValidateAdmin();

    // Don't allow changing own role
    if (user.id === memberId) {
      return { error: "You cannot change your own role" };
    }

    // Check if member is part of the organization
    const memberToUpdate = await db.user.findFirst({
      where: {
        id: memberId,
        organizations: {
          some: {
            id: organization.id,
          },
        },
      },
    });

    if (!memberToUpdate) {
      return { error: "Member not found in this organization" };
    }

    // Update the member's role
    const updatedMember = await db.user.update({
      where: { id: memberId },
      data: { role },
    });

    revalidatePath("/dashboard/team");
    return { success: true, member: updatedMember };
  } catch (error) {
    console.error("Error updating member role:", error);
    return { error: "Failed to update member role" };
  }
}

// Remove a team member
export async function removeMember(memberId: string) {
  try {
    // Validate and get current organization
    const { user, organization } = await getCurrentOrgAndValidateAdmin();

    // Don't allow removing self
    if (user.id === memberId) {
      return { error: "You cannot remove yourself from the organization" };
    }

    // Get the member to be removed
    const memberToRemove = await db.user.findFirst({
      where: {
        id: memberId,
        organizations: {
          some: {
            id: organization.id,
          },
        },
      },
    });

    if (!memberToRemove) {
      return { error: "Member not found in this organization" };
    }

    // Remove the organization from the member's organizations
    await db.user.update({
      where: { id: memberId },
      data: {
        organizations: {
          disconnect: { id: organization.id },
        },
      },
    });

    revalidatePath("/dashboard/team");
    return { success: true };
  } catch (error) {
    console.error("Error removing member:", error);
    return { error: "Failed to remove member" };
  }
}

// Accept an invitation (used when a user clicks the link in the email)
export async function acceptInvite(token: string) {
  try {
    // Get current user from Clerk
    const user = await currentUser();
    if (!user) {
      return { error: "Unauthorized" };
    }

    // Find the invite by token
    const invite = await db.teamInvite.findUnique({
      where: { token },
      include: { organization: true },
    });

    if (!invite) {
      return { error: "Invalid or expired invitation" };
    }

    if (invite.status !== "PENDING") {
      return { error: "Invitation has already been used or revoked" };
    }

    if (invite.expiresAt < new Date()) {
      await db.teamInvite.update({
        where: { id: invite.id },
        data: { status: "EXPIRED" },
      });
      return { error: "Invitation has expired" };
    }

    // Make sure we have a user record in our database
    const { syncUserWithDatabase } = await import('./user');
    const dbUser = await syncUserWithDatabase();

    if (!dbUser) {
      return { error: "Failed to find or create user in database" };
    }

    // Make sure email matches
    if (user.emailAddresses[0]?.emailAddress.toLowerCase() !== invite.email.toLowerCase()) {
      return { error: "Email mismatch. Please use the email that received the invitation." };
    }
    
    // Add user to organization with the specified role
    await db.user.update({
      where: { id: dbUser.id },
      data: {
        role: invite.role,
        organizations: {
          connect: { id: invite.organizationId },
        },
      },
    });

    // Mark invite as accepted
    await db.teamInvite.update({
      where: { id: invite.id },
      data: { status: "ACCEPTED" },
    });

    revalidatePath("/dashboard/team");
    return { 
      success: true, 
      organizationName: invite.organization.name,
      role: invite.role
    };
  } catch (error) {
    console.error("Error accepting invite:", error);
    return { error: "Failed to accept invite" };
  }
}

// Update user active status
export async function updateUserActiveStatus() {
  try {
    const { userId } = await auth();
    if (!userId) return;

    const user = await db.user.findUnique({
      where: { clerkId: userId },
    });

    if (!user) return;

    // Update last active timestamp
    await db.user.update({
      where: { id: user.id },
      data: { lastActive: new Date() },
    });
  } catch (error) {
    console.error("Error updating user active status:", error);
  }
}

// Get team usage statistics
export async function getTeamStatistics() {
  try {
    const { userId } = await auth();
    if (!userId) {
      return { error: "Unauthorized" };
    }

    const user = await db.user.findUnique({
      where: { clerkId: userId },
      include: { organizations: true },
    });

    if (!user || user.organizations.length === 0) {
      return { error: "No organization found" };
    }

    const organizationId = user.organizations[0].id;

    // Get all members count
    const totalMembers = await db.user.count({
      where: {
        organizations: {
          some: { id: organizationId },
        },
      },
    });

    // Get active members count (active in the last 24 hours)
    const oneDayAgo = new Date();
    oneDayAgo.setDate(oneDayAgo.getDate() - 1);
    
    const activeMembers = await db.user.count({
      where: {
        organizations: {
          some: { id: organizationId },
        },
        lastActive: {
          gte: oneDayAgo,
        },
      },
    });

    // Get pending invites count
    const pendingInvites = await db.teamInvite.count({
      where: {
        organizationId,
        status: "PENDING",
      },
    });

    // Get role distribution
    const roleDistribution = await db.user.groupBy({
      by: ['role'],
      where: {
        organizations: {
          some: { id: organizationId },
        },
      },
      _count: {
        role: true,
      },
    });

    const statistics = {
      totalMembers,
      activeMembers,
      pendingInvites,
      roleDistribution: roleDistribution.reduce((acc, curr) => {
        acc[curr.role] = curr._count.role;
        return acc;
      }, {} as Record<string, number>),
    };

    return { statistics };
  } catch (error) {
    console.error("Error getting team statistics:", error);
    return { error: "Failed to get team statistics" };
  }
} 