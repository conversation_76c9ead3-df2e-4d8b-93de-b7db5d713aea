const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function updatePlanLimits() {
  console.log('🔧 Updating plans with usage limits...');

  try {
    // Define the usage limits for each plan tier
    const planUpdates = [
      // Starter Plans
      {
        productName: 'Starter',
        chatLimit: 5,
        invoiceLimit: 10,
      },
      // Business Plans
      {
        productName: 'Business',
        chatLimit: 50,
        invoiceLimit: 100,
      },
      // Enterprise Plans
      {
        productName: 'Enterprise',
        chatLimit: 500,
        invoiceLimit: 1000,
      },
    ];

    let updatedCount = 0;

    for (const update of planUpdates) {
      console.log(`\n📝 Updating ${update.productName} plans...`);

      const result = await prisma.plan.updateMany({
        where: {
          productName: {
            contains: update.productName,
            mode: 'insensitive',
          },
        },
        data: {
          chatLimit: update.chatLimit,
          invoiceLimit: update.invoiceLimit,
        },
      });

      console.log(
        `✅ Updated ${result.count} ${update.productName} plans`
      );
      updatedCount += result.count;
    }

    console.log(
      `\n🎉 Successfully updated ${updatedCount} plans with usage limits`
    );

    // Check final state
    const allPlans = await prisma.plan.findMany({
      select: {
        id: true,
        productName: true,
        name: true,
        chatLimit: true,
        invoiceLimit: true,
        price: true,
      },
    });

    console.log('\n📋 Final plan state:');
    allPlans.forEach((plan) => {
      console.log(
        `  ${plan.productName} (${plan.name}): ${plan.chatLimit} chats, ${plan.invoiceLimit} invoices - $${plan.price}`
      );
    });

    return { success: true, updated: updatedCount };
  } catch (error) {
    console.error('❌ Error updating plan limits:', error);
    return { success: false, error: error.message };
  } finally {
    await prisma.$disconnect();
  }
}

// Run the update
updatePlanLimits()
  .then((result) => {
    if (result.success) {
      console.log('\n✅ Plan limits update completed successfully!');
      process.exit(0);
    } else {
      console.error('\n❌ Plan limits update failed:', result.error);
      process.exit(1);
    }
  })
  .catch((error) => {
    console.error('\n💥 Unexpected error:', error);
    process.exit(1);
  });
