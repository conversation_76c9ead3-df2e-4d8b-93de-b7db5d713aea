import db from '@/db/db';

async function fixSubscriptionPlans() {
  console.log('🔧 Fixing subscription plan associations...');

  try {
    // Find subscriptions using the placeholder plan
    const placeholderPlan = await db.plan.findFirst({
      where: { name: { contains: 'Paddle Plan undefined' } },
    });

    if (!placeholderPlan) {
      console.log('No placeholder plan found');
      return { success: true, updated: 0 };
    }

    const subscriptions = await db.subscription.findMany({
      where: { planId: placeholderPlan.id },
      include: { user: true },
    });

    console.log(
      `Found ${subscriptions.length} subscriptions using placeholder plan`
    );

    let updatedCount = 0;

    for (const subscription of subscriptions) {
      console.log(`\n📋 Subscription ${subscription.id}:`);
      console.log(`  - User: ${subscription.user.email}`);
      console.log(`  - Status: ${subscription.status}`);
      console.log(
        `  - Paddle Subscription ID: ${subscription.paddleSubscriptionId}`
      );
      console.log(
        `  - Paddle Price ID: ${subscription.paddlePriceId}`
      );
      console.log(
        `  - Current Price: $${(parseInt(subscription.price) / 100).toFixed(2)}`
      );

      // If subscription has a Paddle price ID, try to match it to a real plan
      if (subscription.paddlePriceId) {
        const correctPlan = await db.plan.findFirst({
          where: { paddlePriceId: subscription.paddlePriceId },
        });

        if (correctPlan) {
          await db.subscription.update({
            where: { id: subscription.id },
            data: { planId: correctPlan.id },
          });
          console.log(
            `  ✅ Updated to use plan: ${correctPlan.name}`
          );
          updatedCount++;
        } else {
          console.log(
            `  ⚠️ No matching plan found for price ID: ${subscription.paddlePriceId}`
          );
        }
      } else {
        // If no Paddle price ID, try to guess based on price
        const price = parseInt(subscription.price);
        let matchingPlan = null;

        // Try to match by price
        const allPlans = await db.plan.findMany({
          where: { paddlePriceId: { not: null } },
        });

        for (const plan of allPlans) {
          if (parseInt(plan.price) === price) {
            matchingPlan = plan;
            break;
          }
        }

        if (matchingPlan) {
          await db.subscription.update({
            where: { id: subscription.id },
            data: {
              planId: matchingPlan.id,
              paddlePriceId: matchingPlan.paddlePriceId, // Also set the price ID for future reference
            },
          });
          console.log(
            `  ✅ Updated to use plan: ${matchingPlan.name} (matched by price)`
          );
          updatedCount++;
        } else {
          console.log(
            `  ⚠️ Could not determine correct plan for this subscription`
          );
          console.log(
            `  💡 You may need to manually update this subscription`
          );
        }
      }
    }

    // Try to delete the placeholder plan again
    const remainingSubscriptions = await db.subscription.count({
      where: { planId: placeholderPlan.id },
    });

    if (remainingSubscriptions === 0) {
      await db.plan.delete({
        where: { id: placeholderPlan.id },
      });
      console.log(
        `\n🗑️ Deleted placeholder plan: ${placeholderPlan.name}`
      );
    } else {
      console.log(
        `\n⚠️ Still ${remainingSubscriptions} subscriptions using placeholder plan`
      );
    }

    console.log(`\n🎉 Updated ${updatedCount} subscriptions!`);

    return { success: true, updated: updatedCount };
  } catch (error) {
    console.error('❌ Error fixing subscription plans:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

// Function to run the fix if this file is executed directly
if (require.main === module) {
  fixSubscriptionPlans()
    .then((result) => {
      console.log('Fix result:', result);
      process.exit(0);
    })
    .catch((error) => {
      console.error('Fix failed:', error);
      process.exit(1);
    });
}
