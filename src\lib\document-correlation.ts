import { prisma } from "@/lib/prisma";
import { InvoiceData } from "@/types/invoice";
import { 
  DocumentCorrelation, 
  RelatedDocument, 
  EnhancedInvoice 
} from "@/types/enhanced-invoice";
import { InputJsonValue } from "@prisma/client/runtime/library";

/**
 * Find related documents for an invoice
 */
export async function findRelatedDocuments(
  invoiceData: InvoiceData,
  userId: string
): Promise<RelatedDocument[]> {
  try {
    const relatedDocs: RelatedDocument[] = [];
    
    // Extract key information for matching
    const vendorName = invoiceData.vendor?.name;
    const invoiceNumber = invoiceData.invoiceNumber;
    const invoiceAmount = invoiceData.financials?.total 
      ? parseFloat(invoiceData.financials.total) 
      : undefined;
    
    if (!vendorName) return [];
    
    // Look for purchase orders with similar information
    // Note: This assumes you have a PurchaseOrder model in your database
    // If not, you would need to adapt this to your data model
    const purchaseOrders = await prisma.invoice.findMany({
      where: {
        userId,
        vendorName,
        invoiceType: 'PURCHASE_ORDER',
        // If we have an invoice number, try to match on that
        ...(invoiceNumber && {
          OR: [
            { invoiceNumber: { contains: invoiceNumber } },
            { extractedData: { path: ['invoiceNumber'], string_contains: invoiceNumber } }
          ]
        })
      },
      take: 5
    });
    
    // Convert purchase orders to related documents
    for (const po of purchaseOrders) {
      const extractedData = po.extractedData as unknown as InvoiceData;
      
      relatedDocs.push({
        id: po.id,
        type: 'PURCHASE_ORDER',
        documentNumber: po.invoiceNumber || extractedData?.invoiceNumber || 'Unknown',
        fileUrl: po.originalFileUrl || undefined,
        date: po.issueDate || undefined,
        amount: po.amount || undefined,
        currency: po.currency || undefined
      });
    }
    
    // Look for receipts with similar information
    const receipts = await prisma.invoice.findMany({
      where: {
        userId,
        vendorName,
        invoiceType: 'RECEIPT',
        // If we have an amount, try to match on that
        ...(invoiceAmount && {
          amount: {
            gte: invoiceAmount * 0.95, // Allow 5% variance
            lte: invoiceAmount * 1.05
          }
        })
      },
      take: 5
    });
    
    // Convert receipts to related documents
    for (const receipt of receipts) {
      const extractedData = receipt.extractedData as unknown as InvoiceData;
      
      relatedDocs.push({
        id: receipt.id,
        type: 'RECEIPT',
        documentNumber: receipt.invoiceNumber || extractedData?.invoiceNumber || 'Unknown',
        fileUrl: receipt.originalFileUrl || undefined,
        date: receipt.issueDate || undefined,
        amount: receipt.amount || undefined,
        currency: receipt.currency || undefined
      });
    }
    
    return relatedDocs;
  } catch (error) {
    console.error('Error finding related documents:', error);
    return [];
  }
}

/**
 * Correlate an invoice with its related documents
 */
export async function correlateDocuments(
  invoiceData: EnhancedInvoice,
  relatedDocs: RelatedDocument[]
): Promise<DocumentCorrelation> {
  // Initialize correlation result
  const correlation: DocumentCorrelation = {
    primaryDocument: invoiceData,
    relatedDocuments: relatedDocs,
    discrepancies: [],
    correlationScore: 0
  };
  
  // If no related documents, return early
  if (relatedDocs.length === 0) {
    correlation.correlationScore = 1; // Perfect score if no related docs to compare
    return correlation;
  }
  
  // Extract key information from invoice
  const invoiceAmount = invoiceData.financials?.total 
    ? parseFloat(invoiceData.financials.total) 
    : undefined;
  
  const invoiceDate = invoiceData.date 
    ? new Date(invoiceData.date) 
    : undefined;
  
  // Check each related document for discrepancies
  for (const doc of relatedDocs) {
    // Compare amounts
    if (invoiceAmount && doc.amount) {
      const amountDiff = Math.abs(invoiceAmount - doc.amount);
      const percentDiff = amountDiff / doc.amount;
      
      if (percentDiff > 0.01) { // More than 1% difference
        correlation.discrepancies?.push({
          field: 'amount',
          primaryValue: invoiceAmount,
          relatedValue: doc.amount,
          documentId: doc.id,
          severity: percentDiff > 0.1 ? 'HIGH' : percentDiff > 0.05 ? 'MEDIUM' : 'LOW'
        });
      }
    }
    
    // Compare dates
    if (invoiceDate && doc.date) {
      const daysDiff = Math.abs(
        (invoiceDate.getTime() - doc.date.getTime()) / (1000 * 60 * 60 * 24)
      );
      
      if (daysDiff > 30) { // More than 30 days difference
        correlation.discrepancies?.push({
          field: 'date',
          primaryValue: invoiceDate,
          relatedValue: doc.date,
          documentId: doc.id,
          severity: daysDiff > 90 ? 'HIGH' : daysDiff > 60 ? 'MEDIUM' : 'LOW'
        });
      }
    }
    
    // Add more comparisons as needed
  }
  
  // Calculate correlation score (0-1, higher is better)
  if (correlation.discrepancies && correlation.discrepancies.length > 0) {
    const severityWeights = {
      'LOW': 0.1,
      'MEDIUM': 0.3,
      'HIGH': 0.6
    };
    
    const totalWeight = correlation.discrepancies.reduce(
      (sum, discrepancy) => sum + severityWeights[discrepancy.severity], 
      0
    );
    
    correlation.correlationScore = Math.max(0, 1 - (totalWeight / relatedDocs.length));
  } else {
    correlation.correlationScore = 1; // Perfect score if no discrepancies
  }
  
  return correlation;
}

/**
 * Store related documents for an invoice
 */
export async function storeRelatedDocuments(
  invoiceId: string,
  relatedDocs: RelatedDocument[]
): Promise<void> {
  try {
    if (relatedDocs.length === 0) return;
    
    // Update the invoice with related documents
    await prisma.invoice.update({
      where: { id: invoiceId },
      data: {
        relatedDocuments: relatedDocs as unknown as InputJsonValue
      }
    });
  } catch (error) {
    console.error('Error storing related documents:', error);
  }
}
