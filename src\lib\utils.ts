import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"
import { randomBytes } from "crypto"
import { type SubscriptionStatusType } from "./types";
import type {
  CoreAssistantMessage,
  CoreToolMessage,
  Message,
  ToolInvocation,
} from 'ai';
import type { Message as DBMessage, Document } from '@prisma/client';
import { JsonValue } from "@prisma/client/runtime/library";

/**
 * A utility for constructing className strings conditionally.
 * Combines clsx and tailwind-merge for optimal class handling
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Helper function to convert full currency names to ISO codes
 */
export function getCurrencyCode(currency: string = 'USD'): string {
  // Map of common full currency names to ISO codes
  const currencyMap: Record<string, string> = {
    'United States Dollar': 'USD',
    'US Dollar': 'USD',
    'Dollar': 'USD',
    'Euro': 'EUR',
    'British Pound': 'GBP',
    'Pound Sterling': 'GBP',
    'Japanese Yen': 'JPY',
    'Canadian Dollar': 'CAD',
    'Australian Dollar': 'AUD',
    'Swiss Franc': 'CHF',
    'Chinese Yuan': 'CNY',
    'Indian Rupee': 'INR',
    'Brazilian Real': 'BRL',
    'Mexican Peso': 'MXN',
    'South African Rand': 'ZAR',
  };

  // Map of currency symbols to ISO codes
  const symbolToCode: Record<string, string> = {
    '$': 'USD',
    '€': 'EUR',
    '£': 'GBP',
    '¥': 'JPY',
    '₹': 'INR',
    '₽': 'RUB',
    '₩': 'KRW',
    '₪': 'ILS', // Israeli Shekel
    '₺': 'TRY', // Turkish Lira
    '₴': 'UAH', // Ukrainian Hryvnia
    '₦': 'NGN', // Nigerian Naira
    '₱': 'PHP', // Philippine Peso
    '฿': 'THB', // Thai Baht
    '₫': 'VND', // Vietnamese Dong
    '₲': 'PYG', // Paraguayan Guarani
    '₡': 'CRC', // Costa Rican Colón
    '₼': 'AZN', // Azerbaijani Manat
    '₾': 'GEL', // Georgian Lari
  };

  // If it's null or undefined, return USD
  if (!currency) return 'USD';

  // If it's a symbol, convert to code
  if (currency.length === 1 && symbolToCode[currency]) {
    return symbolToCode[currency];
  }

  // If the currency is already a valid 3-letter code, return it
  if (currency.length === 3 && /^[A-Z]{3}$/.test(currency.toUpperCase())) {
    return currency.toUpperCase();
  }

  // Try to find the currency in the map
  const code = currencyMap[currency];

  // Return the code if found, otherwise default to USD
  return code || 'USD';
}

/**
 * Formats a number as currency
 * @param amount The amount to format
 * @param currency The currency code or name (defaults to USD)
 */
export function formatCurrency(amount: number, currency: string = 'USD'): string {
  // Get the ISO currency code
  const currencyCode = getCurrencyCode(currency);

  try {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currencyCode,
      minimumFractionDigits: 2
    }).format(amount);
  } catch (error) {
    // Fallback to basic formatting if Intl.NumberFormat fails
    return `${currencyCode} ${amount.toFixed(2)}`;
  }
}

/**
 * Format a date to a readable string
 */
export function formatDate(date: Date | string): string {
  if (!date) return '';

  const dateObj = typeof date === 'string' ? new Date(date) : date;

  // Check if date is valid
  if (isNaN(dateObj.getTime())) return '';

  return new Intl.DateTimeFormat('en-US', {
    month: 'long',
    day: 'numeric',
    year: 'numeric',
  }).format(dateObj);
}

export function truncateText(text: string, maxLength: number): string {
  if (!text || text.length <= maxLength) return text;
  return `${text.substring(0, maxLength)}...`;
}

export function getInitials(name: string): string {
  if (!name) return ""

  const parts = name.split(" ")
  if (parts.length === 1) return parts[0].charAt(0).toUpperCase()

  return (parts[0].charAt(0) + parts[parts.length - 1].charAt(0)).toUpperCase()
}

export function generateTagsFromText(text: string): string[] {
  // This is a simple example - in a real app, you'd want more sophisticated logic,
  // possibly involving AI for better tag extraction
  if (!text) return [];

  // Extract potential tag words (excluding common words)
  const commonWords = new Set([
    'the', 'and', 'or', 'a', 'an', 'in', 'on', 'at', 'to', 'for', 'with',
    'by', 'about', 'as', 'of', 'from', 'invoice', 'document', 'receipt',
  ]);

  // Split by non-alphanumeric characters, filter, and take unique values
  return [...new Set(
    text.toLowerCase()
      .split(/[^a-z0-9]/)
      .filter(word => word.length > 3 && !commonWords.has(word))
      .slice(0, 5) // Limit to 5 tags
  )];
}

// Generate a secure random token for team invites
export async function generateToken(length = 32): Promise<string> {
  return new Promise((resolve, reject) => {
    randomBytes(length, (err, buffer) => {
      if (err) reject(err);
      resolve(buffer.toString('hex'));
    });
  });
}

// Format time elapsed (e.g. "2 hours ago", "Yesterday", etc.)
export function formatTimeElapsed(date: Date): string {
  const now = new Date();
  const diff = now.getTime() - date.getTime();

  // Convert to seconds, minutes, hours, days
  const seconds = Math.floor(diff / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (seconds < 60) {
    return "Just now";
  } else if (minutes < 60) {
    return `${minutes} ${minutes === 1 ? 'minute' : 'minutes'} ago`;
  } else if (hours < 24) {
    return `${hours} ${hours === 1 ? 'hour' : 'hours'} ago`;
  } else if (days === 1) {
    return "Yesterday";
  } else if (days < 7) {
    return `${days} days ago`;
  } else {
    return formatDate(date);
  }
}

export function checkRequiredEnv() {
  if (!process.env.LEMONSQUEEZY_API_KEY) {
    throw new Error("Missing LEMONSQUEEZY_API_KEY. Set it in your .env file.");
  }

  if (!process.env.LEMONSQUEEZY_STORE_ID) {
    throw new Error("Missing LEMONSQUEEZY_STORE_ID. Set it in your .env file.");
  }

  if (!process.env.LEMONSQUEEZY_STORE_ID) {
    throw new Error("Missing LEMONSQUEEZY_API_KEY. Set it in your .env file.");
  }
}

/**
 * Check if a subscription is valid based on its status
 */
export function isValidSubscription(status: SubscriptionStatusType): boolean {
  return status !== "cancelled" && status !== "expired" && status !== "unpaid";
}

export function takeUniqueOrThrow<T extends unknown[]>(values: T): T[number] {
  if (values.length !== 1)
    throw new Error("Found non unique or inexistent value");
  return values[0];
}

export function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0;
    const v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

interface ApplicationError extends Error {
  info: string;
  status: number;
}

export const fetcher = async (url: string) => {
  const res = await fetch(url);

  if (!res.ok) {
    const error = new Error(
      'An error occurred while fetching the data.',
    ) as ApplicationError;

    error.info = await res.json();
    error.status = res.status;

    throw error;
  }

  return res.json();
};


export function getLocalStorage(key: string) {
  if (typeof window !== 'undefined') {
    return JSON.parse(localStorage.getItem(key) || '[]');
  }
  return [];
}

function addToolMessageToChat({
  toolMessage,
  messages,
}: {
  toolMessage: CoreToolMessage;
  messages: Array<Message>;
}): Array<Message> {
  return messages.map((message) => {
    if (message.toolInvocations) {
      return {
        ...message,
        toolInvocations: message.toolInvocations.map((toolInvocation) => {
          const toolResult = toolMessage.content.find(
            (tool) => tool.toolCallId === toolInvocation.toolCallId,
          );

          if (toolResult) {
            return {
              ...toolInvocation,
              state: 'result',
              result: toolResult.result,
            };
          }

          return toolInvocation;
        }),
      };
    }

    return message;
  });
}

export function convertToUIMessages(
  messages: Array<DBMessage>,
): Array<Message> {
  return messages.reduce((chatMessages: Array<Message>, message) => {
    if (message.role === 'tool') {
      return addToolMessageToChat({
        toolMessage: message as unknown as CoreToolMessage,
        messages: chatMessages,
      });
    }

    let textContent = '';
    let reasoning: string | undefined = undefined;
    const toolInvocations: Array<ToolInvocation> = [];

    if (typeof message.content === 'string') {
      textContent = message.content;
    } else if (Array.isArray(message.content)) {
      for (const content of message.content) {
        if (content && typeof content === 'object' && 'type' in content && content.type === 'text') {
          textContent += content.text;
        } else if (content && typeof content === 'object' && 'type' in content && content.type === 'tool-call') {
          toolInvocations.push({
            state: 'call',
            toolCallId: content.toolCallId as string,
            toolName: content.toolName as string,
            args: content.args as JsonValue,
          });
        } else if (content && typeof content === 'object' && 'type' in content && content.type === 'reasoning') {
          reasoning = content.reasoning as string;
        }
      }
    }

    chatMessages.push({
      id: message.id,
      role: message.role as Message['role'],
      content: textContent,
      reasoning,
      toolInvocations,
    });

    return chatMessages;
  }, []);
}

type ResponseMessageWithoutId = CoreToolMessage | CoreAssistantMessage;
type ResponseMessage = ResponseMessageWithoutId & { id: string };

export function sanitizeResponseMessages({
  messages,
  reasoning,
}: {
  messages: Array<ResponseMessage>;
  reasoning: string | undefined;
}) {
  const toolResultIds: Array<string> = [];

  for (const message of messages) {
    if (message.role === 'tool') {
      for (const content of message.content) {
        if (content.type === 'tool-result') {
          toolResultIds.push(content.toolCallId);
        }
      }
    }
  }

  const messagesBySanitizedContent = messages.map((message) => {
    if (message.role !== 'assistant') return message;

    if (typeof message.content === 'string') return message;

    const sanitizedContent = message.content.filter((content) =>
      content.type === 'tool-call'
        ? toolResultIds.includes(content.toolCallId)
        : content.type === 'text'
          ? content.text.length > 0
          : true,
    );

    if (reasoning) {
      // @ts-expect-error: reasoning message parts in sdk is wip
      sanitizedContent.push({ type: 'reasoning', reasoning });
    }

    return {
      ...message,
      content: sanitizedContent,
    };
  });

  return messagesBySanitizedContent.filter(
    (message) => message.content.length > 0,
  );
}

export function sanitizeUIMessages(messages: Array<Message>): Array<Message> {
  const messagesBySanitizedToolInvocations = messages.map((message) => {
    if (message.role !== 'assistant') return message;

    if (!message.toolInvocations) return message;

    const toolResultIds: Array<string> = [];

    for (const toolInvocation of message.toolInvocations) {
      if (toolInvocation.state === 'result') {
        toolResultIds.push(toolInvocation.toolCallId);
      }
    }

    const sanitizedToolInvocations = message.toolInvocations.filter(
      (toolInvocation) =>
        toolInvocation.state === 'result' ||
        toolResultIds.includes(toolInvocation.toolCallId),
    );

    return {
      ...message,
      toolInvocations: sanitizedToolInvocations,
    };
  });

  return messagesBySanitizedToolInvocations.filter(
    (message) =>
      message.content.length > 0 ||
      (message.toolInvocations && message.toolInvocations.length > 0),
  );
}

export function getMostRecentUserMessage(messages: Array<Message>) {
  const userMessages = messages.filter((message) => message.role === 'user');
  return userMessages.at(-1);
}

export function getDocumentTimestampByIndex(
  documents: Array<Document>,
  index: number,
) {
  if (!documents) return new Date();
  if (index > documents.length) return new Date();

  return documents[index].createdAt;
}
