import { type Subscription } from "@lemonsqueezy/lemonsqueezy.js";

export type InvoiceStatus = "PENDING" | "PAID" | "OVERDUE" | "CANCELLED";

export type InvoiceType = "PURCHASE" | "PAYMENT";

// Document types that the system can process
export type DocumentType =
  | "INVOICE"
  | "CONTRACT"
  | "CERTIFICATE"
  | "RECEIPT"
  | "FORM"
  | "LETTER"
  | "OTHER";

export interface LineItem {
  [key: string]: string; // Allow any field in line items
}

export interface InvoiceData {
  invoiceNumber: string;
  date: string;
  dueDate: string;
  referenceCode?: string;

  vendor: {
    name: string;
    address: string;
    [key: string]: unknown; // Allow any additional vendor fields
  };

  customer: {
    name: string;
    address: string;
    [key: string]: unknown; // Allow any additional customer fields
  };

  lineItems: LineItem[];

  financials: {
    subtotal: string;
    total: string;
    currency: string;
    [key: string]: unknown; // Allow any additional financial fields
  };

  payment: {
    [key: string]: unknown; // Allow any payment fields
  };

  notes?: string;
  termsAndConditions?: string;
  additionalFields?: {
    [key: string]: unknown; // Any other fields that don't fit elsewhere
  };
  [key: string]: unknown; // Allow any top-level fields
}

export interface InvoiceLineItem {
  id?: string;
  name?: string;
  description: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  taxRate?: number;
  taxAmount?: number;
  discount?: number;
  productSku?: string;
  notes?: string;
  invoiceId?: string;
  attributes?: Record<
    string,
    string | number | boolean | Record<string, string | number | boolean>
  >;
  [key: string]:
    | string
    | number
    | boolean
    | undefined
    | Record<
        string,
        string | number | boolean | Record<string, string | number | boolean>
      >
    | null;
}

export interface AISettings {
  id?: string;
  customInstructions?: string;
  confidenceThreshold: number;
  preferredCategories: string[];
  sampleInvoiceUrls: string[];
  userId?: string;
}

export interface ExtractedInvoiceData {
  invoiceNumber?: string;
  vendorName?: string;
  issueDate?: string;
  dueDate?: string;
  amount?: number;
  currency?: string;
  items?: InvoiceLineItem[];
  tax?: number;
  notes?: string;
  language?: string;
  confidence?: number;
  confidenceByField?: Record<string, number>;
  invoiceType?: InvoiceType;
  bankDetails?: {
    accountNumber?: string;
    bankName?: string;
    swiftCode?: string;
    iban?: string;
    routingNumber?: string;
    [key: string]: string | number | boolean | undefined; // Allow any additional bank details
  };
  paymentDetails?: {
    method?: string;
    terms?: string;
    dueInDays?: number;
    reference?: string;
    [key: string]: string | number | boolean | undefined; // Allow any additional payment details
  };
  customFields?: Record<
    string,
    string | number | boolean | Record<string, string | number | boolean>
  >;
  suggestedCategories?: Array<{ name: string; confidence: number }>;
  [key: string]:
    | string
    | number
    | boolean
    | undefined
    | InvoiceLineItem[]
    | Date
    | Record<
        string,
        string | number | boolean | Record<string, string | number | boolean>
      >
    | null
    | Array<{ name: string; confidence: number }>
    | Record<string, unknown>;
}

export interface Category {
  id: string;
  name: string;
  description?: string;
  color?: string;
  icon?: string;
}

// Alias for backward compatibility
export type InvoiceCategory = Category;

export interface Vendor {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  website?: string;
  address?: string;
  notes?: string;
  logoUrl?: string;
}

export interface Invoice {
  id: string;
  invoiceNumber?: string;
  title?: string;
  vendorName?: string;
  issueDate?: Date;
  dueDate?: Date;
  amount?: number;
  currency?: string;
  status: InvoiceStatus;
  invoiceType: InvoiceType;
  notes?: string;
  tags?: string[];
  category?: Category;
  vendor?: Vendor;
  vendorId?: string;
  originalFileUrl?: string;
  thumbnailUrl?: string;
  extractedData?: ExtractedInvoiceData;
  languageCode?: string;
  lineItems?: InvoiceLineItem[];
  createdAt?: Date;
  updatedAt?: Date;
  userId?: string;
  description?: string;
}

export interface User {
  id: string;
  clerkId: string;
  email: string;
  firstName?: string | null;
  lastName?: string | null;
  profileImageUrl?: string | null;
  createdAt: Date;
  updatedAt: Date;
  role: "ADMIN" | "EDITOR" | "USER" | "VIEWER";
}

export interface Organization {
  id: string;
  name: string;
  logoUrl?: string | null;
  industry?: string | null;
  size?: "small" | "medium" | "large";
  invoiceVolume?: "low" | "medium" | "high";
  createdAt: Date;
  updatedAt: Date;
}

// OCR related types
export interface InvoiceFieldConfig {
  id: string;
  label: string;
  description: string;
  required?: boolean;
}

export interface InvoiceItem {
  description: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
}

export interface OCRResult {
  extractedData: InvoiceData;
  suggestedCategories: string[];
}

// Onboarding data type
export interface OnboardingData {
  organization: {
    name: string;
    industry?: string;
    size: "small" | "medium" | "large";
    invoiceVolume: "low" | "medium" | "high";
    documentTypes?: string[];
    commonFormats?: string[];
    vocabularyTerms?: string[];
  };
  aiSettings: AISettings;
}

export interface DataMappingConditions {
  vendorName?: string;
  invoiceType?: InvoiceType;
  minAmount?: number;
  maxAmount?: number;
  currency?: string;
  language?: string;
  [key: string]: string | number | undefined;
}

export type SubscriptionStatusType =
  Subscription["data"]["attributes"]["status"];

export type ReportType = 
  | "SALES" 
  | "EXPENSES" 
  | "VENDOR_ANALYSIS" 
  | "CATEGORY_ANALYSIS" 
  | "CASH_FLOW" 
  | "TAX" 
  | "CUSTOM";

export interface DateRange {
  startDate: Date;
  endDate: Date;
  preset?: string;
}
