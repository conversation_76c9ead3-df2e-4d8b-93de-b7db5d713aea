'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Loader2 } from 'lucide-react';

interface UsageStats {
  chatUsage: number;
  invoiceUsage: number;
  chatLimit: number;
  invoiceLimit: number;
  resetDate: Date;
  daysUntilReset: number;
}

interface UsageGuardProps {
  children: React.ReactNode;
  feature: 'chat' | 'upload';
  fallbackUrl?: string;
}

export function UsageGuard({
  children,
  feature,
  fallbackUrl = '/dashboard/subscription',
}: UsageGuardProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [hasAccess, setHasAccess] = useState(false);
  const router = useRouter();

  useEffect(() => {
    const checkUsageLimit = async () => {
      try {
        const response = await fetch('/api/usage/stats');
        if (response.ok) {
          const result = await response.json();
          if (result.success) {
            const stats: UsageStats = result.stats;

            let canAccess = false;

            if (feature === 'chat') {
              // Check if user has remaining chat usage
              canAccess = stats.chatUsage < stats.chatLimit;
            } else if (feature === 'upload') {
              // Check if user has remaining invoice upload usage
              canAccess = stats.invoiceUsage < stats.invoiceLimit;
            }

            if (canAccess) {
              setHasAccess(true);
            } else {
              // Redirect to subscription page with appropriate message
              const message =
                feature === 'chat'
                  ? 'Chat limit exceeded. Please upgrade your plan to continue.'
                  : 'Invoice upload limit exceeded. Please upgrade your plan to continue.';

              router.push(
                `${fallbackUrl}?limit_exceeded=${feature}&message=${encodeURIComponent(message)}`
              );
              return;
            }
          } else {
            // If we can't get usage stats, allow access (fail open)
            setHasAccess(true);
          }
        } else {
          // If API fails, allow access (fail open)
          setHasAccess(true);
        }
      } catch (error) {
        console.error('Error checking usage limits:', error);
        // If there's an error, allow access (fail open)
        setHasAccess(true);
      } finally {
        setIsLoading(false);
      }
    };

    checkUsageLimit();
  }, [feature, fallbackUrl, router]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
          <p className="text-muted-foreground">
            Checking access permissions...
          </p>
        </div>
      </div>
    );
  }

  if (!hasAccess) {
    // This shouldn't render since we redirect, but just in case
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-muted-foreground">
            Redirecting to subscription page...
          </p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
