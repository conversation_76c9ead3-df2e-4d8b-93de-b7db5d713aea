import type React from 'react';
import { auth } from '@clerk/nextjs/server';
import { redirect } from 'next/navigation';
import { syncUserWithDatabase } from '@/lib/actions/user';
import {
  checkUserSubscription,
  getSubscriptionRedirectUrl,
} from '@/lib/subscription-helpers';

// Add a type for the user with organizations
type UserWithOrganizations = {
  id: string;
  clerkId: string;
  email: string;
  firstName: string | null;
  lastName: string | null;
  profileImageUrl: string | null;
  createdAt: Date;
  updatedAt: Date;
  role: string;
  lastActive: Date | null;
  status: string;
  organizations: { id: string; name: string }[];
};

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Check if user is authenticated
  const { userId } = await auth();

  if (!userId) {
    redirect('/sign-in');
  }

  // Ensure user exists in database
  let user: UserWithOrganizations | null = null;

  try {
    user = (await syncUserWithDatabase()) as UserWithOrganizations;
  } catch (error) {
    console.error('Error syncing user with database:', error);
  }

  // If user doesn't have an organization, redirect to onboarding
  if (
    !user ||
    !user.organizations ||
    user.organizations.length === 0
  ) {
    redirect('/onboarding');
  }

  // Check if user has an active subscription
  // Skip subscription check for subscription page itself to avoid redirect loops
  const subscriptionStatus = await checkUserSubscription();

  if (!subscriptionStatus.hasActiveSubscription) {
    // Get the current URL to redirect back after subscription
    const currentUrl = `/dashboard`;
    const redirectUrl = getSubscriptionRedirectUrl(currentUrl);
    redirect(redirectUrl);
  }

  return (
    <div className="flex min-h-screen flex-col">
      <main className="flex-1">{children}</main>
    </div>
  );
}
