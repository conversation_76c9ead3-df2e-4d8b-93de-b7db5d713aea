import { jsPDF } from "jspdf"
import autoTable, { type CellHookData } from "jspdf-autotable"
import { loadAmiriFont } from "../fonts/amiri-loader"
import { formatNotes, containsArabic, formatDate } from "../utils/pdf"
import type { InvoiceWithExtras, ExtractedData, RenderTextOptions } from "../types/pdf"

// Helper: setup Amiri font and RTL for Arabic
async function setupArabicPdf(doc: jsPDF): Promise<void> {
    const fontLoaded = await loadAmiriFont(doc)
    if (fontLoaded) {
        doc.setFont("Amiri", "normal")
    } else {
        doc.setFont("helvetica", "normal")
    }
    doc.setR2L(true)
}

// Helper: render text with Arabic/RTL support
function renderText(doc: jsPDF, text: string, x: number, y: number, options: RenderTextOptions = {}) {
    if (!text || text === "undefined" || text === "null") text = ""
    const hasArabic = containsArabic(text)
    if (hasArabic) {
        doc.setR2L(true)
        try {
            doc.setFont("Amiri", options.bold ? "bold" : "normal")
        } catch {
            doc.setFont("helvetica", options.bold ? "bold" : "normal")
        }
        if (options.align === "center") {
            doc.text(text, doc.internal.pageSize.width / 2, y, { align: "center" })
        } else if (options.align === "right") {
            doc.text(text, doc.internal.pageSize.width - 14, y, { align: "right" })
        } else if (options.align === "left") {
            doc.text(text, 14, y, { align: "left" })
        } else {
            doc.text(text, doc.internal.pageSize.width - 14, y, { align: "right" })
        }
    } else {
        doc.setR2L(false)
        doc.setFont("helvetica", options.bold ? "bold" : "normal")
        if (options.align === "center") {
            doc.text(text, doc.internal.pageSize.width / 2, y, { align: "center" })
        } else if (options.align === "right") {
            doc.text(text, doc.internal.pageSize.width - 14, y, { align: "right" })
        } else {
            doc.text(text, x, y)
        }
    }
}

function formatCurrency(amount: number, currency = "USD"): string {
    // Validate currency code - must be a 3-letter code according to ISO 4217
    // Common mistake is passing the currency symbol (like $) instead of the code
    if (!currency || currency.length !== 3 || /[$€£¥]/.test(currency)) {
        // Default to USD if invalid currency code is provided
        currency = "USD";
    }

    try {
        return new Intl.NumberFormat("en-US", {
            style: "currency",
            currency,
        }).format(amount)
    } catch (error) {
        // Fallback to basic formatting if Intl.NumberFormat fails
        console.warn(`Error formatting currency ${currency}:`, error);
        return `${currency} ${amount.toFixed(2)}`;
    }
}

export async function generatePdf(invoices: InvoiceWithExtras[], fields: string[]): Promise<Buffer> {
    const doc = new jsPDF({ orientation: "portrait", unit: "mm", format: "a4", compress: true })
    await setupArabicPdf(doc)
    doc.setFontSize(20)
    doc.setTextColor(44, 62, 80)
    renderText(doc, "Invoice Export", doc.internal.pageSize.width / 2, 20, { align: "center", bold: true })
    doc.setFontSize(10)
    doc.setTextColor(100, 100, 100)
    renderText(doc, `Generated on: ${formatDate(new Date())}`, 14, 30)
    renderText(doc, `Number of invoices: ${invoices.length}`, 14, 35)
    let yPos = 45
    for (let index = 0; index < invoices.length; index++) {
        const invoice = invoices[index]
        const extractedData = invoice.extractedData && typeof invoice.extractedData === "object"
            ? (invoice.extractedData as unknown as ExtractedData)
            : undefined
        const isArabic =
            extractedData?.meta?.language === "ar" ||
            (invoice.vendorName && containsArabic(invoice.vendorName)) ||
            (extractedData?.vendor?.address && containsArabic(extractedData.vendor.address))
        if (isArabic) {
            await setupArabicPdf(doc)
        } else {
            doc.setFont("helvetica", "normal")
            doc.setR2L(false)
        }
        if (index > 0) {
            doc.addPage()
            yPos = 20
        }
        // Header
        doc.setFontSize(16)
        doc.setTextColor(41, 128, 185)
        renderText(doc, `Invoice ${invoice.invoiceNumber || invoice.id}`, 14, yPos, { align: isArabic ? "right" : "left", bold: true })
        doc.setLineWidth(0.5)
        doc.setDrawColor(41, 128, 185)
        doc.line(14, yPos + 2, 196, yPos + 2)
        yPos += 10
        // Details
        doc.setFontSize(11)
        doc.setTextColor(60, 60, 60)
        const leftColumnX = 14
        const rightColumnX = doc.internal.pageSize.width / 2 + 10
        let detailsYPos = yPos
        // Invoice Number
        if (fields.includes("invoiceNumber")) {
            renderText(doc, "Invoice Number:", leftColumnX, detailsYPos, { align: "left", bold: true })
            renderText(
                doc,
                String(invoice.invoiceNumber || "N/A"),
                leftColumnX + 40,
                detailsYPos,
                {
                    align: containsArabic(invoice.invoiceNumber || "") ? "right" : "left",
                    bold: false
                }
            )
            detailsYPos += 6
        }
        // Issue Date
        if (fields.includes("date")) {
            renderText(doc, "Issue Date:", leftColumnX, detailsYPos, { align: "left", bold: true })
            const dateValue = invoice.issueDate ? formatDate(invoice.issueDate) : "N/A"
            renderText(
                doc,
                dateValue,
                leftColumnX + 40,
                detailsYPos,
                {
                    align: containsArabic(dateValue || "") ? "right" : "left",
                    bold: false
                }
            )
            detailsYPos += 6
        }
        // Due Date
        if (fields.includes("dueDate")) {
            renderText(doc, "Due Date:", leftColumnX, detailsYPos, { align: "left", bold: true })
            const dueValue = invoice.dueDate ? formatDate(invoice.dueDate) : "N/A"
            renderText(
                doc,
                dueValue,
                leftColumnX + 40,
                detailsYPos,
                {
                    align: containsArabic(dueValue || "") ? "right" : "left",
                    bold: false
                }
            )
            detailsYPos += 6
        }
        detailsYPos = yPos
        // Status
        if (fields.includes("status")) {
            renderText(doc, "Status:", rightColumnX, detailsYPos, { align: "left", bold: true })
            renderText(
                doc,
                String(invoice.status || "N/A"),
                rightColumnX + 30,
                detailsYPos,
                {
                    align: containsArabic(invoice.status || "") ? "right" : "left",
                    bold: false
                }
            )
            detailsYPos += 6
        }
        // Amount
        if (fields.includes("amount")) {
            renderText(doc, "Amount:", rightColumnX, detailsYPos, { align: "left", bold: true })
            const amountValue = formatCurrency(invoice.amount || 0, invoice.currency || "USD")
            renderText(
                doc,
                amountValue,
                rightColumnX + 30,
                detailsYPos,
                {
                    align: containsArabic(amountValue || "") ? "right" : "left",
                    bold: false
                }
            )
            detailsYPos += 6
        }
        // Category
        if (fields.includes("category")) {
            renderText(doc, "Category:", rightColumnX, detailsYPos, { align: "left", bold: true })
            const catValue = invoice.category ? invoice.category.name : "Uncategorized"
            renderText(
                doc,
                catValue,
                rightColumnX + 30,
                detailsYPos,
                {
                    align: containsArabic(catValue || "") ? "right" : "left",
                    bold: false
                }
            )
            detailsYPos += 6
        }
        yPos = Math.max(detailsYPos, yPos) + 5
        // Vendor
        if (fields.includes("vendor")) {
            doc.setFontSize(13)
            doc.setTextColor(41, 128, 185)
            renderText(doc, "Vendor Information", 14, yPos, { align: isArabic ? "right" : "left", bold: true })
            doc.setLineWidth(0.3)
            doc.setDrawColor(200, 200, 200)
            doc.line(14, yPos + 2, 100, yPos + 2)
            yPos += 8
            doc.setFontSize(11)
            doc.setTextColor(60, 60, 60)
            renderText(doc, "Vendor:", 14, yPos, { align: "left", bold: true })
            renderText(
                doc,
                String(invoice.vendorName || "Unknown Vendor"),
                14 + 30,
                yPos,
                {
                    align: containsArabic(invoice.vendorName || "") ? "right" : "left",
                    bold: false
                }
            )
            yPos += 6
            if (extractedData?.vendor) {
                if (extractedData.vendor.address) {
                    renderText(doc, "Address:", 14, yPos, { align: "left", bold: true })
                    renderText(
                        doc,
                        extractedData.vendor.address || "",
                        14 + 30,
                        yPos,
                        {
                            align: containsArabic(extractedData.vendor.address || "") ? "right" : "left",
                            bold: false
                        }
                    )
                    yPos += 6
                }
                if (extractedData.vendor.email) {
                    renderText(doc, "Email:", 14, yPos, { align: "left", bold: true })
                    renderText(
                        doc,
                        extractedData.vendor.email || "",
                        14 + 30,
                        yPos,
                        {
                            align: containsArabic(extractedData.vendor.email || "") ? "right" : "left",
                            bold: false
                        }
                    )
                    yPos += 6
                }
                if (extractedData.vendor.phone) {
                    renderText(doc, "Phone:", 14, yPos, { align: "left", bold: true })
                    renderText(
                        doc,
                        extractedData.vendor.phone || "",
                        14 + 30,
                        yPos,
                        {
                            align: containsArabic(extractedData.vendor.phone || "") ? "right" : "left",
                            bold: false
                        }
                    )
                    yPos += 6
                }
            }
            yPos += 5
        }
        // Customer
        if (fields.includes("customer")) {
            if (extractedData?.customer) {
                doc.setFontSize(13)
                doc.setTextColor(41, 128, 185)
                renderText(doc, "Customer Information", 14, yPos, { align: isArabic ? "right" : "left", bold: true })
                doc.setLineWidth(0.3)
                doc.setDrawColor(200, 200, 200)
                doc.line(14, yPos + 2, 100, yPos + 2)
                yPos += 8
                doc.setFontSize(11)
                doc.setTextColor(60, 60, 60)
                if (extractedData.customer.name) {
                    renderText(doc, "Name:", 14, yPos, { align: "left", bold: true })
                    renderText(
                        doc,
                        extractedData.customer.name || "",
                        14 + 30,
                        yPos,
                        {
                            align: containsArabic(extractedData.customer.name || "") ? "right" : "left",
                            bold: false
                        }
                    )
                    yPos += 6
                }
                if (extractedData.customer.address) {
                    renderText(doc, "Address:", 14, yPos, { align: "left", bold: true })
                    renderText(
                        doc,
                        extractedData.customer.address || "",
                        14 + 30,
                        yPos,
                        {
                            align: containsArabic(extractedData.customer.address || "") ? "right" : "left",
                            bold: false
                        }
                    )
                    yPos += 6
                }
                yPos += 5
            }
        }
        // Line items
        if (fields.includes("lineItems") && invoice.lineItems && invoice.lineItems.length > 0) {
            doc.setFontSize(13)
            doc.setTextColor(41, 128, 185)
            renderText(doc, "Line Items", 14, yPos, { align: isArabic ? "right" : "left", bold: true })
            doc.setLineWidth(0.3)
            doc.setDrawColor(200, 200, 200)
            doc.line(14, yPos + 2, 100, yPos + 2)
            yPos += 10
            if (yPos > 200) {
                doc.addPage()
                yPos = 20
            }
            const tableHeaders = ["Description", "Quantity", "Unit Price", "Total"]
            const tableData = invoice.lineItems.map((item) => [
                item.description || "N/A",
                String(item.quantity || "N/A"),
                item.unitPrice ? formatCurrency(item.unitPrice, invoice.currency || "USD") : "N/A",
                item.totalPrice ? formatCurrency(item.totalPrice, invoice.currency || "USD") : "N/A",
            ])
            autoTable(doc, {
                startY: yPos,
                head: [tableHeaders],
                body: tableData,
                theme: "grid",
                headStyles: {
                    fillColor: [41, 128, 185],
                    textColor: [255, 255, 255],
                    fontStyle: "bold",
                    halign: isArabic ? "right" : "left",
                    valign: "middle",
                    fontSize: 10,
                    cellPadding: { top: 4, right: 3, bottom: 4, left: 3 },
                },
                margin: { top: 20, left: 10, right: 10, bottom: 20 },
                styles: {
                    fontSize: 9,
                    halign: isArabic ? "right" : "left",
                    cellPadding: { top: 3, right: 3, bottom: 3, left: 3 },
                    lineColor: [200, 200, 200],
                    lineWidth: 0.2,
                    overflow: "linebreak",
                    font: isArabic ? "Amiri" : "helvetica",
                },
                columnStyles: {
                    0: { cellWidth: "auto", halign: isArabic ? "right" : "left" },
                    1: { cellWidth: 20, halign: "center" },
                    2: { cellWidth: 25, halign: "right" },
                    3: { cellWidth: 25, halign: "right" },
                },
                alternateRowStyles: {
                    fillColor: [245, 245, 245],
                },
                tableWidth: "wrap",
                horizontalPageBreak: true,
                didParseCell: (data: CellHookData) => {
                    if (data.cell && data.cell.text) {
                        let text = ""
                        if (Array.isArray(data.cell.text)) {
                            text = data.cell.text.join(" ")
                        } else {
                            text = String(data.cell.text)
                        }
                        if (containsArabic(text)) {
                            data.cell.styles.font = "Amiri"
                            data.cell.styles.halign = "right"
                            data.cell.styles.minCellWidth = 20
                        }
                    }
                    if (data.column.index === 0) {
                        data.cell.styles.halign = isArabic ? "right" : "left"
                    } else if (data.column.index === 1) {
                        data.cell.styles.halign = "center"
                    } else {
                        data.cell.styles.halign = "right"
                    }
                },
                willDrawCell: (data: CellHookData) => {
                    if (data.section === "head") {
                        data.cell.styles.fontStyle = "bold"
                    }
                },
            })
            yPos = (doc as unknown as { lastAutoTable: { finalY: number } }).lastAutoTable.finalY + 15
            if (invoice.amount) {
                doc.setFontSize(12)
                doc.setTextColor(41, 128, 185)
                doc.setFont(isArabic ? "Amiri" : "helvetica", "bold")
                doc.setDrawColor(41, 128, 185)
                doc.setLineWidth(0.5)
                const totalLineY = yPos - 5
                if (isArabic) {
                    doc.line(14, totalLineY, 80, totalLineY)
                } else {
                    doc.line(140, totalLineY, 196, totalLineY)
                }
                const totalX = isArabic ? 14 : 150
                const amountX = isArabic ? 60 : 180
                renderText(doc, "Total:", totalX, yPos, { align: isArabic ? "right" : "left", bold: true })
                renderText(doc, formatCurrency(invoice.amount, invoice.currency || "USD"), amountX, yPos, { align: isArabic ? "left" : "right", bold: true })
                yPos += 18
            }
            // Additional line item details
            const allLineItemAttributes = new Set<string>()
            let hasAttributes = false
            invoice.lineItems.forEach((item) => {
                if (item.attributes && Object.keys(item.attributes).length > 0) {
                    hasAttributes = true
                    Object.keys(item.attributes).forEach((key) => allLineItemAttributes.add(key))
                }
                Object.keys(item).forEach((key) => {
                    if (![
                        "id", "description", "quantity", "unitPrice", "totalPrice", "taxRate", "taxAmount", "discount", "productSku", "notes", "attributes", "invoiceId"
                    ].includes(key)) {
                        allLineItemAttributes.add(key)
                        hasAttributes = true
                    }
                })
            })
            if (hasAttributes) {
                doc.addPage()
                yPos = 20
                doc.setFontSize(13)
                doc.setTextColor(41, 128, 185)
                renderText(doc, "Additional Line Item Details", 14, yPos, { align: isArabic ? "right" : "left", bold: true })
                doc.setLineWidth(0.3)
                doc.setDrawColor(200, 200, 200)
                doc.line(14, yPos + 2, 150, yPos + 2)
                yPos += 10
                const attrHeaders = ["Item", ...Array.from(allLineItemAttributes)]
                const attrRows = invoice.lineItems.map((item) => {
                    const row = [item.description || "N/A"]
                    allLineItemAttributes.forEach((attr) => {
                        let value = "N/A"
                        if (item.attributes && attr in item.attributes) {
                            const attrValue = item.attributes[attr]
                            value = typeof attrValue === "object" ? JSON.stringify(attrValue) : String(attrValue)
                        } else if (attr in item) {
                            const itemValue = item[attr as keyof typeof item]
                            value = typeof itemValue === "object" && itemValue !== null ? JSON.stringify(itemValue) : String(itemValue || "N/A")
                        }
                        row.push(value)
                    })
                    return row
                })
                const detailsColumnStyles: Record<number, object> = {}
                const availableWidth = doc.internal.pageSize.width - 20
                const columnCount = attrHeaders.length
                const firstColWidth = Math.min(60, availableWidth * 0.3)
                const remainingWidth = availableWidth - firstColWidth
                const otherColWidth = remainingWidth / (columnCount - 1)
                for (let i = 0; i < columnCount; i++) {
                    const header = attrHeaders[i].toLowerCase()
                    let halign = "left"
                    if (["price", "amount", "total"].includes(header)) {
                        halign = "right"
                    } else if (["quantity", "date", "createdat", "updatedat"].includes(header)) {
                        halign = "center"
                    } else if (isArabic || (i === 0 && containsArabic(String(attrRows[0]?.[i] || "")))) {
                        halign = "right"
                    }
                    detailsColumnStyles[i] = {
                        cellWidth: i === 0 ? firstColWidth : otherColWidth,
                        halign,
                        overflow: "linebreak",
                        fontSize: 8,
                        cellPadding: { top: 2, right: 2, bottom: 2, left: 2 },
                    }
                }
                autoTable(doc, {
                    startY: yPos,
                    head: [attrHeaders],
                    body: attrRows,
                    theme: "grid",
                    headStyles: {
                        fillColor: [41, 128, 185],
                        textColor: [255, 255, 255],
                        fontStyle: "bold",
                        halign: isArabic ? "right" : "left",
                        valign: "middle",
                        fontSize: 9,
                        cellPadding: { top: 3, right: 2, bottom: 3, left: 2 },
                    },
                    margin: { top: 10, left: 10, right: 10, bottom: 10 },
                    styles: {
                        fontSize: 8,
                        halign: isArabic ? "right" : "left",
                        cellPadding: { top: 2, right: 2, bottom: 2, left: 2 },
                        lineColor: [200, 200, 200],
                        lineWidth: 0.2,
                        font: isArabic ? "Amiri" : "helvetica",
                        overflow: "linebreak",
                    },
                    alternateRowStyles: {
                        fillColor: [245, 245, 245],
                    },
                    columnStyles: detailsColumnStyles,
                    tableWidth: "auto",
                    didParseCell: (data: CellHookData) => {
                        if (data.cell && data.cell.text) {
                            let text = ""
                            if (Array.isArray(data.cell.text)) {
                                text = data.cell.text.join(" ")
                            } else {
                                text = String(data.cell.text)
                            }
                            if (containsArabic(text)) {
                                data.cell.styles.halign = "right"
                            }
                        }
                    },
                    willDrawCell: (data: CellHookData) => {
                        if (data.section === "head") {
                            data.cell.styles.fontStyle = "bold"
                            data.cell.styles.fillColor = [41, 128, 185]
                            data.cell.styles.textColor = [255, 255, 255]
                        }
                    },
                })
                yPos = (doc as unknown as { lastAutoTable: { finalY: number } }).lastAutoTable.finalY + 10
            }
        }
        // Notes
        if (fields.includes("notes")) {
            const rawNotes = invoice.notes || extractedData?.notes
            const formattedNotes = formatNotes(rawNotes ?? "")
            if (formattedNotes) {
                if (yPos > 220) {
                    doc.addPage()
                    yPos = 20
                }
                doc.setFontSize(13)
                doc.setTextColor(41, 128, 185)
                renderText(doc, "Notes", 14, yPos, { align: isArabic ? "right" : "left", bold: true })
                doc.setLineWidth(0.3)
                doc.setDrawColor(200, 200, 200)
                doc.line(14, yPos + 2, 100, yPos + 2)
                yPos += 8
                doc.setFontSize(10)
                doc.setTextColor(60, 60, 60)
                doc.setFont(containsArabic(formattedNotes) ? "Amiri" : "helvetica", "normal")
                const splitNotes = doc.splitTextToSize(formattedNotes, 180)
                if (isArabic || containsArabic(formattedNotes)) {
                    doc.setR2L(true)
                    for (let i = 0; i < splitNotes.length; i++) {
                        renderText(doc, splitNotes[i], 14, yPos + i * 6, { align: "right" })
                    }
                    yPos += splitNotes.length * 6 + 5
                } else {
                    doc.setR2L(false)
                    doc.text(splitNotes, 14, yPos)
                    yPos += splitNotes.length * 6 + 5
                }
            }
        }
        // Terms and conditions
        if (fields.includes("termsAndConditions")) {
            if (extractedData?.termsAndConditions) {
                if (yPos > 220) {
                    doc.addPage()
                    yPos = 20
                }
                doc.setFontSize(13)
                doc.setTextColor(41, 128, 185)
                renderText(doc, "Terms and Conditions", 14, yPos, { align: isArabic ? "right" : "left", bold: true })
                doc.setLineWidth(0.3)
                doc.setDrawColor(200, 200, 200)
                doc.line(14, yPos + 2, 100, yPos + 2)
                yPos += 8
                doc.setFontSize(10)
                doc.setTextColor(60, 60, 60)
                const termsText = String(extractedData.termsAndConditions || "")
                doc.setFont(containsArabic(termsText) ? "Amiri" : "helvetica", "normal")
                const splitTerms = doc.splitTextToSize(termsText, 180)
                if (isArabic || containsArabic(termsText)) {
                    doc.setR2L(true)
                    for (let i = 0; i < splitTerms.length; i++) {
                        renderText(doc, splitTerms[i], 14, yPos + i * 6, { align: "right" })
                    }
                    yPos += splitTerms.length * 6 + 5
                } else {
                    doc.setR2L(false)
                    doc.text(splitTerms, 14, yPos)
                    yPos += splitTerms.length * 6 + 5
                }
            }
        }
        // Metadata
        if (fields.includes("metadata")) {
            if (yPos > 220) {
                doc.addPage()
                yPos = 20
            }
            doc.setFontSize(13)
            doc.setTextColor(41, 128, 185)
            renderText(doc, "Metadata", 14, yPos, { align: isArabic ? "right" : "left", bold: true })
            doc.setLineWidth(0.3)
            doc.setDrawColor(200, 200, 200)
            doc.line(14, yPos + 2, 100, yPos + 2)
            yPos += 8
            doc.setFontSize(10)
            doc.setTextColor(100, 100, 100)
            renderText(doc, `Created: ${formatDate(invoice.createdAt)}`, 14, yPos, { align: isArabic ? "right" : "left" })
            yPos += 5
            renderText(doc, `Last Updated: ${formatDate(invoice.updatedAt)}`, 14, yPos, { align: isArabic ? "right" : "left" })
            yPos += 5
            renderText(doc, `Invoice ID: ${invoice.id}`, 14, yPos, { align: isArabic ? "right" : "left" })
            yPos += 5
            if (extractedData?.meta?.language) {
                renderText(doc, `Language: ${extractedData.meta.languageName || extractedData.meta.language}`, 14, yPos, { align: isArabic ? "right" : "left" })
                yPos += 5
            }
            if (extractedData?.meta?.confidence?.overall !== undefined) {
                renderText(doc, `Extraction Confidence: ${extractedData.meta.confidence.overall}%`, 14, yPos, { align: isArabic ? "right" : "left" })
                yPos += 5
            }
        }
    }
    // Footer
    const pageCount = doc.getNumberOfPages()
    for (let i = 1; i <= pageCount; i++) {
        doc.setPage(i)
        doc.setFontSize(8)
        doc.setFont("helvetica", "normal")
        doc.setR2L(false)
        doc.setTextColor(120, 120, 120)
        doc.setLineWidth(0.1)
        doc.setDrawColor(180, 180, 180)
        doc.line(14, doc.internal.pageSize.height - 15, doc.internal.pageSize.width - 14, doc.internal.pageSize.height - 15)
        doc.text(`Page ${i} of ${pageCount}`, doc.internal.pageSize.width / 2, doc.internal.pageSize.height - 8, { align: "center" })
    }
    return Buffer.from(doc.output("arraybuffer"))
}