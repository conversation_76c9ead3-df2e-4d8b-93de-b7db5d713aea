import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import db from '@/db/db';
import { generateReportPdf } from '@/lib/actions/report-pdf-generator';
import { generateReportExcel } from '@/lib/actions/report-excel-generator';
import { put } from '@vercel/blob';
import { nanoid } from 'nanoid';

export async function GET(req: NextRequest, props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  try {
    // Ensure params is properly awaited
    const id = params.id;

    const { userId: clerkUserId } = await auth();

    if (!clerkUserId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the user from the database
    const user = await db.user.findUnique({
      where: { clerkId: clerkUserId },
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Get the report format from query params
    const url = new URL(req.url);
    const format = url.searchParams.get('format') || 'pdf';

    // Find the report
    const report = await db.report.findUnique({
      where: {
        id,
        userId: user.id
      },
      include: {
        data: true,
      }
    });

    if (!report) {
      return NextResponse.json({ error: 'Report not found' }, { status: 404 });
    }

    // Check if we have data
    if (!report.data || report.data.length === 0) {
      console.warn(`No data found for report ${id}. Attempting to regenerate data.`);

      // Try to regenerate the data
      try {
        await db.reportData.deleteMany({
          where: { reportId: id }
        });

        // Regenerate the report data
        const reportData = await import('@/lib/actions/report-data-generator');
        await reportData.generateReportData(
          user.id,
          id,
          report.reportType,
          report.startDate,
          report.endDate,
          undefined // No currency filter
        );

        // Fetch the report again with the new data
        const updatedReport = await db.report.findUnique({
          where: { id },
          include: { data: true }
        });

        if (updatedReport && updatedReport.data && updatedReport.data.length > 0) {
          console.log(`Successfully regenerated data for report ${id} with ${updatedReport.data.length} data points`);
          // Use the updated report
          Object.assign(report, updatedReport);
        } else {
          console.error(`Failed to regenerate data for report ${id}`);
        }
      } catch (error) {
        console.error(`Error regenerating data for report ${id}:`, error);
      }
    }

    // Generate a unique export ID
    const exportId = nanoid();

    // Generate the report file
    let fileBuffer: Buffer;
    let contentType: string;
    let fileName: string;

    if (format === 'excel') {
      fileBuffer = await generateReportExcel(report);
      contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      fileName = `${report.title.replace(/ /g, '_')}_${exportId}.xlsx`;
    } else {
      fileBuffer = await generateReportPdf(report);
      contentType = 'application/pdf';
      fileName = `${report.title.replace(/ /g, '_')}_${exportId}.pdf`;
    }

    // Upload the file to Vercel Blob
    const { url: fileUrl } = await put(`reports/${fileName}`, fileBuffer, {
      contentType,
      access: 'public',
    });

    // Update the report with the file URL
    await db.report.update({
      where: { id },
      data: { fileUrl },
    });

    // Log the download
    await db.reportDownload.create({
      data: {
        reportId: id,
        userId: user.id,
        downloadedAt: new Date(),
      },
    });

    // Redirect to the file URL
    return NextResponse.redirect(fileUrl);
  } catch (error) {
    console.error('Error generating report:', error);
    return NextResponse.json(
      { error: 'Failed to generate report' },
      { status: 500 }
    );
  }
}
