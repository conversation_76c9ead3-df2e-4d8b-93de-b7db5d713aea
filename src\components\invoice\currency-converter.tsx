"use client";

import { useState } from "react";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { ArrowRight, RefreshCw } from "lucide-react";
import { convertCurrency } from "@/actions/currency-conversion";
import { formatCurrency } from "@/lib/utils";

interface CurrencyConverterProps {
  baseCurrency: string;
  baseAmount: number;
  availableCurrencies?: string[];
  exchangeRates?: Record<string, number>;
}

export function CurrencyConverter({ 
  baseCurrency, 
  baseAmount, 
  availableCurrencies = ["USD", "EUR", "GBP", "JPY", "CAD", "AUD", "CNY", "INR"],
  exchangeRates
}: CurrencyConverterProps) {
  const [amount, setAmount] = useState(baseAmount.toString());
  const [fromCurrency, setFromCurrency] = useState(baseCurrency);
  const [toCurrency, setToCurrency] = useState(
    baseCurrency === "USD" ? "EUR" : "USD"
  );
  const [convertedAmount, setConvertedAmount] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Filter out the from currency from available to currencies
  const toOptions = availableCurrencies.filter(c => c !== fromCurrency);
  
  // Handle currency conversion
  const handleConvert = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const amountValue = parseFloat(amount);
      
      if (isNaN(amountValue)) {
        setError("Please enter a valid amount");
        setIsLoading(false);
        return;
      }
      
      // If we have exchange rates, use them directly
      if (exchangeRates && exchangeRates[toCurrency]) {
        setConvertedAmount(amountValue * exchangeRates[toCurrency]);
        setIsLoading(false);
        return;
      }
      
      // Otherwise, call the API
      const { data, error } = await convertCurrency(
        amountValue,
        fromCurrency,
        toCurrency
      );
      
      if (error) {
        setError(error);
      } else if (data !== undefined) {
        setConvertedAmount(data);
      }
    } catch (err) {
      setError("Failed to convert currency");
    } finally {
      setIsLoading(false);
    }
  };
  
  // Swap currencies
  const handleSwap = () => {
    setFromCurrency(toCurrency);
    setToCurrency(fromCurrency);
    setConvertedAmount(null);
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Currency Converter</CardTitle>
        <CardDescription>
          Convert invoice amounts between currencies
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Amount</label>
              <Input
                type="number"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                placeholder="Enter amount"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">From</label>
              <Select
                value={fromCurrency}
                onValueChange={setFromCurrency}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select currency" />
                </SelectTrigger>
                <SelectContent>
                  {availableCurrencies.map((currency) => (
                    <SelectItem key={currency} value={currency}>
                      {currency}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div className="flex justify-center">
            <Button
              variant="outline"
              size="icon"
              onClick={handleSwap}
              type="button"
            >
              <ArrowRight className="h-4 w-4 rotate-90" />
            </Button>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Converted Amount</label>
              <div className="h-10 px-3 py-2 rounded-md border border-input bg-background flex items-center">
                {convertedAmount !== null
                  ? formatCurrency(convertedAmount, toCurrency)
                  : "—"}
              </div>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">To</label>
              <Select
                value={toCurrency}
                onValueChange={setToCurrency}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select currency" />
                </SelectTrigger>
                <SelectContent>
                  {toOptions.map((currency) => (
                    <SelectItem key={currency} value={currency}>
                      {currency}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          
          {error && (
            <div className="text-sm text-red-500">
              {error}
            </div>
          )}
          
          <Button
            onClick={handleConvert}
            disabled={isLoading}
            className="w-full"
          >
            {isLoading ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Converting...
              </>
            ) : (
              "Convert"
            )}
          </Button>
          
          {exchangeRates && (
            <div className="text-xs text-gray-500 text-center">
              Exchange rates last updated: {new Date().toLocaleDateString()}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
