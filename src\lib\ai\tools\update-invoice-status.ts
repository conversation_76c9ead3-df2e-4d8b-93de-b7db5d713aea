import { tool } from 'ai';
import { z } from 'zod';
import db from '@/db/db';
import { getAuthenticatedUserId, checkPermission } from '@/lib/auth-helpers';
import { InvoiceStatus } from '@prisma/client';

// Helper function to update multiple invoices by vendor name
export async function updateInvoicesByVendor(vendorName: string, newStatus: InvoiceStatus | undefined, userId: string) {
  try {
    // Find all invoices for this vendor
    const invoices = await db.invoice.findMany({
      where: {
        vendorName: {
          contains: vendorName,
          mode: 'insensitive'
        },
        userId
      }
    });

    if (invoices.length === 0) {
      return {
        success: false,
        message: `No invoices found for vendor "${vendorName}".`
      };
    }

    // Update all found invoices
    const updatePromises = invoices.map(invoice =>
      db.invoice.update({
        where: { id: invoice.id },
        data: {
          ...(newStatus ? { status: newStatus } : {}),
          updatedAt: new Date()
        }
      })
    );

    const updatedInvoices = await Promise.all(updatePromises);

    return {
      success: true,
      message: `Updated ${updatedInvoices.length} invoices for vendor "${vendorName}"${newStatus ? ` to status ${newStatus}` : ''}.`,
      updatedInvoices: updatedInvoices.map(invoice => ({
        id: invoice.id,
        number: invoice.invoiceNumber || '',
        status: invoice.status
      }))
    };
  } catch (error) {
    console.error('Error updating invoices by vendor:', error);
    return {
      success: false,
      message: 'Failed to update invoices. Please try again.',
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

export const updateInvoiceStatus = tool({
  description: 'Updates an existing invoice with new information. Can change any field including status, category, vendor, dates, amounts, and line items. Can also update all invoices from a specific vendor by providing vendorName and setting updateAllFromVendor to true.',
  parameters: z.object({
    invoiceId: z.string().optional().describe('The ID of the invoice to update. Optional if updating by vendor name.'),
    status: z.enum(['PENDING', 'PAID', 'OVERDUE', 'CANCELLED']).optional().describe('The new status for the invoice'),
    category: z.string().optional().describe('Optional new category for the invoice'),
    vendorName: z.string().optional().describe('Optional vendor name to filter by or update'),
    updateAllFromVendor: z.boolean().optional().default(false).describe('If true, updates all invoices from the specified vendor'),
    notes: z.string().optional().describe('Optional notes to add or update'),
    issueDate: z.string().optional().describe('The date when the invoice was issued (format: YYYY-MM-DD)'),
    dueDate: z.string().optional().describe('The date when the invoice is due (format: YYYY-MM-DD)'),
    amount: z.number().optional().describe('The total amount of the invoice'),
    currency: z.string().optional().describe('The currency of the invoice (e.g., USD, EUR)'),
    invoiceNumber: z.string().optional().describe('The invoice number or reference'),
    title: z.string().optional().describe('The title of the invoice'),
    invoiceType: z.string().optional().describe('The type of invoice (e.g., PURCHASE, SALES)'),
    tags: z.array(z.string()).optional().describe('Tags associated with the invoice'),
    lineItems: z.array(z.object({
      id: z.string().optional().describe('The ID of the line item (required for updating existing line items)'),
      description: z.string().optional().describe('Description of the item or service'),
      quantity: z.number().optional().describe('Quantity of the item or service'),
      unitPrice: z.number().optional().describe('Price per unit'),
      totalPrice: z.number().optional().describe('Total price for this line item'),
      taxRate: z.number().optional().describe('Tax rate applied to this line item'),
      taxAmount: z.number().optional().describe('Tax amount for this line item'),
      discount: z.number().optional().describe('Discount applied to this line item'),
      productSku: z.string().optional().describe('Product SKU or identifier'),
      notes: z.string().optional().describe('Additional notes for this line item')
    })).optional().describe('Line items to add, update, or replace in the invoice'),
  }),
  execute: async ({
    invoiceId,
    status,
    category,
    vendorName,
    updateAllFromVendor = false,
    notes,
    issueDate,
    dueDate,
    amount,
    currency,
    invoiceNumber,
    title,
    invoiceType,
    tags,
    lineItems
  }) => {
    try {
      // Get the authenticated user ID
      const userId = await getAuthenticatedUserId();

      // If updating all invoices from a vendor and status is provided
      if (updateAllFromVendor && vendorName && status) {
        return await updateInvoicesByVendor(vendorName, status, userId);
      }

      // Find the invoice in the database - try multiple ways to identify the invoice
      let invoice = null;

      // Try by ID first
      if (invoiceId) {
        invoice = await db.invoice.findUnique({
          where: { id: invoiceId },
          include: {
            category: true,
            lineItems: true
          },
        });
      }

      // If not found by ID, try to find by invoice number
      if (!invoice && invoiceId) {
        invoice = await db.invoice.findFirst({
          where: {
            invoiceNumber: invoiceId,
            userId
          },
          include: {
            category: true,
            lineItems: true
          },
        });
      }

      // If still not found, try to find by vendor name
      if (!invoice && vendorName) {
        const invoices = await db.invoice.findMany({
          where: {
            vendorName: {
              contains: vendorName,
              mode: 'insensitive'
            },
            userId
          },
          include: {
            category: true,
            lineItems: true
          },
          take: 1,
          orderBy: { createdAt: 'desc' }
        });

        if (invoices.length > 0) {
          invoice = invoices[0];
        }
      }

      if (!invoice) {
        return {
          success: false,
          message: `Invoice ${invoiceId ? `with ID or number ${invoiceId}` : ''} ${vendorName ? `for vendor "${vendorName}"` : ''} not found. Please check the details and try again.`,
        };
      }

      // Check if the invoice belongs to the current user
      await checkPermission(invoice.userId);

      // Prepare the update data
      const updateData: Record<string, unknown> = {
        updatedAt: new Date(),
      };

      // Update the status if provided
      if (status) {
        updateData.status = status;
      }

      // Update all other fields if provided
      if (notes !== undefined) {
        updateData.notes = notes;
      }

      if (vendorName !== undefined) {
        updateData.vendorName = vendorName;
      }

      if (issueDate !== undefined) {
        updateData.issueDate = new Date(issueDate);
      }

      if (dueDate !== undefined) {
        updateData.dueDate = new Date(dueDate);
      }

      if (amount !== undefined) {
        updateData.amount = amount;
      }

      if (currency !== undefined) {
        updateData.currency = currency;
      }

      if (invoiceNumber !== undefined) {
        updateData.invoiceNumber = invoiceNumber;
      }

      if (title !== undefined) {
        updateData.title = title;
      }

      if (invoiceType !== undefined) {
        updateData.invoiceType = invoiceType;
      }

      if (tags !== undefined) {
        updateData.tags = tags;
      }

      // Handle category update
      if (category) {
        // Look for existing category with the same name
        const existingCategory = await db.category.findFirst({
          where: {
            name: {
              equals: category,
              mode: 'insensitive',
            },
            userId: invoice.userId,
          },
        });

        if (existingCategory) {
          updateData.categoryId = existingCategory.id;
        } else {
          // Create a new category
          const newCategory = await db.category.create({
            data: {
              name: category,
              userId: invoice.userId,
            },
          });

          updateData.categoryId = newCategory.id;
        }
      }

      // Handle line items if provided
      let lineItemsUpdateOperation = undefined;

      if (lineItems && lineItems.length > 0) {
        // Get existing line item IDs
        const existingLineItemIds = invoice.lineItems.map(item => item.id);

        // Separate line items into those to update and those to create
        const lineItemsToUpdate = lineItems.filter(item => item.id && existingLineItemIds.includes(item.id));
        const lineItemsToCreate = lineItems.filter(item => !item.id || !existingLineItemIds.includes(item.id));

        // Create update operations for existing line items
        const updateOperations = lineItemsToUpdate.map(item => {
          const { id, ...updateData } = item;
          return {
            where: { id },
            data: updateData
          };
        });

        // Create operation for new line items
        const createOperations = lineItemsToCreate.map(item => {
          // Create a new object without the id property
          const createData = { ...item };
          if ('id' in createData) {
            delete createData.id;
          }
          return createData;
        });

        // Build the line items update operation
        lineItemsUpdateOperation = {
          update: updateOperations.length > 0 ? updateOperations : undefined,
          create: createOperations.length > 0 ? createOperations : undefined,
        };
      }

      // Add line items update operation to updateData if needed
      if (lineItemsUpdateOperation) {
        updateData.lineItems = lineItemsUpdateOperation;
      }

      // Update the invoice in the database
      const updatedInvoice = await db.invoice.update({
        where: { id: invoice.id }, // Use the found invoice ID, not the parameter
        data: updateData,
        include: {
          category: true,
          lineItems: true
        },
      });

      return {
        success: true,
        id: updatedInvoice.id,
        number: updatedInvoice.invoiceNumber || '',
        status: updatedInvoice.status,
        category: updatedInvoice.category?.name,
        vendorName: updatedInvoice.vendorName || '',
        amount: updatedInvoice.amount,
        currency: updatedInvoice.currency,
        issueDate: updatedInvoice.issueDate,
        dueDate: updatedInvoice.dueDate,
        lineItemCount: updatedInvoice.lineItems.length,
        message: `Invoice ${updatedInvoice.invoiceNumber || ''} updated successfully.`,
      };
    } catch (error) {
      console.error('Error updating invoice status:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  },
});
