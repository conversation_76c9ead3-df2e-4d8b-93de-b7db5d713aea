import { tool } from 'ai';
import { z } from 'zod';
import db from '@/db/db';
import { getAuthenticatedUserId } from '@/lib/auth-helpers';

export const deleteInvoice = tool({
  description: 'Removes an invoice from the user\'s account, ensuring proper ownership checks.',
  parameters: z.object({
    invoiceId: z.string().describe('The ID of the invoice to delete'),
    softDelete: z.boolean().default(true).describe('Whether to perform a soft delete (true) or hard delete (false)'),
  }),
  execute: async ({ invoiceId, softDelete }) => {
    try {
      // Get the authenticated user ID
      const userId = await getAuthenticatedUserId();

      // Find the invoice in the database - try both by ID and by invoice number
      let invoice = await db.invoice.findUnique({
        where: { id: invoiceId },
      });

      // If not found by ID, try to find by invoice number
      if (!invoice) {
        invoice = await db.invoice.findFirst({
          where: {
            invoiceNumber: invoiceId,
            userId
          },
        });
      }

      if (!invoice) {
        return {
          success: false,
          message: `Invoice with ID or number ${invoiceId} not found. Please check the ID and try again.`,
        };
      }

      // Check if the invoice belongs to the current user
      if (invoice.userId !== userId) {
        return {
          success: false,
          message: 'You do not have permission to delete this invoice.',
        };
      }

      if (softDelete) {
        // Soft delete - mark the invoice as CANCELLED
        const updatedInvoice = await db.invoice.update({
          where: { id: invoiceId },
          data: {
            status: 'CANCELLED',
            updatedAt: new Date(),
          },
        });

        return {
          id: updatedInvoice.id,
          number: updatedInvoice.invoiceNumber || '',
          message: `Invoice ${updatedInvoice.invoiceNumber || ''} has been soft-deleted (marked as CANCELLED).`,
        };
      } else {
        // Hard delete - remove from the database
        // First, delete all line items (this should be handled by cascading delete in the schema)
        // Then delete the invoice
        await db.$transaction(async (prisma) => {
          // Remove from folders
          await prisma.folderInvoice.deleteMany({
            where: { invoiceId },
          });

          // Delete line items
          await prisma.invoiceLineItem.deleteMany({
            where: { invoiceId },
          });

          // Delete the invoice
          await prisma.invoice.delete({
            where: { id: invoiceId },
          });
        });

        return {
          id: invoiceId,
          number: invoice.invoiceNumber || '',
          message: `Invoice ${invoice.invoiceNumber || ''} has been permanently deleted.`,
        };
      }
    } catch (error) {
      console.error('Error deleting invoice:', error);
      return {
        success: false,
        message: 'Failed to delete invoice. Please try again.',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  },
});
