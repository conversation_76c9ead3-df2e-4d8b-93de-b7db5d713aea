import { toast } from 'sonner';

// Helper for getting base URL
function getBaseUrl() {
    return typeof window !== 'undefined' ? window.location.origin : '';
}

// Types for different data entities
export interface UserData {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    role: string;
    organization?: {
        id: string;
        name: string;
    };
    createdAt: string;
    updatedAt: string;
}

export interface InvoiceStats {
    total: number;
    paid: number;
    unpaid: number;
    overdue: number;
    totalAmount: number;
    paidAmount: number;
    unpaidAmount: number;
    currency: string;
}

export interface SubscriptionData {
    id: string;
    status: string;
    plan: {
        name: string;
        price: number;
        currency: string;
        billingInterval: string;
    };
    startDate: string;
    endDate?: string;
    renewalDate?: string;
    isActive: boolean;
}

export interface ReportSummary {
    total: number;
    recent: Array<{
        id: string;
        title: string;
        createdAt: string;
    }>;
}

export interface DocumentSummary {
    totalCount: number;
    categories: Record<string, number>;
    recentDocuments: Array<{
        id: string;
        name: string;
        category: string;
        uploadedAt: string;
    }>;
}

export interface TeamMember {
    id: string;
    name: string;
    email: string;
    role: string;
    status: string;
}

export interface Category {
    id: string;
    name: string;
    count: number;
}

export interface AnalyticsSummary {
    totalRevenue: number;
    totalExpenses: number;
    netIncome: number;
    currency: string;
    recentTrends: {
        revenue: number[];
        expenses: number[];
        periods: string[];
    };
}

// Main data interface that combines all data types
export interface UserContextData {
    userData: UserData | null;
    invoiceStats: InvoiceStats | null;
    subscription: SubscriptionData | null;
    reportSummary: ReportSummary | null;
    documentSummary: DocumentSummary | null;
    teamMembers: TeamMember[] | null;
    categories: Category[] | null;
    analyticsSummary: AnalyticsSummary | null;
    lastSyncTime: Date;
}

// Function to get base URL helper
function getApiUrl() {
    return `${getBaseUrl()}/api`;
}

// Initial empty data structure
const emptyContextData: UserContextData = {
    userData: null,
    invoiceStats: null,
    subscription: null,
    reportSummary: null,
    documentSummary: null,
    teamMembers: null,
    categories: null,
    analyticsSummary: null,
    lastSyncTime: new Date(),
};

/**
 * Fetches all user data from various endpoints and combines it into a single context object
 */
export async function syncAllUserData(): Promise<UserContextData> {
    try {
        // Start with empty context
        const context: UserContextData = { ...emptyContextData, lastSyncTime: new Date() };

        // Fetch data in parallel for efficiency
        const [
            userData,
            invoiceStats,
            subscription,
            reportSummary,
            documentSummary,
            teamMembers,
            categories,
            analyticsSummary
        ] = await Promise.allSettled([
            fetchUserData(),
            fetchInvoiceStats(),
            fetchSubscription(),
            fetchReportSummary(),
            fetchDocumentSummary(),
            fetchTeamMembers(),
            fetchCategories(),
            fetchAnalyticsSummary()
        ]);

        // Populate data that was successfully fetched
        if (userData.status === 'fulfilled') context.userData = userData.value;
        if (invoiceStats.status === 'fulfilled') context.invoiceStats = invoiceStats.value;
        if (subscription.status === 'fulfilled') context.subscription = subscription.value;
        if (reportSummary.status === 'fulfilled') context.reportSummary = reportSummary.value;
        if (documentSummary.status === 'fulfilled') context.documentSummary = documentSummary.value;
        if (teamMembers.status === 'fulfilled') context.teamMembers = teamMembers.value;
        if (categories.status === 'fulfilled') context.categories = categories.value;
        if (analyticsSummary.status === 'fulfilled') context.analyticsSummary = analyticsSummary.value;

        return context;
    } catch (error) {
        toast.error('Could not sync all user data. Some information may be missing.');
        return { ...emptyContextData, lastSyncTime: new Date() };
    }
}

// Individual data fetching functions

async function fetchUserData(): Promise<UserData | null> {
    try {
        const response = await fetch(`${getApiUrl()}/user/profile`);
        if (!response.ok) throw new Error(`Error: ${response.status}`);
        return await response.json();
    } catch (error) {
        return null;
    }
}

async function fetchInvoiceStats(): Promise<InvoiceStats | null> {
    try {
        const response = await fetch(`${getApiUrl()}/invoices/stats`);
        if (!response.ok) throw new Error(`Error: ${response.status}`);
        return await response.json();
    } catch (error) {
        return null;
    }
}

async function fetchSubscription(): Promise<SubscriptionData | null> {
    try {
        const response = await fetch(`${getApiUrl()}/subscriptions/active`);
        if (!response.ok) throw new Error(`Error: ${response.status}`);
        return await response.json();
    } catch (error) {
        return null;
    }
}

async function fetchReportSummary(): Promise<ReportSummary | null> {
    try {
        const response = await fetch(`${getApiUrl()}/reports/summary`);
        if (!response.ok) throw new Error(`Error: ${response.status}`);
        return await response.json();
    } catch (error) {
        return null;
    }
}

async function fetchDocumentSummary(): Promise<DocumentSummary | null> {
    try {
        const response = await fetch(`${getApiUrl()}/uploads/summary`);
        if (!response.ok) throw new Error(`Error: ${response.status}`);
        return await response.json();
    } catch (error) {
        return null;
    }
}

async function fetchTeamMembers(): Promise<TeamMember[] | null> {
    try {
        const response = await fetch(`${getApiUrl()}/team/members`);
        if (!response.ok) throw new Error(`Error: ${response.status}`);
        return await response.json();
    } catch (error) {
        return null;
    }
}

async function fetchCategories(): Promise<Category[] | null> {
    try {
        const response = await fetch(`${getApiUrl()}/categories`);
        if (!response.ok) throw new Error(`Error: ${response.status}`);
        return await response.json();
    } catch (error) {
        return null;
    }
}

async function fetchAnalyticsSummary(): Promise<AnalyticsSummary | null> {
    try {
        const response = await fetch(`${getApiUrl()}/financial-analytics/summary`);
        if (!response.ok) throw new Error(`Error: ${response.status}`);
        return await response.json();
    } catch (error) {
        return null;
    }
}

/**
 * Fetches detailed information about a specific entity type
 */
export async function fetchDetailedData<T>(
    dataType: 'invoices' | 'reports' | 'documents' | 'team' | 'analytics',
    queryParams?: Record<string, string>
): Promise<T | null> {
    try {
        const queryString = queryParams
            ? `?${new URLSearchParams(queryParams).toString()}`
            : '';

        const response = await fetch(`${getApiUrl()}/${dataType}${queryString}`);

        if (!response.ok) throw new Error(`Error: ${response.status}`);

        return await response.json();
    } catch (error) {
        return null;
    }
}

// Interface for ensuring data has an ID field
interface WithId {
    id: string;
}

/**
 * Executes a data operation like creating or updating an entity
 */
export async function executeDataOperation<T, R>(
    operationType: 'create',
    entityType: 'invoice' | 'report' | 'document' | 'category' | 'team-member',
    data: T
): Promise<R>;
export async function executeDataOperation<T extends WithId, R>(
    operationType: 'update' | 'delete',
    entityType: 'invoice' | 'report' | 'document' | 'category' | 'team-member',
    data: T
): Promise<R>;
export async function executeDataOperation<T, R>(
    operationType: 'create' | 'update' | 'delete',
    entityType: 'invoice' | 'report' | 'document' | 'category' | 'team-member',
    data: T
): Promise<R> {
    try {
        let method: string;
        let endpoint: string = `${getApiUrl()}/${entityType.includes('-') ? entityType.replace('-', '/') : entityType}s`;

        switch (operationType) {
            case 'create':
                method = 'POST';
                break;
            case 'update':
                method = 'PUT';
                // TypeScript will know that data has an id for 'update' and 'delete' operations
                // because of the function overload and type constraint
                endpoint = `${endpoint}/${(data as WithId).id}`;
                break;
            case 'delete':
                method = 'DELETE';
                endpoint = `${endpoint}/${(data as WithId).id}`;
                break;
            default:
                throw new Error(`Unknown operation type: ${operationType}`);
        }

        const response = await fetch(endpoint, {
            method,
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data),
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Error: ${response.status} - ${errorText}`);
        }

        return await response.json() as R;
    } catch (error) {
        throw error;
    }
}