import { TeamInviteEmailTemplate } from './templates/team-invite-email';
import { UserRole } from '@prisma/client';
import { ScheduledReportEmailTemplate } from './templates/scheduled-report-email';
import { FailedReportEmailTemplate } from './templates/failed-report-email';
// WAITLIST DISABLED FOR PRODUCTION TESTING
// import { WaitlistEmailTemplate } from './templates/waitlist-email';
import { z } from 'zod';
import { getResend } from './resend';

// Don't initialize Resend at the module level
// This is now handled by the getResend helper in resend.ts

const fromEmail = process.env.EMAIL_FROM || '<EMAIL>';

export const NewsletterFormSchema = z.object({
  email: z.string().email('Invalid email.')
})

type NewsletterFormInputs = z.infer<typeof NewsletterFormSchema>

// Interface for team invitation email
interface TeamInviteEmailProps {
  email: string;
  inviterName: string;
  organizationName: string;
  role: UserRole;
  token: string;
  expiresAt: Date;
}

// Send team invitation email
export async function sendTeamInviteEmail(props: TeamInviteEmailProps) {
  const { email, inviterName, organizationName, role, token } = props;

  // Create invite URL
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
  const inviteUrl = `${baseUrl}/accept-invite?token=${token}`;

  // Format role name for display
  const roleName = role.charAt(0) + role.slice(1).toLowerCase();

  try {
    // Only initialize Resend when the function is called
    const resend = getResend();

    const { data, error } = await resend.emails.send({
      from: `${organizationName} <${fromEmail}>`,
      to: email,
      subject: `Invitation to join ${organizationName}`,
      react: TeamInviteEmailTemplate({
        inviterName,
        organizationName,
        role: roleName,
        inviteUrl,
        expiresAt: props.expiresAt
      }),
    });

    if (error) {
      console.error('Error sending invite email:', error);
      throw new Error(`Failed to send invite email: ${error.message}`);
    }

    return { success: true, messageId: data?.id };
  } catch (error) {
    console.error('Error in sendTeamInviteEmail:', error);
    throw error;
  }
}

interface EmailOptions {
  recipients: string[];
  reportName: string;
  pdfUrl?: string;
  excelUrl?: string;
  userName: string;
  cc?: string[];
  bcc?: string[];
  priority?: 'high' | 'normal' | 'low';
  customSubject?: string;
  customMessage?: string;
  includeCompanyLogo?: boolean;
  emailTemplate?: 'standard' | 'professional' | 'minimal';
  emailColor?: string; // primary color for email template
  includeMetadata?: boolean; // include report metadata like generation date
  replyTo?: string;
}

interface ErrorNotificationOptions {
  recipients: string[];
  reportName: string;
  errorMessage: string;
  userName: string;
  reportId?: string;
  scheduledTime?: Date;
}

export async function sendReportEmail(options: EmailOptions) {
  const {
    recipients,
    reportName,
    pdfUrl,
    excelUrl,
    userName,
    cc = [],
    bcc = [],
    customSubject,
    customMessage,
    includeCompanyLogo = true,
    emailTemplate = 'professional',
    emailColor = '#3C5A99', // default Bilix brand color
    includeMetadata = true,
    replyTo,
  } = options;

  // Validate inputs
  if (!recipients.length) {
    throw new Error('At least one recipient is required');
  }

  try {
    // Only initialize Resend when the function is called
    const resend = getResend();

    // Create email composition
    const emailData = {
      from: `Billix Reports <${process.env.EMAIL_FROM || '<<EMAIL>>'}>`,
      to: recipients,
      cc: cc.length ? cc : undefined,
      bcc: bcc.length ? bcc : undefined,
      reply_to: replyTo || process.env.EMAIL_REPLY_TO,
      subject: customSubject || `Your Report: ${reportName} is Ready`,
      react: ScheduledReportEmailTemplate({
        reportName,
        pdfUrl,
        excelUrl,
        userName,
        customMessage,
        includeCompanyLogo,
        templateStyle: emailTemplate,
        primaryColor: emailColor,
        includeMetadata,
        generatedDate: new Date(),
      }),
    };

    const { data, error } = await resend.emails.send(emailData);

    if (error) {
      console.error('Error sending report email:', error);
      throw new Error(`Failed to send report email: ${error.message}`);
    }

    return { success: true, messageId: data?.id };
  } catch (error) {
    console.error('Failed to send report email:', error);
    throw new Error(`Failed to send report email: ${(error as Error).message}`);
  }
}

export async function sendErrorNotification(options: ErrorNotificationOptions) {
  const {
    recipients,
    reportName,
    errorMessage,
    userName,
    reportId,
    scheduledTime,
  } = options;

  try {
    // Only initialize Resend when the function is called
    const resend = getResend();

    const { data, error } = await resend.emails.send({
      from: `Billix Reports <${process.env.EMAIL_FROM || '<EMAIL>'}>`,
      to: recipients,
      subject: `Error Generating Report: ${reportName}`,
      react: FailedReportEmailTemplate({
        reportName,
        errorMessage,
        userName,
        reportId,
        scheduledTime,
      }),
    });

    if (error) {
      console.error('Error sending error notification:', error);
      throw new Error(`Failed to send error notification: ${error.message}`);
    }

    return { success: true, messageId: data?.id };
  } catch (error) {
    console.error('Failed to send error notification email:', error);
    throw new Error(`Failed to send error notification: ${(error as Error).message}`);
  }
}

export async function subscribe(data: NewsletterFormInputs) {
  const result = NewsletterFormSchema.safeParse(data)

  if (result.error) {
    return { error: result.error.format() }
  }

  try {
    const { email } = result.data

    // Only initialize Resend when the function is called
    const resend = getResend();

    // Add the contact to audience list
    const { data: contactData, error: contactError } = await resend.contacts.create({
      email: email,
      audienceId: process.env.RESEND_AUDIENCE_ID as string
    })

    if (!contactData || contactError) {
      throw new Error('Failed to subscribe')
    }

    // Use VERIFIED domain - HARDCODED to ensure it works
    // Add a delay to avoid rate limiting (429 errors)
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // WAITLIST DISABLED FOR PRODUCTION TESTING
    // const { error: emailError } = await resend.emails.send({
    //   from: '<EMAIL>', // Hardcoded verified domain
    //   to: email,
    //   subject: 'Welcome to the Billix AI Waitlist!',
    //   react: WaitlistEmailTemplate(),
    // });

    // if (emailError) {
    //   console.error('Error sending waitlist confirmation email:', emailError);
    // }

    return { success: true }
  } catch (error) {
    console.error('Subscription error:', error);
    return { error }
  }
}