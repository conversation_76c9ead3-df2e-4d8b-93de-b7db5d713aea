"use client";

import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
// Import client-safe report types
import { ReportType } from "@/lib/report-types";
import DashboardLayout from "@/components/layout/DashboardLayout";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  ArrowL<PERSON><PERSON>,
  Save,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { Ta<PERSON>, Ta<PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { DatePicker } from "@/components/ui/date-picker";
import { getReportById, updateReport, generateReportData } from "@/lib/actions/reports";
import { toast } from "sonner";

// Define the form schema
const formSchema = z.object({
  name: z.string().min(3, "Name must be at least 3 characters"),
  description: z.string().optional(),
  type: z.enum([
    ReportType.EXPENSES,
    ReportType.VENDOR_ANALYSIS,
    ReportType.CATEGORY_ANALYSIS,
    ReportType.CASH_FLOW,
    ReportType.SALES,
    ReportType.TAX,
    ReportType.CUSTOM,
  ]),
  startDate: z.date(),
  endDate: z.date(),
  visualizationType: z.string(),
});

export default function EditReportPage() {
  const params = useParams();
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const reportId = params.id as string;

  // Initialize the form
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      description: "",
      type: ReportType.EXPENSES,
      startDate: new Date(new Date().setDate(1)), // First day of current month
      endDate: new Date(), // Today
      visualizationType: "bar",
    },
  });

  useEffect(() => {
    async function fetchReport() {
      try {
        // console.log("Fetching report with ID:", reportId);
        const report = await getReportById(reportId);
        // console.log("Fetched report:", report);

        if (!report) {
          throw new Error("Report not found");
        }

        // Get date range from the report
        let dateRange = { startDate: new Date(), endDate: new Date() };

        if (report.dateRange) {
          try {
            // If dateRange is a string, parse it
            if (typeof report.dateRange === "string") {
              dateRange = JSON.parse(report.dateRange);
            } else {
              // Otherwise use it directly
              dateRange = {
                startDate: new Date(report.dateRange.startDate),
                endDate: new Date(report.dateRange.endDate)
              };
            }
          } catch (error) {
            console.error("Error parsing date range:", error);
          }
        } else if (report.startDate && report.endDate) {
          // If no dateRange but we have startDate and endDate, use those
          dateRange = {
            startDate: new Date(report.startDate),
            endDate: new Date(report.endDate)
          };
        } else {
          // Fallback to default date range
          dateRange = {
            startDate: new Date(new Date().setDate(1)), // First day of current month
            endDate: new Date() // Today
          };
        }

        // Handle both old and new field names
        const reportName = report.title || report.name || "";
        const reportType = report.reportType || report.type || ReportType.EXPENSES;
        const reportVisualizationType = report.visualizationType || "bar";
        const reportDescription = report.description || "";

        // console.log("Setting form values:", {
        //   name: reportName,
        //   description: reportDescription,
        //   type: reportType,
        //   startDate: new Date(dateRange.startDate),
        //   endDate: new Date(dateRange.endDate),
        //   visualizationType: reportVisualizationType,
        // });

        // Set form values
        form.reset({
          name: reportName,
          description: reportDescription,
          type: reportType as ReportType.EXPENSES | ReportType.VENDOR_ANALYSIS | ReportType.CATEGORY_ANALYSIS | ReportType.CASH_FLOW | ReportType.SALES | ReportType.TAX | ReportType.CUSTOM,
          startDate: new Date(dateRange.startDate),
          endDate: new Date(dateRange.endDate),
          visualizationType: reportVisualizationType,
        });
      } catch (error) {
        console.error("Failed to fetch report:", error);
        toast.error("Failed to load report data");
      } finally {
        setIsLoading(false);
      }
    }

    fetchReport();
  }, [reportId, form]);

  // Handle form submission
  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsSubmitting(true);

    // Show a loading toast
    const loadingToastId = toast.loading("Updating report...");

    try {
      // Update the report
      const dateRange = {
        startDate: values.startDate.toISOString(),
        endDate: values.endDate.toISOString(),
      };

      // Create update data with both old and new field names for compatibility
      const updateData = {
        id: reportId,
        name: values.name,
        title: values.name, // Add title for compatibility
        description: values.description,
        type: values.type,
        reportType: values.type, // Add reportType for compatibility
        dateRange,
        visualizationType: values.visualizationType,
      };

      await updateReport(updateData);

      // Regenerate the report data
      await generateReportData(reportId);

      // Dismiss the loading toast
      toast.dismiss(loadingToastId);

      toast.success("Report updated successfully");
      router.push(`/dashboard/reports/view/${reportId}`);
    } catch (error) {
      // Dismiss the loading toast
      toast.dismiss(loadingToastId);

      console.error("Error updating report:", error);
      toast.error("Failed to update report");
    } finally {
      setIsSubmitting(false);
    }
  }

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex h-screen items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="flex min-h-screen w-full flex-col">
        <header className="sticky top-0 z-10 border-b bg-background">
          <div className="flex h-16 items-center justify-between px-6">
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => router.back()}
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <h1 className="text-xl font-semibold">Edit Report</h1>
            </div>

            <Button
              type="submit"
              form="report-form"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Update Report
                </>
              )}
            </Button>
          </div>
        </header>

        <main className="flex-1 space-y-6 p-6 md:p-8">
          <Form {...form}>
            <form
              id="report-form"
              onSubmit={form.handleSubmit(onSubmit)}
              className="space-y-8"
            >
              <Card>
                <CardHeader>
                  <CardTitle>Report Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid gap-6 sm:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Report Name</FormLabel>
                          <FormControl>
                            <Input placeholder="Enter report name" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="type"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Report Type</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select report type" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {[
                                { value: ReportType.EXPENSES, label: "Expenses" },
                                { value: ReportType.VENDOR_ANALYSIS, label: "Vendor Analysis" },
                                { value: ReportType.CATEGORY_ANALYSIS, label: "Category Analysis" },
                                { value: ReportType.CASH_FLOW, label: "Cash Flow" },
                                { value: ReportType.SALES, label: "Sales" },
                                { value: ReportType.TAX, label: "Tax" },
                                { value: ReportType.CUSTOM, label: "Custom" }
                              ].map((item) => (
                                <SelectItem key={item.value} value={item.value}>
                                  {item.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Enter a description of this report"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <Separator />

                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Date Range</h3>
                    <div className="grid gap-6 sm:grid-cols-2">
                      <FormField
                        control={form.control}
                        name="startDate"
                        render={({ field }) => (
                          <FormItem className="flex flex-col">
                            <FormLabel>Start Date</FormLabel>
                            <DatePicker
                              date={field.value}
                              setDate={field.onChange}
                            />
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="endDate"
                        render={({ field }) => (
                          <FormItem className="flex flex-col">
                            <FormLabel>End Date</FormLabel>
                            <DatePicker
                              date={field.value}
                              setDate={field.onChange}
                            />
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Visualization</h3>
                    <FormField
                      control={form.control}
                      name="visualizationType"
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Tabs
                              value={field.value}
                              onValueChange={field.onChange}
                              className="w-full"
                            >
                              <TabsList className="grid w-full grid-cols-3">
                                <TabsTrigger value="bar" className="flex items-center gap-2">
                                  <BarChart3 className="h-4 w-4" />
                                  Bar Chart
                                </TabsTrigger>
                                <TabsTrigger value="line" className="flex items-center gap-2">
                                  <LineChart className="h-4 w-4" />
                                  Line Chart
                                </TabsTrigger>
                                <TabsTrigger value="pie" className="flex items-center gap-2">
                                  <PieChart className="h-4 w-4" />
                                  Pie Chart
                                </TabsTrigger>
                              </TabsList>
                            </Tabs>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </CardContent>
              </Card>
            </form>
          </Form>
        </main>
      </div>
    </DashboardLayout>
  );
}