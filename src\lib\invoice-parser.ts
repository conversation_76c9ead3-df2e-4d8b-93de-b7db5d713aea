import type { InvoiceData, LineItem } from '@/types/invoice';

// Currency to country mapping - Enhanced with more regions
const CURRENCY_TO_COUNTRY: Record<
  string,
  { country: string; countryCode: string; region: string }
> = {
  // North America
  USD: {
    country: 'United States',
    countryCode: 'US',
    region: 'Americas',
  },
  CAD: { country: 'Canada', countryCode: 'CA', region: 'Americas' },
  MXN: { country: 'Mexico', countryCode: 'MX', region: 'Americas' },

  // South America
  BRL: { country: 'Brazil', countryCode: 'BR', region: 'Americas' },
  ARS: {
    country: 'Argentina',
    countryCode: 'AR',
    region: 'Americas',
  },
  CLP: { country: 'Chile', countryCode: 'CL', region: 'Americas' },
  COP: { country: 'Colombia', countryCode: 'CO', region: 'Americas' },
  PEN: { country: 'Peru', countryCode: 'PE', region: 'Americas' },
  VES: {
    country: 'Venezuela',
    countryCode: 'VE',
    region: 'Americas',
  },

  // Europe
  EUR: {
    country: 'European Union',
    countryCode: 'EU',
    region: 'Europe',
  },
  GBP: {
    country: 'United Kingdom',
    countryCode: 'GB',
    region: 'Europe',
  },
  CHF: {
    country: 'Switzerland',
    countryCode: 'CH',
    region: 'Europe',
  },
  SEK: { country: 'Sweden', countryCode: 'SE', region: 'Europe' },
  NOK: { country: 'Norway', countryCode: 'NO', region: 'Europe' },
  DKK: { country: 'Denmark', countryCode: 'DK', region: 'Europe' },
  PLN: { country: 'Poland', countryCode: 'PL', region: 'Europe' },
  CZK: {
    country: 'Czech Republic',
    countryCode: 'CZ',
    region: 'Europe',
  },
  HUF: { country: 'Hungary', countryCode: 'HU', region: 'Europe' },
  RON: { country: 'Romania', countryCode: 'RO', region: 'Europe' },
  BGN: { country: 'Bulgaria', countryCode: 'BG', region: 'Europe' },
  HRK: { country: 'Croatia', countryCode: 'HR', region: 'Europe' },
  RSD: { country: 'Serbia', countryCode: 'RS', region: 'Europe' },
  TRY: { country: 'Turkey', countryCode: 'TR', region: 'Europe' },
  RUB: { country: 'Russia', countryCode: 'RU', region: 'Europe' },

  // Middle East & North Africa
  ILS: {
    country: 'Palestine/Israel',
    countryCode: 'PS',
    region: 'Arab',
  },
  AED: {
    country: 'United Arab Emirates',
    countryCode: 'AE',
    region: 'Arab',
  },
  SAR: { country: 'Saudi Arabia', countryCode: 'SA', region: 'Arab' },
  QAR: { country: 'Qatar', countryCode: 'QA', region: 'Arab' },
  BHD: { country: 'Bahrain', countryCode: 'BH', region: 'Arab' },
  KWD: { country: 'Kuwait', countryCode: 'KW', region: 'Arab' },
  OMR: { country: 'Oman', countryCode: 'OM', region: 'Arab' },
  JOD: { country: 'Jordan', countryCode: 'JO', region: 'Arab' },
  LBP: { country: 'Lebanon', countryCode: 'LB', region: 'Arab' },
  EGP: { country: 'Egypt', countryCode: 'EG', region: 'Arab' },
  MAD: { country: 'Morocco', countryCode: 'MA', region: 'Arab' },
  TND: { country: 'Tunisia', countryCode: 'TN', region: 'Arab' },
  DZD: { country: 'Algeria', countryCode: 'DZ', region: 'Arab' },
  LYD: { country: 'Libya', countryCode: 'LY', region: 'Arab' },

  // Asia Pacific
  JPY: { country: 'Japan', countryCode: 'JP', region: 'Asia' },
  CNY: { country: 'China', countryCode: 'CN', region: 'Asia' },
  HKD: { country: 'Hong Kong', countryCode: 'HK', region: 'Asia' },
  TWD: { country: 'Taiwan', countryCode: 'TW', region: 'Asia' },
  KRW: { country: 'South Korea', countryCode: 'KR', region: 'Asia' },
  SGD: { country: 'Singapore', countryCode: 'SG', region: 'Asia' },
  MYR: { country: 'Malaysia', countryCode: 'MY', region: 'Asia' },
  THB: { country: 'Thailand', countryCode: 'TH', region: 'Asia' },
  IDR: { country: 'Indonesia', countryCode: 'ID', region: 'Asia' },
  PHP: { country: 'Philippines', countryCode: 'PH', region: 'Asia' },
  VND: { country: 'Vietnam', countryCode: 'VN', region: 'Asia' },
  INR: { country: 'India', countryCode: 'IN', region: 'Asia' },
  PKR: { country: 'Pakistan', countryCode: 'PK', region: 'Asia' },
  BDT: { country: 'Bangladesh', countryCode: 'BD', region: 'Asia' },
  LKR: { country: 'Sri Lanka', countryCode: 'LK', region: 'Asia' },
  NPR: { country: 'Nepal', countryCode: 'NP', region: 'Asia' },

  // Oceania
  AUD: { country: 'Australia', countryCode: 'AU', region: 'Oceania' },
  NZD: {
    country: 'New Zealand',
    countryCode: 'NZ',
    region: 'Oceania',
  },
  FJD: { country: 'Fiji', countryCode: 'FJ', region: 'Oceania' },

  // Africa
  ZAR: {
    country: 'South Africa',
    countryCode: 'ZA',
    region: 'Africa',
  },
  NGN: { country: 'Nigeria', countryCode: 'NG', region: 'Africa' },
  KES: { country: 'Kenya', countryCode: 'KE', region: 'Africa' },
  GHS: { country: 'Ghana', countryCode: 'GH', region: 'Africa' },
  UGX: { country: 'Uganda', countryCode: 'UG', region: 'Africa' },
  TZS: { country: 'Tanzania', countryCode: 'TZ', region: 'Africa' },
  ETB: { country: 'Ethiopia', countryCode: 'ET', region: 'Africa' },
};

// Language detection mapping
const LANGUAGE_CODES: Record<string, string> = {
  en: 'English',
  ar: 'Arabic',
  fr: 'French',
  es: 'Spanish',
  de: 'German',
  zh: 'Chinese',
  ja: 'Japanese',
  ru: 'Russian',
  pt: 'Portuguese',
  it: 'Italian',
  nl: 'Dutch',
  he: 'Hebrew',
  tr: 'Turkish',
};

// Business categories with examples for inference
const BUSINESS_CATEGORIES = {
  Technology: [
    'software',
    'computer',
    'it services',
    'digital',
    'cloud',
    'web',
    'tech',
    'hosting',
    'development',
  ],
  'Food & Beverage': [
    'restaurant',
    'catering',
    'cafe',
    'food',
    'grocery',
    'bakery',
    'dining',
  ],
  Healthcare: [
    'hospital',
    'clinic',
    'medical',
    'health',
    'doctor',
    'pharmacy',
    'dental',
  ],
  Hospitality: [
    'hotel',
    'accommodation',
    'resort',
    'lodging',
    'motel',
    'inn',
  ],
  Marketing: [
    'advertising',
    'media',
    'promotion',
    'pr',
    'creative',
    'design',
  ],
  'Legal Services': [
    'law firm',
    'legal',
    'attorney',
    'advocate',
    'solicitor',
    'counsel',
  ],
  Education: [
    'school',
    'university',
    'college',
    'training',
    'academy',
    'teaching',
  ],
  Construction: [
    'building',
    'contractor',
    'renovation',
    'construction',
    'architecture',
  ],
  Transportation: [
    'logistics',
    'shipping',
    'freight',
    'delivery',
    'transport',
  ],
  Retail: [
    'store',
    'shop',
    'retail',
    'merchandise',
    'product',
    'outlet',
  ],
  'Financial Services': [
    'banking',
    'finance',
    'investment',
    'insurance',
    'accounting',
  ],
  Consulting: [
    'consultant',
    'advisory',
    'expertise',
    'business services',
  ],
  Manufacturing: [
    'factory',
    'production',
    'industrial',
    'assembly',
    'fabrication',
  ],
  'Real Estate': [
    'property',
    'realty',
    'leasing',
    'housing',
    'commercial property',
  ],
  Utilities: ['electricity', 'water', 'gas', 'energy', 'power'],
  Telecommunications: [
    'telecom',
    'communications',
    'phone',
    'internet',
    'network',
  ],
  Entertainment: [
    'entertainment',
    'event',
    'venue',
    'media',
    'production',
  ],
};

// Enhanced tax rate system for different countries and regions
const COUNTRY_TAX_RATES: Record<
  string,
  {
    taxName: string;
    defaultRate: number;
    additionalTaxes?: Array<{ name: string; rate: number }>;
    taxExemptions?: Array<string>;
    taxRules?: Array<string>;
  }
> = {
  // North America
  US: {
    taxName: 'Sales Tax',
    defaultRate: 0.0825, // Varies by state, this is average
    additionalTaxes: [
      { name: 'County Tax', rate: 0.01 },
      { name: 'City Tax', rate: 0.01 },
    ],
    taxExemptions: ['Food', 'Medicine', 'Education', 'Non-profit'],
    taxRules: [
      'Nexus rules apply',
      'Digital goods taxable in some states',
    ],
  },
  CA: {
    taxName: 'GST/HST',
    defaultRate: 0.13, // Varies by province
    additionalTaxes: [
      { name: 'PST/QST', rate: 0.09 }, // Provincial sales tax varies
    ],
    taxExemptions: ['Basic groceries', 'Medical devices', 'Exports'],
  },
  MX: {
    taxName: 'IVA',
    defaultRate: 0.16,
    additionalTaxes: [
      { name: 'IEPS', rate: 0.03 }, // Special tax on production and services
    ],
  },

  // South America
  BR: {
    taxName: 'ICMS',
    defaultRate: 0.18, // Varies by state
    additionalTaxes: [
      { name: 'ISS', rate: 0.05 }, // Service tax
      { name: 'PIS/COFINS', rate: 0.0925 }, // Social contribution taxes
    ],
  },
  AR: { taxName: 'IVA', defaultRate: 0.21 },
  CL: { taxName: 'IVA', defaultRate: 0.19 },
  CO: { taxName: 'IVA', defaultRate: 0.19 },
  PE: { taxName: 'IGV', defaultRate: 0.18 },

  // Europe
  EU: {
    taxName: 'VAT',
    defaultRate: 0.21, // Average EU VAT
    taxRules: [
      'Reverse charge mechanism applies for B2B',
      'One Stop Shop (OSS) for digital services',
    ],
  },
  GB: {
    taxName: 'VAT',
    defaultRate: 0.2, // UK
    taxExemptions: [
      'Food',
      "Children's clothes",
      'Books',
      'Medical supplies',
    ],
  },
  DE: {
    taxName: 'VAT',
    defaultRate: 0.19,
    additionalTaxes: [
      { name: 'Reduced VAT', rate: 0.07 }, // For food, books, etc.
    ],
  },
  FR: {
    taxName: 'VAT',
    defaultRate: 0.2,
    additionalTaxes: [
      { name: 'Reduced VAT', rate: 0.055 }, // For food, books, etc.
    ],
  },
  IT: { taxName: 'VAT', defaultRate: 0.22 },
  ES: { taxName: 'VAT', defaultRate: 0.21 },
  CH: { taxName: 'VAT', defaultRate: 0.077 },
  SE: { taxName: 'VAT', defaultRate: 0.25 },
  NO: { taxName: 'MVA', defaultRate: 0.25 },
  DK: { taxName: 'MOMS', defaultRate: 0.25 },
  PL: { taxName: 'VAT', defaultRate: 0.23 },
  CZ: { taxName: 'DPH', defaultRate: 0.21 },
  HU: { taxName: 'ÁFA', defaultRate: 0.27 },
  RO: { taxName: 'TVA', defaultRate: 0.19 },
  BG: { taxName: 'DDS', defaultRate: 0.2 },
  HR: { taxName: 'PDV', defaultRate: 0.25 },
  RS: { taxName: 'PDV', defaultRate: 0.2 },
  TR: { taxName: 'KDV', defaultRate: 0.18 },
  RU: { taxName: 'VAT', defaultRate: 0.2 },

  // Middle East & North Africa
  PS: {
    taxName: 'VAT',
    defaultRate: 0.17, // Palestine
    taxRules: ['Special rules for West Bank and Gaza'],
  },
  IL: {
    taxName: 'VAT',
    defaultRate: 0.17, // Israel
    taxRules: ['Zero-rated for tourists'],
  },
  AE: {
    taxName: 'VAT',
    defaultRate: 0.05,
    taxExemptions: [
      'Healthcare',
      'Education',
      'Certain financial services',
    ],
  },
  SA: {
    taxName: 'VAT',
    defaultRate: 0.15,
    taxRules: ['Reverse charge for non-resident suppliers'],
  },
  QA: { taxName: 'VAT', defaultRate: 0.05 },
  BH: { taxName: 'VAT', defaultRate: 0.1 },
  KW: { taxName: 'No VAT', defaultRate: 0.0 }, // Kuwait has no VAT yet
  OM: { taxName: 'VAT', defaultRate: 0.05 },
  JO: { taxName: 'GST', defaultRate: 0.16 },
  LB: { taxName: 'VAT', defaultRate: 0.11 },
  EG: { taxName: 'VAT', defaultRate: 0.14 },
  MA: { taxName: 'TVA', defaultRate: 0.2 },
  TN: { taxName: 'TVA', defaultRate: 0.19 },
  DZ: { taxName: 'TVA', defaultRate: 0.19 },
  LY: { taxName: 'No VAT', defaultRate: 0.0 }, // Libya has no VAT system

  // Asia Pacific
  JP: {
    taxName: 'Consumption Tax',
    defaultRate: 0.1,
    taxRules: ['Reduced rate of 8% for food and beverages'],
  },
  CN: {
    taxName: 'VAT',
    defaultRate: 0.13,
    additionalTaxes: [
      { name: 'Reduced VAT', rate: 0.09 }, // For specific industries
      { name: 'Small-scale VAT', rate: 0.03 }, // For small businesses
    ],
  },
  HK: { taxName: 'No VAT/GST', defaultRate: 0.0 }, // Hong Kong has no VAT/GST
  TW: { taxName: 'VAT', defaultRate: 0.05 },
  KR: { taxName: 'VAT', defaultRate: 0.1 },
  SG: {
    taxName: 'GST',
    defaultRate: 0.08,
    taxRules: ['Overseas vendor registration for digital services'],
  },
  MY: { taxName: 'SST', defaultRate: 0.1 }, // Sales and Service Tax
  TH: { taxName: 'VAT', defaultRate: 0.07 },
  ID: { taxName: 'VAT', defaultRate: 0.11 },
  PH: { taxName: 'VAT', defaultRate: 0.12 },
  VN: { taxName: 'VAT', defaultRate: 0.1 },
  IN: {
    taxName: 'GST',
    defaultRate: 0.18, // Varies by product
    additionalTaxes: [
      { name: 'CGST', rate: 0.09 }, // Central GST
      { name: 'SGST', rate: 0.09 }, // State GST
    ],
    taxRules: [
      'IGST for interstate transactions',
      'Composition scheme for small businesses',
    ],
  },
  PK: { taxName: 'GST', defaultRate: 0.17 },
  BD: { taxName: 'VAT', defaultRate: 0.15 },
  LK: { taxName: 'VAT', defaultRate: 0.15 },
  NP: { taxName: 'VAT', defaultRate: 0.13 },

  // Oceania
  AU: {
    taxName: 'GST',
    defaultRate: 0.1,
    taxExemptions: [
      'Basic food',
      'Healthcare',
      'Education',
      'Exports',
    ],
  },
  NZ: {
    taxName: 'GST',
    defaultRate: 0.15,
    taxRules: [
      'Applies to most goods and services with few exemptions',
    ],
  },
  FJ: { taxName: 'VAT', defaultRate: 0.09 },

  // Africa
  ZA: { taxName: 'VAT', defaultRate: 0.15 },
  NG: { taxName: 'VAT', defaultRate: 0.075 },
  KE: { taxName: 'VAT', defaultRate: 0.16 },
  GH: { taxName: 'VAT', defaultRate: 0.125 },
  UG: { taxName: 'VAT', defaultRate: 0.18 },
  TZ: { taxName: 'VAT', defaultRate: 0.18 },
  ET: { taxName: 'VAT', defaultRate: 0.15 },
};

/**
 * Parse invoice text into structured data with enhanced capabilities:
 * - Language detection
 * - Confidence scores
 * - Financial auditing
 * - Tax accounting for various regions
 * - Fraud detection
 */
export async function parseInvoiceData(
  text: string
): Promise<InvoiceData> {
  const startTime = Date.now();

  try {
    // Use external invoice extraction API with backend URL from environment
    const backendUrl = process.env.BACKEND_URL;

    if (!backendUrl) {
      throw new Error(
        'BACKEND_URL environment variable is not configured'
      );
    }

    console.log('🔍 Calling backend /extract/invoice endpoint...');
    console.log('📄 Text length:', text.length, 'characters');

    const response = await fetch(`${backendUrl}/extract/invoice`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        text: text,
      }),
    });

    if (!response.ok) {
      console.error(
        '❌ Backend API call failed:',
        response.status,
        response.statusText
      );
      throw new Error(
        `API call failed: ${response.status} ${response.statusText}`
      );
    }

    const apiResponseText = await response.text();
    console.log(
      '📝 Backend response length:',
      apiResponseText.length,
      'characters'
    );

    // Check if this is a backend error response (JSON parsing failure)
    if (
      apiResponseText.includes('"detail":') &&
      apiResponseText.includes('Invalid json output')
    ) {
      console.error('🚨 Backend JSON parsing error detected');
      console.error('Raw backend error response:', apiResponseText);

      // Try to extract the malformed JSON from the error message
      const jsonMatch = apiResponseText.match(
        /```json\n([\s\S]*?)\n```/
      );
      if (jsonMatch && jsonMatch[1]) {
        console.log(
          '🔧 Attempting to fix malformed JSON from backend...'
        );
        try {
          // Try to fix common JSON issues
          let fixedJson = jsonMatch[1]
            .replace(/\n/g, ' ') // Remove newlines
            .replace(/\s+/g, ' ') // Normalize whitespace
            .replace(/,\s*}/g, '}') // Remove trailing commas
            .replace(/,\s*]/g, ']') // Remove trailing commas in arrays
            .replace(/"\s*"/g, '""') // Fix empty quotes
            .replace(/"\s*([^"]*?)\s*"/g, '"$1"') // Trim quoted strings
            .trim();

          console.log(
            '🔧 Fixed JSON attempt:',
            fixedJson.substring(0, 200) + '...'
          );
          const parsedData = JSON.parse(fixedJson) as InvoiceData;

          console.log(
            '✅ Successfully recovered from backend JSON error'
          );
          console.log(
            '📋 Line items recovered:',
            parsedData.lineItems?.length || 0
          );

          // Ensure lineItems exists
          if (!parsedData.lineItems) {
            parsedData.lineItems = [];
          }

          // Add metadata about the recovery
          if (!parsedData.meta) parsedData.meta = {};
          parsedData.meta.backendParsingIssue = true;
          parsedData.meta.recoveredFromError = true;

          return processInvoiceData(parsedData, text, startTime);
        } catch (fixError) {
          console.error(
            '❌ Failed to fix malformed JSON:',
            fixError instanceof Error
              ? fixError.message
              : String(fixError)
          );
        }
      }

      // If we can't fix the JSON, throw a descriptive error
      throw new Error(
        'Backend failed to parse invoice structure. This often happens with complex line item tables. The invoice basic data may still be extractable, but line items extraction failed.'
      );
    }

    // Normal processing for successful responses
    const jsonStr = apiResponseText
      .trim()
      .replace(/```json|```/g, '')
      .trim();

    console.log('📊 Processing successful backend response...');

    // Parse the JSON response
    try {
      const invoiceData = JSON.parse(jsonStr) as InvoiceData;

      console.log('✅ Successfully parsed backend JSON');
      console.log(
        '📋 Line items found:',
        invoiceData.lineItems?.length || 0
      );

      // Log line items structure for debugging
      if (invoiceData.lineItems && invoiceData.lineItems.length > 0) {
        console.log(
          '📋 Line items sample:',
          invoiceData.lineItems[0]
        );
        console.log(
          '📋 Field names found:',
          Object.keys(invoiceData.lineItems[0])
        );
      } else {
        console.log('⚠️  No line items found in backend response');
        // Check if line items might be in other locations
        if (invoiceData.financials) {
          console.log(
            '🔍 Checking financials object for line items...'
          );
        }
      }

      // Ensure lineItems exists as an array
      if (!invoiceData.lineItems) {
        invoiceData.lineItems = [];
      }

      return processInvoiceData(invoiceData, text, startTime);
    } catch (parseError) {
      console.error('❌ Failed to parse backend JSON response');
      console.error(
        'Parse error:',
        parseError instanceof Error
          ? parseError.message
          : String(parseError)
      );
      console.log(
        'Raw response that failed to parse:',
        apiResponseText.substring(0, 500) + '...'
      );

      // If parsing fails, try to extract a JSON object from the text
      const jsonMatch = apiResponseText.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        try {
          const extractedData = JSON.parse(
            jsonMatch[0]
          ) as InvoiceData;
          console.log('✅ Recovered data using regex extraction');

          // Ensure lineItems exists
          if (!extractedData.lineItems) {
            extractedData.lineItems = [];
          }

          // Add metadata about the recovery
          if (!extractedData.meta) extractedData.meta = {};
          extractedData.meta.backendParsingIssue = true;
          extractedData.meta.recoveredFromRegex = true;

          return processInvoiceData(extractedData, text, startTime);
        } catch (secondParseError) {
          console.error(
            '❌ Regex extraction also failed:',
            secondParseError instanceof Error
              ? secondParseError.message
              : String(secondParseError)
          );
          throw new Error(
            'Failed to parse the extracted data from backend. Backend response may be malformed.'
          );
        }
      }

      throw new Error(
        'Failed to parse the extracted data from backend. No valid JSON found in response.'
      );
    }
  } catch (error) {
    console.error(
      '❌ Invoice parsing error:',
      error instanceof Error ? error.message : String(error)
    );
    throw error;
  }
}

/**
 * Process the invoice data after successful parsing
 */
function processInvoiceData(
  invoiceData: InvoiceData,
  originalText: string,
  startTime: number
): InvoiceData {
  // Process country and tax information
  if (invoiceData.financials) {
    let countryCode = invoiceData.meta?.countryCode;

    // Enhance country detection based on currency if not already detected
    if (
      invoiceData.financials?.currency &&
      (!invoiceData.meta?.country || !invoiceData.meta?.countryCode)
    ) {
      const currencyCode = invoiceData.financials.currency
        .trim()
        .toUpperCase();
      const extractedCurrency = Object.keys(CURRENCY_TO_COUNTRY).find(
        (code) =>
          currencyCode.includes(code) ||
          currencyCode.includes(code.toLowerCase())
      );

      if (
        extractedCurrency &&
        CURRENCY_TO_COUNTRY[extractedCurrency]
      ) {
        if (!invoiceData.meta) invoiceData.meta = {};
        invoiceData.meta.country =
          invoiceData.meta.country ||
          CURRENCY_TO_COUNTRY[extractedCurrency].country;
        invoiceData.meta.countryCode =
          invoiceData.meta.countryCode ||
          CURRENCY_TO_COUNTRY[extractedCurrency].countryCode;
        countryCode = invoiceData.meta.countryCode;
      }
    }

    // If we have a tax amount but no tax rate, or vice versa, calculate the missing value
    if (countryCode && COUNTRY_TAX_RATES[countryCode]) {
      const defaultTaxInfo = COUNTRY_TAX_RATES[countryCode];

      // Add tax name if missing
      if (!invoiceData.financials.taxName && defaultTaxInfo.taxName) {
        invoiceData.financials.taxName = defaultTaxInfo.taxName;
      }

      // Calculate tax rate or amount if one is missing
      if (invoiceData.financials.subtotal) {
        const subtotal = parseFloat(
          String(invoiceData.financials.subtotal).replace(
            /[^0-9.-]+/g,
            ''
          )
        );

        if (!isNaN(subtotal)) {
          // If we have tax amount but no rate
          if (
            invoiceData.financials.tax &&
            !invoiceData.financials.taxRate
          ) {
            const taxAmount = parseFloat(
              String(invoiceData.financials.tax).replace(
                /[^0-9.-]+/g,
                ''
              )
            );
            if (!isNaN(taxAmount)) {
              const calculatedRate = (taxAmount / subtotal) * 100;
              invoiceData.financials.taxRate = `${calculatedRate.toFixed(2)}%`;
            }
          }
          // If we have tax rate but no amount
          else if (
            invoiceData.financials.taxRate &&
            !invoiceData.financials.tax
          ) {
            const rateStr = String(
              invoiceData.financials.taxRate
            ).replace(/[^0-9.-]+/g, '');
            let rate = parseFloat(rateStr);

            // Convert to decimal if it's a percentage
            if (
              rate > 1 &&
              !String(invoiceData.financials.taxRate).includes('.')
            ) {
              rate = rate / 100;
            }

            if (!isNaN(rate)) {
              const calculatedTax = subtotal * rate;
              // Format to match the currency format in the subtotal
              const formatter = new Intl.NumberFormat(
                invoiceData.meta?.language
                  ? `${invoiceData.meta.language}-${countryCode}`
                  : undefined,
                {
                  style: 'currency',
                  currency: invoiceData.financials.currency || 'USD',
                }
              );
              invoiceData.financials.tax =
                formatter.format(calculatedTax);
            }
          }
          // If neither tax rate nor amount, use default rate
          else if (
            !invoiceData.financials.tax &&
            !invoiceData.financials.taxRate
          ) {
            const defaultRate = defaultTaxInfo.defaultRate;
            const calculatedTax = subtotal * defaultRate;

            // Update only if we have a total to compare with
            const total = invoiceData.financials.total
              ? parseFloat(
                  String(invoiceData.financials.total).replace(
                    /[^0-9.-]+/g,
                    ''
                  )
                )
              : NaN;

            // Only set calculated tax if it's reasonably close to the difference between total and subtotal
            if (
              isNaN(total) ||
              Math.abs(total - subtotal - calculatedTax) <
                subtotal * 0.05
            ) {
              invoiceData.financials.taxRate = `${(defaultRate * 100).toFixed(2)}%`;

              // Format tax amount to match currency
              const formatter = new Intl.NumberFormat(
                invoiceData.meta?.language
                  ? `${invoiceData.meta.language}-${countryCode}`
                  : undefined,
                {
                  style: 'currency',
                  currency: invoiceData.financials.currency || 'USD',
                }
              );
              invoiceData.financials.tax =
                formatter.format(calculatedTax);
            }
          }
        }
      }
    }
  }

  // Enhance language name if we have the code
  if (invoiceData.meta?.language && !invoiceData.meta.languageName) {
    const langCode = invoiceData.meta.language
      .substring(0, 2)
      .toLowerCase();
    if (LANGUAGE_CODES[langCode]) {
      invoiceData.meta.languageName = LANGUAGE_CODES[langCode];
    }
  }

  // Ensure suggestions are present
  if (!invoiceData.meta) invoiceData.meta = {};
  if (!invoiceData.meta.suggestions)
    invoiceData.meta.suggestions = {};

  // If not enough category suggestions, generate more
  if (
    !invoiceData.meta.suggestions.categories ||
    !Array.isArray(invoiceData.meta.suggestions.categories) ||
    invoiceData.meta.suggestions.categories.length < 3
  ) {
    const inferredCategories = inferMultipleCategories(invoiceData);

    // Initialize or start with existing suggestions
    if (
      !invoiceData.meta.suggestions.categories ||
      !Array.isArray(invoiceData.meta.suggestions.categories)
    ) {
      invoiceData.meta.suggestions.categories = [];
    }

    // Only add categories that aren't already in the array
    const existingNames = new Set(
      invoiceData.meta.suggestions.categories.map((c) => c.name)
    );
    for (const category of inferredCategories) {
      if (!existingNames.has(category.name)) {
        invoiceData.meta.suggestions.categories.push(category);
        existingNames.add(category.name);
      }
    }

    // Ensure we have at least 3 categories
    if (invoiceData.meta.suggestions.categories.length < 3) {
      const backupCategories = [
        { name: 'Retail', confidence: 30 },
        { name: 'Business Services', confidence: 25 },
        { name: 'Technology', confidence: 20 },
        { name: 'Manufacturing', confidence: 15 },
        { name: 'Professional Services', confidence: 10 },
      ];

      for (const category of backupCategories) {
        if (!existingNames.has(category.name)) {
          invoiceData.meta.suggestions.categories.push(category);
          existingNames.add(category.name);
          if (invoiceData.meta.suggestions.categories.length >= 3)
            break;
        }
      }
    }

    // Sort by confidence
    invoiceData.meta.suggestions.categories.sort(
      (a, b) => b.confidence - a.confidence
    );
  }

  // Ensure vendor type suggestions
  if (
    !invoiceData.meta.suggestions.vendorTypes ||
    !Array.isArray(invoiceData.meta.suggestions.vendorTypes) ||
    invoiceData.meta.suggestions.vendorTypes.length < 2
  ) {
    const inferredVendorTypes = inferVendorTypes(invoiceData);

    // Initialize or start with existing suggestions
    if (
      !invoiceData.meta.suggestions.vendorTypes ||
      !Array.isArray(invoiceData.meta.suggestions.vendorTypes)
    ) {
      invoiceData.meta.suggestions.vendorTypes = [];
    }

    // Only add types that aren't already in the array
    const existingTypes = new Set(
      invoiceData.meta.suggestions.vendorTypes.map((t) => t.name)
    );
    for (const type of inferredVendorTypes) {
      if (!existingTypes.has(type.name)) {
        invoiceData.meta.suggestions.vendorTypes.push(type);
        existingTypes.add(type.name);
      }
    }

    // Sort by confidence
    invoiceData.meta.suggestions.vendorTypes.sort(
      (a, b) => b.confidence - a.confidence
    );
  }

  // Normalize confidence values to ensure realistic values
  if (invoiceData.meta.suggestions.categories) {
    invoiceData.meta.suggestions.categories =
      invoiceData.meta.suggestions.categories.map((cat) => {
        if (cat.confidence > 95) cat.confidence = 95;
        if (cat.confidence < 10) cat.confidence = 10;
        return cat;
      });
  }

  if (invoiceData.meta.suggestions.vendorTypes) {
    invoiceData.meta.suggestions.vendorTypes =
      invoiceData.meta.suggestions.vendorTypes.map((type) => {
        if (type.confidence > 95) type.confidence = 95;
        if (type.confidence < 10) type.confidence = 10;
        return type;
      });
  }

  // Initialize selection fields
  invoiceData.meta.suggestions.selectedCategory =
    invoiceData.meta.suggestions.selectedCategory || null;
  invoiceData.meta.suggestions.selectedVendorType =
    invoiceData.meta.suggestions.selectedVendorType || null;

  // Determine if this is an outgoing or incoming invoice based on organization context
  // This requires additional info about the user's organization which we'll handle elsewhere

  // Record processing time
  invoiceData.meta.processingTime = Date.now() - startTime;

  // Remove undefined, null, and empty string values
  return cleanInvoiceData(invoiceData, originalText);
}

/**
 * Remove null, undefined, and empty string values from the invoice data
 */
function cleanInvoiceData(
  data: InvoiceData,
  originalText: string
): InvoiceData {
  // Create a deep copy to avoid modifying the original
  const cleanedData: InvoiceData = {
    ...data,
    lineItems: Array.isArray(data.lineItems)
      ? [...data.lineItems]
      : [],
  };

  // Clean top-level fields
  Object.keys(cleanedData).forEach((key) => {
    if (key === 'lineItems') return; // Skip lineItems, we'll handle them separately

    const value = cleanedData[key];

    if (value === null || value === undefined || value === '') {
      delete cleanedData[key];
    } else if (typeof value === 'object') {
      // Clean nested objects
      const cleanedObj = cleanObjectValues(
        value as Record<string, unknown>
      );
      if (Object.keys(cleanedObj).length === 0) {
        delete cleanedData[key];
      } else {
        cleanedData[key] = cleanedObj;
      }
    }
  });

  // Clean line items
  if (Array.isArray(cleanedData.lineItems)) {
    cleanedData.lineItems = cleanedData.lineItems
      .map((item) => cleanObjectValues(item) as LineItem)
      .filter((item) => Object.keys(item).length > 0);
  }

  // If a vendor name is clearly present, remove vendor type suggestions
  if (
    cleanedData.vendor?.name &&
    cleanedData.vendor.name.trim().length > 0 &&
    cleanedData.meta?.suggestions?.vendorTypes &&
    cleanedData.meta.suggestions.vendorTypes.length > 0
  ) {
    // If vendor name is present, we shouldn't suggest vendor types
    if (!cleanedData.meta) cleanedData.meta = {};
    if (!cleanedData.meta.suggestions)
      cleanedData.meta.suggestions = {};

    // Replace vendor suggestions with just the actual vendor from the invoice
    cleanedData.meta.suggestions.vendorTypes = [
      {
        name: 'Vendor: ' + cleanedData.vendor.name,
        confidence: 98,
      },
    ];

    // Auto-select this vendor
    cleanedData.meta.suggestions.selectedVendorType =
      'Vendor: ' + cleanedData.vendor.name;
  }

  // Normalize confidence values to ensure realistic values
  if (
    cleanedData.meta?.suggestions?.categories &&
    Array.isArray(cleanedData.meta.suggestions.categories) &&
    cleanedData.meta.suggestions.categories.length > 0
  ) {
    cleanedData.meta.suggestions.categories =
      cleanedData.meta.suggestions.categories.map((cat, idx) => {
        // Keep confidence between 50-85 for more realistic values
        let confidence = Math.min(
          Math.max(Math.round(cat.confidence), 50),
          85
        );
        // Adjust confidence to avoid all having the same value
        if (idx === 0) {
          confidence = Math.min(confidence + 5, 85); // Top suggestion slightly higher
        }
        return { ...cat, confidence };
      });
  }

  if (
    cleanedData.meta?.suggestions?.vendorTypes &&
    Array.isArray(cleanedData.meta.suggestions.vendorTypes) &&
    cleanedData.meta.suggestions.vendorTypes.length > 0
  ) {
    cleanedData.meta.suggestions.vendorTypes =
      cleanedData.meta.suggestions.vendorTypes.map((type, idx) => {
        // Keep confidence between 50-85 for more realistic values
        let confidence = Math.min(
          Math.max(Math.round(type.confidence), 50),
          85
        );
        // Adjust confidence to avoid all having the same value
        if (idx === 0) {
          confidence = Math.min(confidence + 5, 85); // Top suggestion slightly higher
        }
        return { ...type, confidence };
      });
  }

  // Determine invoice type - add it if not already present
  if (!cleanedData.meta?.suggestions?.invoiceType) {
    // Default to PURCHASE (incoming invoice)
    let invoiceType: 'PURCHASE' | 'SALES' = 'PURCHASE';

    // Check for indications this is a SALES invoice (our org is sending it)
    const orgNames = [
      'your company',
      'our company',
      'your organization',
      'our organization',
    ];
    const orgSignatures = [
      'sincerely',
      'thank you for your business',
      'we appreciate your business',
    ];

    // If customer looks like an external entity and "we/our/your company" is mentioned, it's likely a SALES invoice
    if (
      cleanedData.customer?.name &&
      !orgNames.some((name) =>
        cleanedData.customer?.name?.toLowerCase().includes(name)
      ) &&
      (originalText
        .toLowerCase()
        .includes('invoice from your company') ||
        originalText
          .toLowerCase()
          .includes('invoice from our company') ||
        orgSignatures.some((sig) =>
          originalText.toLowerCase().includes(sig)
        ))
    ) {
      invoiceType = 'SALES';
    }

    if (!cleanedData.meta) cleanedData.meta = {};
    if (!cleanedData.meta.suggestions)
      cleanedData.meta.suggestions = {};

    // Add invoiceType to suggestions
    cleanedData.meta.suggestions.invoiceType = invoiceType;
  }

  return cleanedData;
}

/**
 * Clean empty values from an object
 */
function cleanObjectValues(
  obj: Record<string, unknown>
): Record<string, unknown> {
  if (!obj || typeof obj !== 'object' || Array.isArray(obj))
    return obj;

  const result: Record<string, unknown> = {};

  Object.entries(obj).forEach(([key, value]) => {
    // Skip null, undefined, and empty strings
    if (value === null || value === undefined || value === '') return;

    // Clean nested objects
    if (
      typeof value === 'object' &&
      value !== null &&
      !Array.isArray(value)
    ) {
      const cleanedValue = cleanObjectValues(
        value as Record<string, unknown>
      );
      if (Object.keys(cleanedValue).length > 0) {
        result[key] = cleanedValue;
      }
    } else if (Array.isArray(value)) {
      // Clean arrays
      const cleanedArray = value
        .map((item) =>
          typeof item === 'object'
            ? cleanObjectValues(item as Record<string, unknown>)
            : item
        )
        .filter((item) => {
          if (typeof item === 'object' && item !== null) {
            return Object.keys(item).length > 0;
          }
          return item !== null && item !== undefined && item !== '';
        });

      if (cleanedArray.length > 0) {
        result[key] = cleanedArray;
      }
    } else {
      result[key] = value;
    }
  });

  return result;
}

/**
 * Infer multiple business categories from invoice data with confidence scores
 */
function inferMultipleCategories(
  data: InvoiceData
): Array<{ name: string; confidence: number }> {
  const categories: Array<{ name: string; confidence: number }> = [];
  const scores: Record<string, number> = {};

  // Initialize with zero scores for all categories
  Object.keys(BUSINESS_CATEGORIES).forEach((category) => {
    scores[category] = 0;
  });

  // Check if category is already clearly mentioned in the invoice
  const existingCategory = findExistingCategory(data);
  if (existingCategory) {
    // Return just the clearly mentioned category with high confidence
    // This will be used directly without showing suggestions
    return [{ name: existingCategory, confidence: 90 }];
  }

  // Score based on vendor name
  if (data.vendor?.name) {
    const vendorName = data.vendor.name.toLowerCase();

    Object.entries(BUSINESS_CATEGORIES).forEach(
      ([category, keywords]) => {
        for (const keyword of keywords) {
          if (vendorName.includes(keyword.toLowerCase())) {
            scores[category] += 30; // Strong signal from vendor name
            break; // Only count once per category
          }
        }
      }
    );
  }

  // Score based on line items
  if (data.lineItems && data.lineItems.length > 0) {
    const itemsText = data.lineItems
      .map((item) => {
        const desc = item.description?.toLowerCase() || '';
        const itemName = item.itemCode?.toLowerCase() || '';
        return `${desc} ${itemName}`;
      })
      .join(' ');

    Object.entries(BUSINESS_CATEGORIES).forEach(
      ([category, keywords]) => {
        for (const keyword of keywords) {
          if (itemsText.includes(keyword.toLowerCase())) {
            scores[category] += 20; // Good signal from line items
            break; // Only count once per category
          }
        }
      }
    );
  }

  // Score based on invoice total amount
  if (data.financials?.total) {
    const totalStr = data.financials.total.replace(/[^0-9.]/g, '');
    const totalAmount = parseFloat(totalStr);

    if (!isNaN(totalAmount)) {
      if (totalAmount > 10000) {
        scores['Manufacturing'] += 15;
        scores['Construction'] += 15;
        scores['Technology'] += 10;
      } else if (totalAmount > 1000) {
        scores['Consulting'] += 10;
        scores['Professional Services'] += 10;
        scores['Technology'] += 5;
      } else if (totalAmount < 100) {
        scores['Retail'] += 15;
        scores['Food & Beverage'] += 10;
      }
    }
  }

  // Convert scores to array of categories with confidence
  Object.entries(scores).forEach(([category, score]) => {
    if (score > 0) {
      // Cap confidence at 90 to avoid unrealistic 99% values
      const confidence = Math.min(score, 90);
      categories.push({ name: category, confidence });
    }
  });

  // If no categories found, add defaults with more realistic confidences
  if (categories.length === 0) {
    categories.push(
      { name: 'Retail', confidence: 60 },
      { name: 'Business Services', confidence: 55 },
      { name: 'Other', confidence: 50 }
    );
  }

  // Sort by confidence score and ensure realistic confidence values
  return categories
    .sort((a, b) => b.confidence - a.confidence)
    .map((cat, index) => ({
      name: cat.name,
      // Ensure confidence values are realistic integers between 50-85
      // Stagger confidence levels for multiple suggestions
      confidence: Math.min(
        85 - index * 5,
        Math.max(Math.round(cat.confidence), 50)
      ),
    }));
}

// Find if category is already clearly mentioned in the invoice
function findExistingCategory(data: InvoiceData): string | null {
  // Check if category is explicitly mentioned in various fields
  const categoryFields = [
    data.additionalFields?.category,
    data.additionalFields?.invoiceCategory,
    data.additionalFields?.documentCategory,
  ];

  for (const field of categoryFields) {
    if (
      field &&
      typeof field === 'string' &&
      field.trim().length > 0
    ) {
      return field.trim();
    }
  }

  // Check if category is mentioned in specific text fields
  const textToSearch = [
    data.notes,
    data.additionalFields?.description,
  ]
    .filter(Boolean)
    .join(' ')
    .toLowerCase();

  const categoryRegex = /category\s*:\s*([a-zA-Z\s&]+)/i;
  const match = textToSearch.match(categoryRegex);
  if (match && match[1]) {
    return match[1].trim();
  }

  return null;
}

/**
 * Infer vendor types from invoice data
 */
function inferVendorTypes(
  data: InvoiceData
): Array<{ name: string; confidence: number }> {
  // Check if vendor name already exists in the invoice - if so, use it directly
  if (data.vendor?.name && data.vendor.name.trim().length > 0) {
    // If vendor is clearly specified, use it directly
    return [{ name: 'Vendor: ' + data.vendor.name, confidence: 98 }];
  }

  // Check if vendor type is already clearly mentioned in the invoice
  const existingVendorType = findExistingVendorType(data);
  if (existingVendorType) {
    // Return just the clearly mentioned vendor type with high confidence
    // This will be used directly without showing suggestions
    return [{ name: existingVendorType, confidence: 98 }];
  }

  // Continue with the rest of the function (only for cases where no vendor name or type is specified)
  const vendorTypes: Array<{ name: string; confidence: number }> = [];
  const scores: Record<string, number> = {};

  // Common vendor types with keywords
  const VENDOR_TYPES = {
    Supplier: ['supplier', 'vendor', 'distributor', 'wholesale'],
    'Service Provider': [
      'service',
      'provider',
      'consultant',
      'contractor',
    ],
    Manufacturer: [
      'manufacturer',
      'factory',
      'production',
      'made by',
      'made in',
    ],
    Retailer: ['retail', 'store', 'shop', 'outlet', 'market'],
    Contractor: ['contractor', 'construction', 'project', 'build'],
    Consultant: ['consulting', 'advisor', 'specialist', 'expert'],
    'Software Provider': [
      'software',
      'license',
      'subscription',
      'platform',
    ],
    'Healthcare Provider': [
      'medical',
      'health',
      'clinic',
      'hospital',
      'care',
    ],
    'Financial Institution': [
      'bank',
      'financial',
      'insurance',
      'investment',
    ],
    'Utility Company': [
      'utility',
      'electric',
      'power',
      'water',
      'gas',
    ],
    Customer: ['customer', 'client', 'buyer', 'purchaser'],
  };

  // Initialize scores
  Object.keys(VENDOR_TYPES).forEach((type) => {
    scores[type] = 0;
  });

  // Score based on line items
  if (data.lineItems && data.lineItems.length > 0) {
    const itemsText = data.lineItems
      .map((item) => item.description?.toLowerCase() || '')
      .join(' ');

    // Check for product-like items
    const productPatterns =
      /item|product|unit|qty|quantity|pcs|ea|each/i;
    if (productPatterns.test(itemsText)) {
      scores['Supplier'] += 30;
      scores['Manufacturer'] += 20;
      scores['Retailer'] += 15;
    }

    // Check for service-like items
    const servicePatterns =
      /service|hour|hr|consultation|support|maintenance|subscription/i;
    if (servicePatterns.test(itemsText)) {
      scores['Service Provider'] += 30;
      scores['Consultant'] += 20;
    }

    // Check specific keywords
    Object.entries(VENDOR_TYPES).forEach(([type, keywords]) => {
      for (const keyword of keywords) {
        if (itemsText.includes(keyword.toLowerCase())) {
          scores[type] += 20;
          break;
        }
      }
    });
  }

  // Determine if the invoice looks like an outgoing (to customer) or incoming (from supplier)
  // This is a heuristic and may need fine-tuning based on actual data
  if (data.customer?.name && data.customer.name.trim().length > 0) {
    // If customer is clearly specified, this might be an outgoing invoice
    if (
      data.customer.name.toLowerCase() !== 'self' &&
      data.customer.name.toLowerCase() !== 'internal' &&
      data.customer.name.toLowerCase() !== 'company'
    ) {
      scores['Customer'] += 40;
    }
  }

  // Convert scores to array of types with confidence
  Object.entries(scores).forEach(([type, score]) => {
    if (score > 0) {
      // Cap at 90 to avoid unrealistic values
      const confidence = Math.min(score, 90);
      vendorTypes.push({ name: type, confidence });
    }
  });

  // If no types found, add defaults with more realistic confidences
  if (vendorTypes.length === 0) {
    vendorTypes.push(
      { name: 'Supplier', confidence: 65 },
      { name: 'Service Provider', confidence: 60 }
    );
  }

  // Sort by confidence and return top results with realistic confidence values
  return vendorTypes
    .sort((a, b) => b.confidence - a.confidence)
    .map((type, index) => ({
      name: type.name,
      // Ensure confidence values are realistic integers between 50-85
      // Stagger confidence levels for multiple suggestions
      confidence: Math.min(
        85 - index * 5,
        Math.max(Math.round(type.confidence), 50)
      ),
    }));
}

// Modify findExistingVendorType to look at the main vendor property first
function findExistingVendorType(data: InvoiceData): string | null {
  // First check if there's an actual vendor name
  if (data.vendor?.name && data.vendor.name.trim().length > 0) {
    return 'Vendor: ' + data.vendor.name;
  }

  // Then check for explicit fields
  const vendorTypeFields = [
    data.additionalFields?.vendorType,
    data.additionalFields?.supplierType,
    data.additionalFields?.partnerType,
  ];

  for (const field of vendorTypeFields) {
    if (
      field &&
      typeof field === 'string' &&
      field.trim().length > 0
    ) {
      return field.trim();
    }
  }

  // Check if vendor type is mentioned in specific text fields
  const textToSearch = [
    data.notes,
    data.additionalFields?.description,
    data.vendor?.notes,
  ]
    .filter(Boolean)
    .join(' ')
    .toLowerCase();

  const vendorTypeRegex =
    /vendor\s*type\s*:\s*([a-zA-Z\s&]+)|supplier\s*type\s*:\s*([a-zA-Z\s&]+)/i;
  const match = textToSearch.match(vendorTypeRegex);
  if (match && (match[1] || match[2])) {
    return (match[1] || match[2]).trim();
  }

  return null;
}
