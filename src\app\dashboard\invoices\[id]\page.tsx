import { notFound } from "next/navigation";
import type { Metadata } from "next";
import DashboardLayout from "@/components/layout/DashboardLayout";
import { InvoiceDetails } from "@/components/dashboard/pages/invoices/invoice-details";
import { getInvoiceById } from "@/actions/invoice-actions";
import { InvoiceStatus } from "@/lib/types";

export async function generateMetadata(
  props: {
    params: Promise<{ id: string }>;
  }
): Promise<Metadata> {
  const params = await props.params;
  const invoice = await getInvoiceById(params.id);

  if (!invoice) {
    return {
      title: "Invoice Not Found",
    };
  }

  return {
    title: `Invoice ${invoice.invoiceNumber || invoice.id} | Dashboard`,
    description: `Details for invoice ${invoice.invoiceNumber || invoice.id}`,
  };
}

export default async function InvoiceDetailsPage(
  props: {
    params: Promise<{ id: string }>;
  }
) {
  const params = await props.params;
  const invoice = await getInvoiceById(params.id);

  if (!invoice) {
    notFound();
  }

  // Ensure extractedData is never null
  const mappedInvoice = {
    ...invoice,
    status: invoice.status as unknown as InvoiceStatus,
    extractedData: invoice.extractedData ? invoice.extractedData as unknown as Record<string, unknown> : {},
    category: invoice.category ? {
      id: invoice.category.id,
      name: invoice.category.name,
      color: invoice.category.color || undefined
    } : null,
    lineItems: invoice.lineItems.map(item => ({
      ...item,
      attributes: item.attributes ? item.attributes as Record<string, unknown> : undefined
    }))
  };

  return (
    <DashboardLayout>
      <InvoiceDetails invoice={mappedInvoice} />
    </DashboardLayout>
  );
}
