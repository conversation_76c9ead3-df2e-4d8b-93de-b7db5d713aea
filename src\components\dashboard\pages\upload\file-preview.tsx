"use client"

import { useState, useEffect } from "react"
import { FileText, Image as ImageIcon, FileBarChart2 } from "lucide-react"
import Image from "next/image"

interface FilePreviewProps {
  src: string
  type: string
  large?: boolean
}

export function FilePreview({ src, type, large = false }: FilePreviewProps) {
  const [error, setError] = useState(false)

  useEffect(() => {
    setError(false)
  }, [src])
  
  const getFileIcon = () => {
    if (type === "application/pdf") {
      return <FileBarChart2 className={`${large ? "h-24 w-24" : "h-16 w-16"} text-red-400`} />
    } else if (type.startsWith("image/")) {
      return <ImageIcon className={`${large ? "h-24 w-24" : "h-16 w-16"} text-blue-400`} />
    } else {
      return <FileText className={`${large ? "h-24 w-24" : "h-16 w-16"} text-amber-400`} />
    }
  }

  if (error || type === "application/pdf") {
    return (
      <div className={`flex ${large ? "h-96" : "h-full"} w-full items-center justify-center rounded-md ${large ? "bg-slate-50" : "bg-white"} transition-colors duration-200`}>
        {getFileIcon()}
      </div>
    )
  }

  return (
    <div className="relative w-full h-full">
      <Image
        src={src || "/placeholder.svg"}
        alt="Document preview"
        className={`mx-auto ${large ? "max-h-96" : "h-full"} w-auto rounded-md object-contain transition-opacity duration-200 hover:opacity-90`}
        onError={() => setError(true)}
        width={large ? 1000 : 500}
        height={large ? 1000 : 500}
        quality={90}
        priority={large}
      />
    </div>
  )
}
