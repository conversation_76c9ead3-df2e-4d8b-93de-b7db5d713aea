import { put } from '@vercel/blob';
import { nanoid } from 'nanoid';

/**
 * Upload a file to Vercel Blob storage
 * @param file The file to upload
 * @param folder Optional folder path to organize uploads
 * @returns The URL of the uploaded file
 */
export async function uploadToBlob(file: File, folder: string = 'invoices'): Promise<string> {
  try {
    // Create a unique filename with the original extension
    const fileExt = file.name.split('.').pop() || '';
    const uniqueFilename = `${folder}/${nanoid()}.${fileExt}`;

    // Upload to Vercel Blob
    const { url } = await put(uniqueFilename, file, {
      access: 'public',
      addRandomSuffix: false,
    });

    return url;
  } catch (error) {
    console.error('Error uploading to Vercel Blob:', error);
    throw new Error('Failed to upload file to storage');
  }
}

/**
 * Uploads a file to Vercel Blob storage
 * @param fileName The name of the file
 * @param data The file data as a Buffer
 * @param contentType The MIME type of the file
 * @returns The URL of the uploaded file
 */
export async function uploadToVercelBlob(
  fileName: string,
  data: Buffer,
  contentType: string
): Promise<string> {
  try {
    const blob = await put(fileName, data, {
      contentType,
      access: "public",
    });

    return blob.url;
  } catch (error) {
    console.error("Error uploading to Vercel Blob:", error);
    throw new Error("Failed to upload file to storage");
  }
}

/**
 * Gets the URL for a file in Vercel Blob storage
 * @param fileName The name of the file
 * @returns The URL of the file
 */
export function getVercelBlobUrl(fileName: string): string {
  // Construct URL based on project and environment
  const baseUrl = process.env.VERCEL_BLOB_URL || "https://public.blob.vercel-storage.com";
  return `${baseUrl}/${fileName}`;
} 