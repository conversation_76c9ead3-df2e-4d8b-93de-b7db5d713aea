"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  FolderPlus,
  Folder,
  MoreHorizontal,
  Trash2,
  Edit,
  Download,
  FileText,
  Loader2,
  Search,
  Plus,
  Calendar,
  FileSpreadsheet,
  RefreshCw,
  ChevronRight,
} from "lucide-react"
import {
  getExportFolders,
  createExportFolder,
  deleteExportFolder,
  renameExportFolder,
  getFolderExports,
  removeInvoiceFromFolder,
  updateFolderWithInvoices,
  getFolderInvoices,
} from "@/actions/export-actions"
import { formatDate } from "@/lib/utils"
import { toast } from "sonner"
import { Checkbox } from "@/components/ui/checkbox"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { getInvoices } from "@/actions/invoice-actions"
import { ExportDialog } from "@/components/dashboard/pages/invoices/export-dialog"
import { Badge } from "@/components/ui/badge"

interface ExportFolder {
  id: string
  name: string
  createdAt: Date
  invoiceCount: number
}

interface FolderExport {
  id: string
  fileName: string
  fileUrl: string
  format: string
  count: number
  createdAt: Date
}

interface Invoice {
  id: string
  invoiceNumber?: string | null
  vendorName?: string | null
  issueDate?: Date | null
  amount?: number | null
  status?: string
}

// Function to format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  }).format(amount)
}

export function ExportFolders() {
  const router = useRouter()
  const [folders, setFolders] = useState<ExportFolder[]>([])
  const [selectedFolder, setSelectedFolder] = useState<ExportFolder | null>(null)
  const [folderExports, setFolderExports] = useState<FolderExport[]>([])
  const [isLoadingFolders, setIsLoadingFolders] = useState(true)
  const [isLoadingExports, setIsLoadingExports] = useState(false)
  const [isCreatingFolder, setIsCreatingFolder] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isRenameDialogOpen, setIsRenameDialogOpen] = useState(false)
  const [isRemoveInvoiceDialogOpen, setIsRemoveInvoiceDialogOpen] = useState(false)
  const [newFolderName, setNewFolderName] = useState("")
  const [renameFolderName, setRenameFolderName] = useState("")
  const [folderToDelete, setFolderToDelete] = useState<string | null>(null)
  const [folderToRename, setFolderToRename] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState("")
  const [invoiceToRemove, setInvoiceToRemove] = useState<string | null>(null)
  const [isRemovingInvoice, setIsRemovingInvoice] = useState(false)
  const [isUpdatingFolder, setIsUpdatingFolder] = useState(false)
  const [availableInvoices, setAvailableInvoices] = useState<Invoice[]>([])
  const [folderInvoices, setFolderInvoices] = useState<Invoice[]>([])
  const [selectedInvoices, setSelectedInvoices] = useState<string[]>([])
  const [isLoadingInvoices, setIsLoadingInvoices] = useState(false)
  const [activeTab, setActiveTab] = useState("exports")
  const [error, setError] = useState<string | null>(null)
  const [isExportDialogOpen, setIsExportDialogOpen] = useState(false)
  const [invoiceSearchQuery, setInvoiceSearchQuery] = useState("")

  // Fetch folders on component mount
  useEffect(() => {
    fetchFolders()
  }, [])

  // Fetch folder exports when a folder is selected
  useEffect(() => {
    if (selectedFolder) {
      fetchFolderExports(selectedFolder.id)
    }
  }, [selectedFolder])

  // Add new effect to update selectedFolder invoiceCount based on folderInvoices
  useEffect(() => {
    if (selectedFolder && folderInvoices.length !== selectedFolder.invoiceCount) {
      setSelectedFolder(prev => {
        if (!prev) return null;
        return {
          ...prev,
          invoiceCount: folderInvoices.length
        }
      });
      
      // Also update in the folders list
      setFolders(prev => 
        prev.map(folder => 
          folder.id === selectedFolder.id 
            ? { ...folder, invoiceCount: folderInvoices.length } 
            : folder
        )
      );
    }
  }, [folderInvoices, selectedFolder]);

  const fetchFolders = async () => {
    try {
      setIsLoadingFolders(true)
      setError(null)
      const result = await getExportFolders()
      if (result.success && result.folders) {
        setFolders(result.folders)
      } else {
        throw new Error(result.error || "Failed to fetch folders")
      }
    } catch (error) {
      console.error("Error fetching folders:", error)
      setError("Failed to load export folders.")
      toast.error("Failed to load export folders.")
    } finally {
      setIsLoadingFolders(false)
    }
  }

  const fetchFolderExports = async (folderId: string) => {
    try {
      setIsLoadingExports(true)
      setError(null)
      const result = await getFolderExports(folderId)
      if (result.success && result.exports) {
        setFolderExports(result.exports)
        
        // Fetch invoices in the folder
        const invoicesResult = await getFolderInvoices(folderId)
        if (invoicesResult.success && invoicesResult.invoices) {
          setFolderInvoices(invoicesResult.invoices)
        }
      } else {
        throw new Error(result.error || "Failed to fetch folder exports")
      }
    } catch (error) {
      console.error("Error fetching folder exports:", error)
      setError("Failed to load folder exports.")
      toast.error("Failed to load folder exports.")
    } finally {
      setIsLoadingExports(false)
    }
  }

  const fetchAvailableInvoices = async () => {
    try {
      setIsLoadingInvoices(true)
      setError(null)
      const result = await getInvoices({
        page: 1,
        limit: 100, // Get a large number of invoices
      })

      if (result.invoices) {
        // Get current folder invoices to filter them out
        const currentFolderInvoiceIds = folderInvoices.map((invoice) => invoice.id)

        // Filter out invoices already in the folder
        const filteredInvoices = result.invoices.filter((invoice) => !currentFolderInvoiceIds.includes(invoice.id))

        setAvailableInvoices(filteredInvoices)
      }
    } catch (error) {
      console.error("Error fetching invoices:", error)
      setError("Failed to load invoices.")
      toast.error("Failed to load invoices.")
    } finally {
      setIsLoadingInvoices(false)
    }
  }

  const handleCreateFolder = async () => {
    if (!newFolderName.trim()) {
      toast.error("Please enter a folder name.")
      return
    }

    try {
      setIsCreatingFolder(true)
      setError(null)
      const result = await createExportFolder(newFolderName)

      if (result.success && result.folder) {
        toast.success(`Folder "${newFolderName}" has been created.`)

        // Add the new folder to the list and select it
        setFolders((prev) => [...prev, result.folder])
        setSelectedFolder(result.folder)
        setNewFolderName("")
      } else {
        throw new Error(result.error || "Failed to create folder")
      }
    } catch (error) {
      console.error("Error creating folder:", error)
      setError("Failed to create folder. Please try again.")
      toast.error("Failed to create folder. Please try again.")
    } finally {
      setIsCreatingFolder(false)
    }
  }

  const handleDeleteFolder = async () => {
    if (!folderToDelete) return

    try {
      setError(null)
      const result = await deleteExportFolder(folderToDelete)

      if (result.success) {
        toast.success("The folder has been deleted successfully.")

        // Remove the folder from the list
        setFolders((prev) => prev.filter((folder) => folder.id !== folderToDelete))

        // If the deleted folder was selected, clear the selection
        if (selectedFolder && selectedFolder.id === folderToDelete) {
          setSelectedFolder(null)
          setFolderExports([])
        }
      } else {
        throw new Error(result.error || "Failed to delete folder")
      }
    } catch (error) {
      console.error("Error deleting folder:", error)
      setError("Failed to delete folder. Please try again.")
      toast.error("Failed to delete folder. Please try again.")
    } finally {
      setIsDeleteDialogOpen(false)
      setFolderToDelete(null)
    }
  }

  const handleRenameFolder = async () => {
    if (!folderToRename || !renameFolderName.trim()) return

    try {
      setError(null)
      const result = await renameExportFolder(folderToRename, renameFolderName)

      if (result.success) {
        toast.success("The folder has been renamed successfully.")

        // Update the folder in the list
        setFolders((prev) =>
          prev.map((folder) => (folder.id === folderToRename ? { ...folder, name: renameFolderName } : folder)),
        )

        // If the renamed folder was selected, update the selection
        if (selectedFolder && selectedFolder.id === folderToRename) {
          setSelectedFolder((prev) => (prev ? { ...prev, name: renameFolderName } : null))
        }
      } else {
        throw new Error(result.error || "Failed to rename folder")
      }
    } catch (error) {
      console.error("Error renaming folder:", error)
      setError("Failed to rename folder. Please try again.")
      toast.error("Failed to rename folder. Please try again.")
    } finally {
      setIsRenameDialogOpen(false)
      setFolderToRename(null)
      setRenameFolderName("")
    }
  }

  const handleRemoveInvoice = async () => {
    if (!selectedFolder || !invoiceToRemove) return

    try {
      setIsRemovingInvoice(true)
      setError(null)
      const result = await removeInvoiceFromFolder(selectedFolder.id, invoiceToRemove)

      if (result.success) {
        toast.success("Invoice removed from folder successfully.")

        // Refresh the folder exports
        fetchFolderExports(selectedFolder.id)

        // Update the folder count in the list, ensuring it doesn't go below 0
        setFolders((prev) =>
          prev.map((folder) => {
            if (folder.id === selectedFolder.id) {
              const newCount = Math.max(0, folder.invoiceCount - 1)
              return { ...folder, invoiceCount: newCount }
            }
            return folder
          }),
        )

        // Update the selected folder, ensuring count doesn't go below 0
        if (selectedFolder) {
          const newCount = Math.max(0, selectedFolder.invoiceCount - 1)
          setSelectedFolder({
            ...selectedFolder,
            invoiceCount: newCount,
          })
        }
      } else {
        throw new Error(result.error || "Failed to remove invoice from folder")
      }
    } catch (error) {
      console.error("Error removing invoice:", error)
      setError("Failed to remove invoice from folder.")
      toast.error("Failed to remove invoice from folder.")
    } finally {
      setIsRemovingInvoice(false)
      setIsRemoveInvoiceDialogOpen(false)
      setInvoiceToRemove(null)
    }
  }

  const handleUpdateFolder = async () => {
    if (!selectedFolder || selectedInvoices.length === 0) return

    try {
      setIsUpdatingFolder(true)
      setError(null)
      const result = await updateFolderWithInvoices(selectedFolder.id, selectedInvoices)

      if (result.success) {
        const addedCount = result.addedCount || 0;
        const duplicateCount = selectedInvoices.length - addedCount;
        
        if (addedCount > 0) {
          if (duplicateCount > 0) {
            toast.success(`Added ${addedCount} invoice(s) to folder. ${duplicateCount} already existed.`)
          } else {
            toast.success(`Added ${addedCount} invoice(s) to folder.`)
          }
          
          // Refresh the folder exports
          fetchFolderExports(selectedFolder.id)

          // Update the folder count in the list
          setFolders((prev) =>
            prev.map((folder) =>
              folder.id === selectedFolder.id
                ? { ...folder, invoiceCount: folder.invoiceCount + addedCount }
                : folder,
            ),
          )

          // Update the selected folder
          if (selectedFolder) {
            setSelectedFolder({
              ...selectedFolder,
              invoiceCount: selectedFolder.invoiceCount + addedCount,
            })
          }
        } else {
          toast.warning("These invoices are already in the folder.")
        }

        // Clear selected invoices
        setSelectedInvoices([])
        setActiveTab("exports")
      } else {
        if (result.error?.includes("already exist") || result.error?.includes("duplicate")) {
          toast.warning("These invoices are already in the folder.")
          setSelectedInvoices([])
          setActiveTab("exports")
        } else {
          throw new Error(result.error || "Failed to update folder")
        }
      }
    } catch (error) {
      console.error("Error updating folder:", error)
      setError("Failed to update folder with invoices.")
      toast.error("Failed to update folder with invoices.")
    } finally {
      setIsUpdatingFolder(false)
    }
  }

  const downloadExport = (fileUrl: string, fileName: string) => {
    const link = document.createElement("a")
    link.href = fileUrl
    link.setAttribute("download", fileName)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  // Filter folders based on search query
  const filteredFolders = folders.filter((folder) => folder.name.toLowerCase().includes(searchQuery.toLowerCase()))

  // Filter available invoices based on search query
  const filteredAvailableInvoices = availableInvoices.filter((invoice) => {
    const searchLower = invoiceSearchQuery.toLowerCase()
    return (
      invoice.invoiceNumber?.toLowerCase().includes(searchLower) ||
      false ||
      invoice.vendorName?.toLowerCase().includes(searchLower) ||
      false
    )
  })

  return (
    <div className="px-4 py-6 space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Export Folders</h1>
          <p className="text-muted-foreground mt-1">Manage your invoice exports and downloads</p>
        </div>
        <Button onClick={() => router.push("/dashboard/invoices")} className="self-start sm:self-center">
          View Invoices
        </Button>
      </div>

      {error && (
        <Alert variant="destructive" className="animate-in fade-in-50 slide-in-from-top-5">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="space-y-6">
          {/* Create New Folder */}
          <Card className="shadow-sm border-neutral-200 dark:border-neutral-800">
            <CardHeader className="pb-3">
              <CardTitle className="text-xl">Create New Folder</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-end gap-3">
                <div className="flex-1 space-y-2">
                  <Label htmlFor="newFolderName">Folder Name</Label>
                  <Input
                    id="newFolderName"
                    placeholder="Enter folder name"
                    value={newFolderName}
                    onChange={(e) => setNewFolderName(e.target.value)}
                    className="h-10"
                  />
                </div>
                <Button
                  onClick={handleCreateFolder}
                  disabled={isCreatingFolder || !newFolderName.trim()}
                  className="h-10"
                >
                  {isCreatingFolder ? (
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  ) : (
                    <FolderPlus className="h-4 w-4 mr-2" />
                  )}
                  Create
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Folder List */}
          <Card className="shadow-sm border-neutral-200 dark:border-neutral-800 h-[calc(100vh-320px)] min-h-[400px]">
            <CardHeader className="pb-3">
              <CardTitle className="text-xl">Your Folders</CardTitle>
              <div className="relative mt-2">
                <Search className="absolute left-2.5 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search folders..."
                  className="pl-8 h-10"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </CardHeader>
            <CardContent className="p-0">
              {isLoadingFolders ? (
                <div className="flex justify-center items-center py-12">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                </div>
              ) : filteredFolders.length === 0 ? (
                <div className="text-center py-12 px-4">
                  <Folder className="h-12 w-12 mx-auto text-muted-foreground opacity-50" />
                  <h3 className="mt-4 text-lg font-medium">No folders found</h3>
                  <p className="mt-2 text-sm text-muted-foreground">
                    {searchQuery ? "Try a different search term" : "Create a folder to start organizing your exports"}
                  </p>
                </div>
              ) : (
                <ScrollArea className="h-[calc(100vh-420px)] min-h-[300px]">
                  <div className="px-4 pb-4">
                    {filteredFolders.map((folder) => (
                      <div
                        key={folder.id}
                        className={`flex items-center justify-between p-3 rounded-lg cursor-pointer transition-all my-2 ${
                          selectedFolder?.id === folder.id
                            ? "bg-primary/10 border border-primary/30 shadow-sm"
                            : "hover:bg-muted border border-transparent"
                        }`}
                        onClick={() => setSelectedFolder(folder)}
                      >
                        <div className="flex items-center gap-3">
                          <div
                            className={`p-2 rounded-md ${selectedFolder?.id === folder.id ? "bg-primary/20" : "bg-muted"}`}
                          >
                            <Folder
                              className={`h-5 w-5 ${selectedFolder?.id === folder.id ? "text-primary" : "text-muted-foreground"}`}
                            />
                          </div>
                          <div>
                            <p className="font-medium text-base">{folder.name}</p>
                            <div className="flex items-center text-xs text-muted-foreground mt-0.5">
                              <Badge variant="outline" className="text-xs font-normal rounded-full px-2 py-0 h-5">
                                {folder.invoiceCount} invoice{folder.invoiceCount !== 1 ? "s" : ""}
                              </Badge>
                              <span className="mx-1.5">•</span>
                              <span>{formatDate(folder.createdAt)}</span>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center">
                          {selectedFolder?.id === folder.id && <ChevronRight className="h-5 w-5 text-primary mr-1" />}
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                              <Button variant="ghost" size="icon" className="h-8 w-8">
                                <MoreHorizontal className="h-4 w-4" />
                                <span className="sr-only">Open menu</span>
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end" className="w-48">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem
                                onClick={(e) => {
                                  e.stopPropagation()
                                  setFolderToRename(folder.id)
                                  setRenameFolderName(folder.name)
                                  setIsRenameDialogOpen(true)
                                }}
                                className="cursor-pointer"
                              >
                                <Edit className="mr-2 h-4 w-4" />
                                Rename
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                onClick={(e) => {
                                  e.stopPropagation()
                                  setFolderToDelete(folder.id)
                                  setIsDeleteDialogOpen(true)
                                }}
                                className="text-destructive focus:text-destructive cursor-pointer"
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              )}
            </CardContent>
          </Card>
        </div>

        <div className="lg:col-span-2">
          <Card className="shadow-sm border-neutral-200 dark:border-neutral-800 h-[calc(100vh-200px)] min-h-[500px] flex flex-col">
            <CardHeader className="pb-3 flex-shrink-0">
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div>
                  <CardTitle className="text-lg">
                    {selectedFolder ? `${selectedFolder.name}` : "Select a folder to view exports"}
                  </CardTitle>
                  {selectedFolder && (
                    <CardDescription>
                      {folderInvoices.length} invoice
                      {folderInvoices.length !== 1 ? "s" : ""} in this folder
                    </CardDescription>
                  )}
                </div>
                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 w-full sm:w-auto">
                  {selectedFolder && folderInvoices.length > 0 && (
                    <Button onClick={() => setIsExportDialogOpen(true)} className="w-full sm:w-auto" variant="outline">
                      <Download className="mr-2 h-4 w-4" />
                      Export Folder
                    </Button>
                  )}
                  {selectedFolder && (
                    <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full sm:w-auto">
                      <TabsList className="grid w-full grid-cols-2">
                        <TabsTrigger value="exports" className="px-3 py-1.5">
                          <FileText className="h-4 w-4 mr-2" />
                          Exports
                        </TabsTrigger>
                        <TabsTrigger value="update" onClick={() => fetchAvailableInvoices()} className="px-3 py-1.5">
                          <RefreshCw className="h-4 w-4 mr-2" />
                          Update
                        </TabsTrigger>
                      </TabsList>
                    </Tabs>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent className="flex-grow overflow-hidden p-0">
              {!selectedFolder ? (
                <div className="flex flex-col items-center justify-center h-full p-6">
                  <div className="bg-muted/50 p-6 rounded-full mb-4">
                    <FileText className="h-12 w-12 text-muted-foreground" />
                  </div>
                  <h3 className="text-xl font-medium">No folder selected</h3>
                  <p className="text-muted-foreground mt-2 text-center max-w-md">
                    Select a folder from the list to view its exports or create a new folder to get started
                  </p>
                </div>
              ) : (
                <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full h-full flex flex-col">
                  <TabsContent value="exports" className="mt-0 flex-grow overflow-hidden flex flex-col p-4">
                    {isLoadingExports ? (
                      <div className="flex justify-center items-center py-12 flex-grow">
                        <Loader2 className="h-8 w-8 animate-spin text-primary" />
                      </div>
                    ) : folderExports.length === 0 && folderInvoices.length === 0 ? (
                      <div className="flex flex-col items-center justify-center h-full py-8">
                        <div className="bg-muted/50 p-6 rounded-full mb-4">
                          <FileText className="h-12 w-12 text-muted-foreground" />
                        </div>
                        <h3 className="text-xl font-medium">No exports yet</h3>
                        <p className="text-muted-foreground mt-2 text-center max-w-md">
                          Export invoices to this folder to see them here
                        </p>
                        <Button className="mt-6" onClick={() => router.push("/dashboard/invoices")}>
                          <Plus className="mr-2 h-4 w-4" />
                          Export Invoices
                        </Button>
                      </div>
                    ) : (
                      <div className="space-y-6 h-full flex flex-col overflow-hidden">
                        {/* Folder invoices */}
                        {folderInvoices.length > 0 && (
                          <div className="flex flex-col flex-shrink-0">
                            <h3 className="text-lg font-medium mb-4 flex items-center">
                              <FileText className="h-5 w-5 mr-2 text-primary" />
                              Invoices in this folder
                            </h3>
                            <ScrollArea className="h-[30vh] max-h-[250px] border rounded-lg">
                              <div className="space-y-2 p-3">
                                {folderInvoices.map((invoice) => (
                                  <div
                                    key={invoice.id}
                                    className="flex items-center justify-between p-3 rounded-lg border hover:bg-muted/50 transition-colors"
                                  >
                                    <div className="flex items-center gap-3 min-w-0">
                                      <div className="bg-muted/70 p-2 rounded-md">
                                        <FileText className="h-4 w-4 text-primary" />
                                      </div>
                                      <div className="min-w-0">
                                        <p className="font-medium truncate">{invoice.invoiceNumber || invoice.id}</p>
                                        <div className="flex items-center text-xs text-muted-foreground mt-0.5 flex-wrap gap-1">
                                          <span className="truncate max-w-[150px]">
                                            {invoice.vendorName || "Unknown Vendor"}
                                          </span>
                                          <span className="mx-1">•</span>
                                          <span>{invoice.issueDate ? formatDate(invoice.issueDate) : "No date"}</span>
                                          {invoice.status && (
                                            <>
                                              <span className="mx-1">•</span>
                                              <Badge
                                                variant="outline"
                                                className="text-xs font-normal rounded-full px-2 py-0 h-5"
                                              >
                                                {invoice.status}
                                              </Badge>
                                            </>
                                          )}
                                        </div>
                                      </div>
                                    </div>
                                    <div className="flex items-center gap-3">
                                      <div className="font-medium text-sm">
                                        {invoice.amount ? formatCurrency(invoice.amount) : "N/A"}
                                      </div>
                                      <Button
                                        variant="ghost"
                                        size="icon"
                                        className="h-8 w-8 rounded-full hover:bg-destructive/10 hover:text-destructive"
                                        onClick={(e) => {
                                          e.stopPropagation()
                                          setInvoiceToRemove(invoice.id)
                                          setIsRemoveInvoiceDialogOpen(true)
                                        }}
                                      >
                                        <Trash2 className="h-4 w-4" />
                                      </Button>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </ScrollArea>
                          </div>
                        )}

                        {/* Exports */}
                        {folderExports.length > 0 && (
                          <div
                            className={`flex-grow flex flex-col overflow-hidden ${folderInvoices.length > 0 ? "mt-6" : ""}`}
                          >
                            <h3 className="text-lg font-medium mb-4 flex-shrink-0 flex items-center">
                              <Download className="h-5 w-5 mr-2 text-primary" />
                              Export History
                            </h3>
                            <div className="flex-grow min-h-[200px] border rounded-lg overflow-hidden">
                              <ScrollArea className="h-full w-full">
                                <div className="space-y-3 p-3">
                                  {folderExports.map((exportItem) => (
                                    <Card
                                      key={exportItem.id}
                                      className="overflow-hidden border-neutral-200 dark:border-neutral-800 shadow-sm"
                                    >
                                      <div className="flex items-center p-4">
                                        <div className="h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center mr-4 flex-shrink-0">
                                          {exportItem.format === "pdf" ? (
                                            <FileText className="h-6 w-6 text-red-500" />
                                          ) : (
                                            <FileSpreadsheet className="h-6 w-6 text-green-600" />
                                          )}
                                        </div>
                                        <div className="flex-1 min-w-0">
                                          <h4 className="font-medium truncate text-base">{exportItem.fileName}</h4>
                                          <div className="flex flex-wrap items-center text-sm text-muted-foreground mt-1 gap-1">
                                            <div className="flex items-center">
                                              <Calendar className="h-3.5 w-3.5 mr-1 flex-shrink-0" />
                                              <span>{formatDate(exportItem.createdAt)}</span>
                                            </div>
                                            <span className="mx-1.5">•</span>
                                            <Badge
                                              variant="outline"
                                              className="text-xs font-normal rounded-full px-2 py-0 h-5"
                                            >
                                              {exportItem.count} invoice{exportItem.count !== 1 ? "s" : ""}
                                            </Badge>
                                            <span className="mx-1.5">•</span>
                                            <Badge
                                              variant="secondary"
                                              className="text-xs font-normal rounded-full px-2 py-0 h-5"
                                            >
                                              {exportItem.format.toUpperCase()}
                                            </Badge>
                                          </div>
                                        </div>
                                        <Button
                                          variant="outline"
                                          size="sm"
                                          className="flex-shrink-0 gap-1.5"
                                          onClick={() => downloadExport(exportItem.fileUrl, exportItem.fileName)}
                                        >
                                          <Download className="h-4 w-4" />
                                          Download
                                        </Button>
                                      </div>
                                    </Card>
                                  ))}
                                </div>
                              </ScrollArea>
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </TabsContent>

                  <TabsContent value="update" className="mt-0 flex-grow overflow-hidden flex flex-col p-4">
                    {isLoadingInvoices ? (
                      <div className="flex justify-center items-center py-12 flex-grow">
                        <Loader2 className="h-8 w-8 animate-spin text-primary" />
                      </div>
                    ) : availableInvoices.length === 0 ? (
                      <div className="flex flex-col items-center justify-center h-full py-8">
                        <div className="bg-muted/50 p-6 rounded-full mb-4">
                          <FileText className="h-12 w-12 text-muted-foreground" />
                        </div>
                        <h3 className="text-xl font-medium">No invoices available</h3>
                        <p className="text-muted-foreground mt-2 text-center max-w-md">
                          All invoices are already in this folder
                        </p>
                        <Button className="mt-6" onClick={() => router.push("/dashboard/invoices")}>
                          <Plus className="mr-2 h-4 w-4" />
                          Create New Invoice
                        </Button>
                      </div>
                    ) : (
                      <div className="space-y-4 h-full flex flex-col overflow-hidden">
                        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 flex-shrink-0">
                          <h3 className="text-lg font-medium flex items-center">
                            <Plus className="h-5 w-5 mr-2 text-primary" />
                            Select Invoices to Add
                          </h3>
                          <div className="flex gap-2 w-full sm:w-auto">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setSelectedInvoices([])}
                              disabled={selectedInvoices.length === 0}
                              className="flex-1 sm:flex-none"
                            >
                              Clear Selection
                            </Button>
                            <Button
                              size="sm"
                              onClick={handleUpdateFolder}
                              disabled={selectedInvoices.length === 0 || isUpdatingFolder}
                              className="flex-1 sm:flex-none"
                            >
                              {isUpdatingFolder ? (
                                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                              ) : (
                                <Plus className="h-4 w-4 mr-2" />
                              )}
                              Add {selectedInvoices.length} Invoice{selectedInvoices.length !== 1 ? "s" : ""}
                            </Button>
                          </div>
                        </div>

                        <div className="relative mb-4 flex-shrink-0">
                          <Search className="absolute left-2.5 top-3 h-4 w-4 text-muted-foreground" />
                          <Input
                            type="search"
                            placeholder="Search invoices..."
                            className="pl-8 h-10"
                            value={invoiceSearchQuery}
                            onChange={(e) => setInvoiceSearchQuery(e.target.value)}
                          />
                        </div>

                        <ScrollArea className="flex-grow border rounded-lg">
                          <div className="space-y-2 p-3">
                            {filteredAvailableInvoices.map((invoice) => (
                              <div
                                key={invoice.id}
                                className={`flex items-center justify-between p-3 rounded-lg border transition-colors ${
                                  selectedInvoices.includes(invoice.id)
                                    ? "bg-primary/10 border-primary/30"
                                    : "hover:bg-muted/50"
                                }`}
                                onClick={() => {
                                  if (selectedInvoices.includes(invoice.id)) {
                                    setSelectedInvoices((prev) => prev.filter((id) => id !== invoice.id))
                                  } else {
                                    setSelectedInvoices((prev) => [...prev, invoice.id])
                                  }
                                }}
                              >
                                <div className="flex items-center gap-3 min-w-0">
                                  <Checkbox
                                    checked={selectedInvoices.includes(invoice.id)}
                                    onCheckedChange={(checked) => {
                                      if (checked) {
                                        setSelectedInvoices((prev) => [...prev, invoice.id])
                                      } else {
                                        setSelectedInvoices((prev) => prev.filter((id) => id !== invoice.id))
                                      }
                                    }}
                                    className="h-5 w-5"
                                  />
                                  <div className="min-w-0">
                                    <p className="font-medium truncate">{invoice.invoiceNumber || invoice.id}</p>
                                    <div className="flex items-center text-xs text-muted-foreground mt-0.5 flex-wrap gap-1">
                                      <span className="truncate max-w-[150px]">
                                        {invoice.vendorName || "Unknown Vendor"}
                                      </span>
                                      <span className="mx-1">•</span>
                                      <span>{invoice.issueDate ? formatDate(invoice.issueDate) : "No date"}</span>
                                      {invoice.status && (
                                        <>
                                          <span className="mx-1">•</span>
                                          <Badge
                                            variant="outline"
                                            className="text-xs font-normal rounded-full px-2 py-0 h-5"
                                          >
                                            {invoice.status}
                                          </Badge>
                                        </>
                                      )}
                                    </div>
                                  </div>
                                </div>
                                <div className="font-medium text-sm">
                                  {invoice.amount ? formatCurrency(invoice.amount) : "N/A"}
                                </div>
                              </div>
                            ))}
                          </div>
                        </ScrollArea>
                      </div>
                    )}
                  </TabsContent>
                </Tabs>
              )}
            </CardContent>
            <CardFooter className="flex justify-end flex-shrink-0 border-t p-4">
              {selectedFolder && activeTab === "exports" && (
                <Button onClick={() => router.push("/dashboard/invoices")} className="gap-1.5">
                  <Plus className="h-4 w-4" />
                  Export More Invoices
                </Button>
              )}
            </CardFooter>
          </Card>
        </div>
      </div>

      {/* Delete Folder Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Delete Folder</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this folder? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex flex-col sm:flex-row gap-2 sm:justify-end">
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)} className="sm:w-auto w-full">
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDeleteFolder} className="sm:w-auto w-full">
              Delete Folder
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Rename Folder Dialog */}
      <Dialog open={isRenameDialogOpen} onOpenChange={setIsRenameDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Rename Folder</DialogTitle>
            <DialogDescription>Enter a new name for this folder.</DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="renameFolderName">Folder Name</Label>
              <Input
                id="renameFolderName"
                value={renameFolderName}
                onChange={(e) => setRenameFolderName(e.target.value)}
                className="h-10"
              />
            </div>
          </div>
          <DialogFooter className="flex flex-col sm:flex-row gap-2 sm:justify-end">
            <Button variant="outline" onClick={() => setIsRenameDialogOpen(false)} className="sm:w-auto w-full">
              Cancel
            </Button>
            <Button onClick={handleRenameFolder} disabled={!renameFolderName.trim()} className="sm:w-auto w-full">
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Remove Invoice Dialog */}
      <Dialog open={isRemoveInvoiceDialogOpen} onOpenChange={setIsRemoveInvoiceDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Remove Invoice</DialogTitle>
            <DialogDescription>Are you sure you want to remove this invoice from the folder?</DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex flex-col sm:flex-row gap-2 sm:justify-end">
            <Button variant="outline" onClick={() => setIsRemoveInvoiceDialogOpen(false)} className="sm:w-auto w-full">
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleRemoveInvoice}
              disabled={isRemovingInvoice}
              className="sm:w-auto w-full"
            >
              {isRemovingInvoice ? "Removing..." : "Remove Invoice"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Export Dialog */}
      {selectedFolder && (
        <ExportDialog
          isOpen={isExportDialogOpen}
          onOpenChange={setIsExportDialogOpen}
          invoices={folderInvoices.map((invoice) => ({ ...invoice, id: invoice.id }))}
        />
      )}
    </div>
  )
}
