import { Skeleton } from '@/components/ui/skeleton';

interface CheckoutPriceAmountProps {
  value: number | undefined;
  currencyCode: string | undefined;
  className?: string;
}

export function CheckoutPriceAmount({ value, currencyCode, className = '' }: CheckoutPriceAmountProps) {
  const formatMoney = (amount: number, currency: string | undefined) => {
    if (!currency) return `$${(amount / 100).toFixed(2)}`;
    
    try {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency,
      }).format(amount / 100);
    } catch {
      return `$${(amount / 100).toFixed(2)}`;
    }
  };

  if (value === undefined) {
    return <Skeleton className={`h-6 w-20 bg-neutral-800 ${className}`} />;
  }

  return (
    <span className={`font-medium text-white ${className}`}>
      {formatMoney(value, currencyCode)}
    </span>
  );
}
