'use client';

import { Button } from '@/components/ui/button';
import {
  BadgeCheck,
  Zap,
  FileText,
  MessageSquare,
} from 'lucide-react';
import { useEffect, useState } from 'react';

interface SubscriptionData {
  id: string;
  status: string;
  plan: {
    name: string;
    price: string;
    currency: string;
    billingInterval: string;
  };
  startDate: string | null;
  endDate: string | null;
  renewalDate: string | null;
  isActive: boolean;
}

interface UsageStats {
  chatUsage: number;
  invoiceUsage: number;
  chatLimit: number;
  invoiceLimit: number;
  resetDate: Date;
  daysUntilReset: number;
}

export function UserSubscription() {
  const [subscriptionData, setSubscriptionData] =
    useState<SubscriptionData | null>(null);
  const [usageStats, setUsageStats] = useState<UsageStats | null>(
    null
  );
  const [isLoading, setIsLoading] = useState(true);
  const [usageLoading, setUsageLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch subscription data
        const subscriptionResponse = await fetch(
          '/api/subscriptions/active'
        );
        if (subscriptionResponse.ok) {
          const subscriptionData = await subscriptionResponse.json();
          setSubscriptionData(subscriptionData);
        }

        // Fetch usage stats
        const usageResponse = await fetch('/api/usage/stats');
        if (usageResponse.ok) {
          const usageResult = await usageResponse.json();
          if (usageResult.success) {
            setUsageStats({
              ...usageResult.stats,
              resetDate: new Date(usageResult.stats.resetDate),
            });
          }
        }
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setIsLoading(false);
        setUsageLoading(false);
      }
    };

    fetchData();
  }, []);

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-6 bg-muted rounded w-32 mb-4"></div>
          <div className="space-y-4">
            <div className="h-4 bg-muted rounded w-full"></div>
            <div className="h-2 bg-muted rounded w-full"></div>
            <div className="h-4 bg-muted rounded w-full"></div>
            <div className="h-2 bg-muted rounded w-full"></div>
          </div>
        </div>
      </div>
    );
  }

  const planName = subscriptionData?.plan?.name || 'No Subscription';

  // Calculate usage percentages
  const invoicePercentage =
    usageStats && usageStats.invoiceLimit > 0
      ? (usageStats.invoiceUsage / usageStats.invoiceLimit) * 100
      : 0;

  const chatPercentage =
    usageStats && usageStats.chatLimit > 0
      ? (usageStats.chatUsage / usageStats.chatLimit) * 100
      : 0;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <BadgeCheck className="h-5 w-5 text-primary" />
          <span className="font-medium text-foreground">
            {planName}
          </span>
        </div>
        <Button
          size="sm"
          variant="outline"
          className="border-primary/20 hover:bg-primary/10"
          onClick={() =>
            (window.location.href = '/dashboard/subscription')
          }
        >
          Upgrade
        </Button>
      </div>

      {usageLoading ? (
        <div className="space-y-4">
          <div className="animate-pulse space-y-2">
            <div className="h-4 bg-muted rounded w-3/4"></div>
            <div className="h-2 bg-muted rounded w-full"></div>
            <div className="h-3 bg-muted rounded w-1/2"></div>
          </div>
          <div className="animate-pulse space-y-2">
            <div className="h-4 bg-muted rounded w-3/4"></div>
            <div className="h-2 bg-muted rounded w-full"></div>
            <div className="h-3 bg-muted rounded w-1/2"></div>
          </div>
        </div>
      ) : usageStats ? (
        <>
          {/* Invoice Processing Usage */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Invoice Processing
              </span>
              <span className="text-sm font-medium text-foreground">
                {usageStats.invoiceUsage} / {usageStats.invoiceLimit}
              </span>
            </div>
            <div className="dashboard-progress-bar">
              <div
                className="dashboard-progress-value dashboard-progress-primary"
                style={{
                  width: `${Math.min(invoicePercentage, 100)}%`,
                }}
              ></div>
            </div>
            <p className="text-xs text-muted-foreground">
              You&apos;ve used {Math.round(invoicePercentage)}% of
              your monthly invoice processing limit.
            </p>
          </div>

          {/* Chat Messages Usage */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground flex items-center gap-2">
                <MessageSquare className="h-4 w-4" />
                Chat Messages
              </span>
              <span className="text-sm font-medium text-foreground">
                {usageStats.chatUsage} / {usageStats.chatLimit}
              </span>
            </div>
            <div className="dashboard-progress-bar">
              <div
                className="dashboard-progress-value dashboard-progress-secondary"
                style={{ width: `${Math.min(chatPercentage, 100)}%` }}
              ></div>
            </div>
            <p className="text-xs text-muted-foreground">
              You&apos;ve used {Math.round(chatPercentage)}% of your
              monthly chat limit.
            </p>
          </div>
        </>
      ) : (
        <div className="text-center py-4">
          <p className="text-muted-foreground text-sm">
            Unable to load usage statistics
          </p>
        </div>
      )}

      <div className="rounded-lg bg-card border border-border p-3 flex items-center gap-3">
        <div className="rounded-full bg-primary/20 p-2">
          <Zap className="h-4 w-4 text-primary" />
        </div>
        <div>
          <p className="text-sm font-medium text-foreground">
            Need more resources?
          </p>
          <p className="text-xs text-muted-foreground">
            Upgrade to our Business plan for higher limits.
          </p>
        </div>
      </div>
    </div>
  );
}
