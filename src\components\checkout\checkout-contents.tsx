'use client';

import { PriceSection } from '@/components/checkout/price-section';
import { type Environments, initializePaddle, type Paddle } from '@paddle/paddle-js';
import type { CheckoutEventsData } from '@paddle/paddle-js/types/checkout/events';
import throttle from 'lodash.throttle';
import { useCallback, useEffect, useState } from 'react';

interface CheckoutContentsProps {
  priceId: string;
  userEmail?: string;
  onCheckoutComplete?: (data: any) => void;
  onCheckoutError?: (error: string) => void;
}

export function CheckoutContents({ 
  priceId, 
  userEmail, 
  onCheckoutComplete,
  onCheckoutError 
}: CheckoutContentsProps) {
  const [quantity, setQuantity] = useState(1);
  const [paddle, setPaddle] = useState<Paddle | null>(null);
  const [checkoutData, setCheckoutData] = useState<CheckoutEventsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const handleCheckoutEvents = (event: CheckoutEventsData) => {
    setCheckoutData(event);
    
    // Handle checkout completion
    if (event.status === 'completed' && onCheckoutComplete) {
      onCheckoutComplete(event);
    }
    
    // Handle checkout errors
    if (event.status === 'error' && onCheckoutError) {
      onCheckoutError(event.error?.message || 'Checkout failed');
    }
  };

  const updateItems = useCallback(
    throttle((paddle: Paddle, priceId: string, quantity: number) => {
      paddle.Checkout.updateItems([{ priceId, quantity }]);
    }, 1000),
    [],
  );

  useEffect(() => {
    if (!paddle?.Initialized && process.env.NEXT_PUBLIC_PADDLE_CLIENT_KEY) {
      initializePaddle({
        token: process.env.NEXT_PUBLIC_PADDLE_CLIENT_KEY,
        environment: (process.env.NODE_ENV === 'production' ? 'production' : 'sandbox') as Environments,
        eventCallback: (event) => {
          if (event.data && event.name) {
            handleCheckoutEvents(event.data);
          }
        },
        checkout: {
          settings: {
            variant: 'one-page',
            displayMode: 'inline',
            theme: 'dark',
            allowLogout: !userEmail,
            frameTarget: 'paddle-checkout-frame',
            frameInitialHeight: 450,
            frameStyle: 'width: 100%; background-color: transparent; border: none',
            successUrl: '/dashboard/subscription?success=true',
          },
        },
      }).then(async (paddle) => {
        if (paddle && priceId) {
          setPaddle(paddle);
          setIsLoading(false);
          
          paddle.Checkout.open({
            ...(userEmail && { customer: { email: userEmail } }),
            items: [{ priceId: priceId, quantity: 1 }],
          });
        }
      }).catch((error) => {
        console.error('Failed to initialize Paddle:', error);
        setIsLoading(false);
        if (onCheckoutError) {
          onCheckoutError('Failed to initialize payment system');
        }
      });
    }
  }, [paddle?.Initialized, priceId, userEmail, onCheckoutComplete, onCheckoutError]);

  useEffect(() => {
    if (paddle && priceId && paddle.Initialized) {
      updateItems(paddle, priceId, quantity);
    }
  }, [paddle, priceId, quantity, updateItems]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8 p-4 sm:p-6">
      {/* Left side - Payment Form */}
      <div className="order-2 lg:order-1">
        <h2 className="text-xl font-semibold text-white mb-6">Payment details</h2>
        <div id="paddle-checkout-frame" className="min-h-[450px] bg-neutral-900/50 rounded-lg border border-neutral-800" />
      </div>

      {/* Right side - Order Summary */}
      <div className="order-1 lg:order-2">
        <PriceSection
          checkoutData={checkoutData}
          quantity={quantity}
          onQuantityChange={setQuantity}
        />
      </div>
    </div>
  );
}
