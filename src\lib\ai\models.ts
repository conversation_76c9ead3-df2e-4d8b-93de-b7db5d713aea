import { groq } from '@ai-sdk/groq';
import {
  customProvider,
  extractReasoningMiddleware,
  wrapLanguageModel,
} from 'ai';

export const DEFAULT_CHAT_MODEL: string = 'chat-model-small';

export const myProvider = customProvider({
  languageModels: {
    'chat-model-small': groq('qwen-qwq-32b'),
    'chat-model-large': groq('meta-llama-llama-4-scout-17b-16e-instruct'),
    'chat-model-reasoning': wrapLanguageModel({
      model: groq('meta-llama-llama-4-scout-17b-16e-instruct'),
      middleware: extractReasoningMiddleware({ tagName: 'think' }),
    }),
    'title-model': groq('qwen-qwq-32b'),
    'artifact-model': groq('meta-llama-llama-4-scout-17b-16e-instruct'),
    'invoice-extraction-model': groq('meta-llama-llama-4-scout-17b-16e-instruct'),
  },
});

interface ChatModel {
  id: string;
  name: string;
  description: string;
}

export const chatModels: Array<ChatModel> = [
  {
    id: 'chat-model-small',
    name: 'Small model',
    description: 'Small model for fast, lightweight tasks',
  },
  {
    id: 'chat-model-large',
    name: 'Large model',
    description: 'Large model for complex, multi-step tasks',
  },
  {
    id: 'chat-model-reasoning',
    name: 'Reasoning model',
    description: 'Uses advanced reasoning',
  },
];
