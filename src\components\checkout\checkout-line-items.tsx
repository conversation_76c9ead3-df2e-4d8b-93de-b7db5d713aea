import { QuantityField } from '@/components/checkout/quantity-field';
import { CheckoutPriceAmount } from '@/components/checkout/checkout-price-amount';
import { Separator } from '@/components/ui/separator';

interface CheckoutLineItemsProps {
  checkoutData: any;
  quantity: number;
  onQuantityChange: (quantity: number) => void;
}

export function CheckoutLineItems({ 
  checkoutData, 
  quantity, 
  onQuantityChange 
}: CheckoutLineItemsProps) {
  const item = checkoutData?.items?.[0];
  const totals = checkoutData?.totals;

  return (
    <div className="space-y-4">
      {/* Product Line Item */}
      {item && (
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <h3 className="text-white font-medium">
              {item.price_name || item.product?.name || 'Subscription'}
            </h3>
            <p className="text-sm text-gray-400">
              {item.product?.description || 'Monthly subscription'}
            </p>
          </div>
          
          <div className="flex items-center gap-4">
            <QuantityField
              quantity={quantity}
              onQuantityChange={onQuantityChange}
            />
            <div className="w-20 text-right">
              <CheckoutPriceAmount
                value={item.totals?.subtotal}
                currencyCode={totals?.currency_code}
              />
            </div>
          </div>
        </div>
      )}

      <Separator className="bg-neutral-800" />

      {/* Pricing Breakdown */}
      <div className="space-y-3">
        <div className="flex justify-between text-sm">
          <span className="text-gray-400">Subtotal</span>
          <CheckoutPriceAmount
            value={totals?.subtotal}
            currencyCode={totals?.currency_code}
            className="text-sm"
          />
        </div>

        {totals?.tax && totals.tax > 0 && (
          <div className="flex justify-between text-sm">
            <span className="text-gray-400">Tax</span>
            <CheckoutPriceAmount
              value={totals.tax}
              currencyCode={totals?.currency_code}
              className="text-sm"
            />
          </div>
        )}

        {totals?.discount && totals.discount > 0 && (
          <div className="flex justify-between text-sm">
            <span className="text-gray-400">Discount</span>
            <CheckoutPriceAmount
              value={-totals.discount}
              currencyCode={totals?.currency_code}
              className="text-sm text-green-400"
            />
          </div>
        )}

        <Separator className="bg-neutral-800" />

        <div className="flex justify-between">
          <span className="text-white font-semibold">Due today</span>
          <CheckoutPriceAmount
            value={totals?.total}
            currencyCode={totals?.currency_code}
            className="text-lg font-semibold"
          />
        </div>
      </div>
    </div>
  );
}
