import { tool } from 'ai';
import { z } from 'zod';
import db from '@/db/db';
import { getAuthenticatedUserId } from '@/lib/auth-helpers';

export const addInvoiceToFolder = tool({
  description: 'Places an invoice into an export/organization folder. Creates the folder if needed.',
  parameters: z.object({
    invoiceId: z.string().describe('The ID of the invoice to add to the folder'),
    folderName: z.string().describe('The name of the folder to add the invoice to'),
  }),
  execute: async ({ invoiceId, folderName }) => {
    try {
      // Get the authenticated user ID
      const userId = await getAuthenticatedUserId();

      // Check if the invoice exists and belongs to the current user
      let invoice = await db.invoice.findUnique({
        where: { id: invoiceId },
      });

      // If not found by ID, try to find by invoice number
      if (!invoice) {
        invoice = await db.invoice.findFirst({
          where: {
            invoiceNumber: invoiceId,
            userId
          },
        });
      }

      if (!invoice) {
        return {
          success: false,
          message: `Invoice with ID or number ${invoiceId} not found. Please check the ID and try again.`,
        };
      }

      // Check if the invoice belongs to the current user
      if (invoice.userId !== userId) {
        return {
          success: false,
          message: 'You do not have permission to access this invoice.',
        };
      }

      // Check if the folder exists
      let folder = await db.exportFolder.findFirst({
        where: {
          name: {
            equals: folderName,
            mode: 'insensitive',
          },
          userId,
        },
      });

      // If the folder doesn't exist, create it
      if (!folder) {
        folder = await db.exportFolder.create({
          data: {
            name: folderName,
            userId,
            invoiceCount: 0,
          },
        });
      }

      // Check if the invoice is already in the folder
      const existingRelation = await db.folderInvoice.findUnique({
        where: {
          folderId_invoiceId: {
            folderId: folder.id,
            invoiceId: invoiceId,
          },
        },
      });

      if (existingRelation) {
        return {
          invoiceId,
          folderId: folder.id,
          folderName: folder.name,
          message: `Invoice ${invoice.invoiceNumber || ''} is already in folder "${folder.name}".`,
        };
      }

      // Add the invoice to the folder
      await db.$transaction(async (prisma) => {
        // Create the relation
        await prisma.folderInvoice.create({
          data: {
            folderId: folder!.id,
            invoiceId: invoiceId,
          },
        });

        // Update the folder's invoice count
        await prisma.exportFolder.update({
          where: { id: folder!.id },
          data: {
            invoiceCount: {
              increment: 1,
            },
            updatedAt: new Date(),
          },
        });
      });

      return {
        invoiceId,
        invoiceNumber: invoice.invoiceNumber || '',
        folderId: folder.id,
        folderName: folder.name,
        message: `Invoice ${invoice.invoiceNumber || ''} added to folder "${folder.name}".`,
      };
    } catch (error) {
      console.error('Error adding invoice to folder:', error);
      return {
        success: false,
        message: 'Failed to add invoice to folder. Please try again.',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  },
});
