import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server'
import { NextResponse } from 'next/server'

const isPublicRoute = createRouteMatcher([
  '/',               
  '/sign-in(.*)',
  '/sign-up(.*)',
  '/onboarding(.*)',
  '/accept-invite(.*)',
  '/api/webhook(.*)',
  '/api/email(.*)',  // Allow email API routes to be public
  '/waitlist'        // Allow waitlist page
])

// Routes that should be accessible in production "coming soon" mode
// const isProductionAllowedRoute = createRouteMatcher([
//   '/',               // Landing page
//   '/waitlist',       // Waitlist/Coming soon page
//   '/api/webhook(.*)',
//   '/api/email(.*)'   // Email API endpoints needed for waitlist signup
// ])

export default clerkMiddleware(async (auth, req) => {
  // Check if in production
  // const isProduction = process.env.NODE_ENV === 'production';

  // WAITLIST DISABLED FOR PRODUCTION TESTING
  // Commented out production restrictions to allow full dashboard access
  /*
  if (isProduction) {
    // In production, only allow landing page, waitlist, and essential API routes
    if (!isProductionAllowedRoute(req)) {
      // Redirect to landing page
      const url = new URL('/', req.url);
      return NextResponse.redirect(url);
    }
  } else {
    // In development, use normal authentication rules
    if (!isPublicRoute(req)) {
      await auth.protect();
    }
  }
  */

  // Enable full authentication for production testing
  if (!isPublicRoute(req)) {
    await auth.protect();
  }
  
  return NextResponse.next();
})

export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
    // Always run for API routes
    '/(api|trpc)(.*)',
  ],
}
