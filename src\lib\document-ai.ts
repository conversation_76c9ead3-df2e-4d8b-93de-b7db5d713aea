/**
 * Extract text content from a document (image or PDF)
 * Enhanced to handle multi-page PDFs and handwritten content
 */
export async function extractTextFromDocument(
  file: File
): Promise<string> {
  try {
    // Log file information for debugging
    console.log('Processing file:', {
      name: file.name,
      type: file.type,
      size: file.size,
      sizeInMB: (file.size / 1024 / 1024).toFixed(2),
    });

    // Check file size (limit to 50MB to avoid backend issues)
    const maxSizeInMB = 50;
    if (file.size > maxSizeInMB * 1024 * 1024) {
      throw new Error(
        `File size (${(file.size / 1024 / 1024).toFixed(2)}MB) exceeds maximum allowed size of ${maxSizeInMB}MB`
      );
    }

    // Create FormData for the API request
    const formData = new FormData();
    formData.append('file', file);

    // Make API request to extract text using backend URL from environment
    const backendUrl = process.env.BACKEND_URL;

    if (!backendUrl) {
      throw new Error(
        'BACKEND_URL environment variable is not configured'
      );
    }

    // Add timeout for PDF processing (2 minutes)
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 120000); // 2 minutes

    const response = await fetch(
      `${backendUrl}/extract/pdf-image-text`,
      {
        method: 'POST',
        body: formData,
        signal: controller.signal,
      }
    );
    clearTimeout(timeoutId);

    if (!response.ok) {
      // Get more detailed error information
      let errorMessage = `API request failed with status ${response.status}`;
      try {
        const errorData = await response.json();
        if (errorData.detail) {
          errorMessage += `: ${errorData.detail}`;
        }
      } catch (e) {
        // If we can't parse the error response, get the text
        try {
          const errorText = await response.text();
          if (errorText) {
            errorMessage += `: ${errorText}`;
          }
        } catch (e2) {
          // Ignore if we can't get error details
        }
      }
      throw new Error(errorMessage);
    }

    const result = await response.json();
    return result.text || '';
  } catch (error) {
    console.error('Error extracting text from document:', error);

    // Handle timeout errors specifically
    if (error instanceof Error && error.name === 'AbortError') {
      throw new Error(
        'PDF processing timeout (2 minutes) - file may be too large or complex'
      );
    }

    throw error;
  }
}

/**
 * Classify document type (invoice, receipt, etc.) with enhanced detection
 */
export async function classifyDocument(
  text: string
): Promise<string> {
  try {
    // Enhanced classification with more comprehensive patterns
    const lowerText = text.toLowerCase();

    // Invoice patterns - expanded to include international variations
    const invoicePatterns = [
      'invoice',
      'bill to',
      'factura',
      'rechnung',
      'facture',
      'fattura',
      '发票',
      'インボイス',
      'فاتورة',
      'חשבונית',
      'счет',
      'tax invoice',
      'billing statement',
      'payment due',
      'invoice number',
      'invoice no',
      'invoice #',
      'inv #',
      'inv no',
      'invoice date',
    ];

    // Receipt patterns
    const receiptPatterns = [
      'receipt',
      'payment received',
      'paid',
      'payment confirmation',
      'proof of payment',
      'recibo',
      'quittung',
      'reçu',
      'ricevuta',
      '收据',
      '領収書',
      'إيصال',
      'קבלה',
      'квитанция',
      'thank you for your purchase',
      'cash receipt',
      'payment receipt',
    ];

    // Purchase order patterns
    const poPatterns = [
      'purchase order',
      'p.o.',
      'p/o',
      'order confirmation',
      'order form',
      'orden de compra',
      'bestellung',
      'bon de commande',
      "ordine d'acquisto",
      '采购订单',
      '注文書',
      'أمر شراء',
      'הזמנת רכש',
      'заказ на покупку',
    ];

    // Quote/Estimate patterns
    const quotePatterns = [
      'quote',
      'estimate',
      'quotation',
      'proposal',
      'pro forma',
      'proforma',
      'presupuesto',
      'angebot',
      'devis',
      'preventivo',
      '报价',
      '見積もり',
      'عرض أسعار',
      'הצעת מחיר',
      'коммерческое предложение',
    ];

    // Statement patterns
    const statementPatterns = [
      'statement',
      'account statement',
      'statement of account',
      'monthly statement',
      'estado de cuenta',
      'kontoauszug',
      'relevé de compte',
      'estratto conto',
      '对账单',
      '取引明細書',
      'كشف حساب',
      'דף חשבון',
      'выписка по счету',
    ];

    // Credit note patterns
    const creditNotePatterns = [
      'credit note',
      'credit memo',
      'credit memorandum',
      'refund',
      'nota de crédito',
      'gutschrift',
      'note de crédit',
      'nota di credito',
      '贷记通知单',
      'クレジットノート',
      'إشعار دائن',
      'הודעת זיכוי',
      'кредитное авизо',
    ];

    // Check for each document type using the expanded patterns
    for (const pattern of invoicePatterns) {
      if (lowerText.includes(pattern)) {
        return 'invoice';
      }
    }

    for (const pattern of receiptPatterns) {
      if (lowerText.includes(pattern)) {
        return 'receipt';
      }
    }

    for (const pattern of poPatterns) {
      if (lowerText.includes(pattern)) {
        return 'purchase_order';
      }
    }

    for (const pattern of quotePatterns) {
      if (lowerText.includes(pattern)) {
        return 'quote';
      }
    }

    for (const pattern of statementPatterns) {
      if (lowerText.includes(pattern)) {
        return 'statement';
      }
    }

    for (const pattern of creditNotePatterns) {
      if (lowerText.includes(pattern)) {
        return 'credit_note';
      }
    }

    // Additional heuristics for invoice detection when standard keywords are missing
    // Check for patterns that strongly suggest an invoice
    if (
      (lowerText.includes('total') &&
        (lowerText.includes('due') ||
          lowerText.includes('amount'))) ||
      (lowerText.includes('payment') &&
        lowerText.includes('terms')) ||
      (lowerText.includes('tax') && lowerText.includes('subtotal')) ||
      (lowerText.match(/inv[^a-z]/i) && lowerText.match(/\d{4,}/)) // Invoice number pattern
    ) {
      return 'invoice';
    }

    // Default to invoice if we can't determine the type
    return 'invoice';
  } catch (error) {
    console.error('Error classifying document:', error);
    // Default to invoice if there's an error
    return 'invoice';
  }
}
