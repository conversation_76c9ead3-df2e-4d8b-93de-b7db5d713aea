/**
 * Client-side helpers for authentication
 * These functions are meant to be used in client components
 */

/**
 * Gets the database user ID for the currently authenticated Clerk user
 * Creates a user record in the database if it doesn't exist
 */
export async function getClientUserId(): Promise<string | null> {
  try {
    // Call our API endpoint to get the current user
    const response = await fetch('/api/auth/user');

    if (!response.ok) {
      return null;
    }

    const data = await response.json();
    return data.id;
  } catch (error) {
    console.error('Error getting current user ID:', error);
    return null;
  }
}

/**
 * Checks if the current user is authorized to access a resource
 * @param resourceUserId The user ID associated with the resource
 */
export async function isClientAuthorized(resourceUserId: string): Promise<boolean> {
  const currentUserId = await getClientUserId();
  return currentUserId === resourceUserId;
}
