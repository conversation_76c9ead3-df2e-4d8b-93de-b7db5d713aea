'use client';

import { ReactNode, useEffect } from 'react';
import { lemonSqueezySetup } from '@lemonsqueezy/lemonsqueezy.js';

interface LemonSqueezyProviderProps {
  children: ReactNode;
  apiKey?: string;
}

export function LemonSqueezyProvider({ 
  children,
  apiKey
}: LemonSqueezyProviderProps) {
  useEffect(() => {
    if (apiKey) {
      lemonSqueezySetup({
        apiKey,
        onError: (error) => {
          // Removed: console.error('Lemon Squeezy API error:', error);
        },
      });
    }
  }, [apiKey]);

  return <>{children}</>;
} 