'use client';

import { Suspense } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Ta<PERSON>,
  Ta<PERSON>Content,
  <PERSON><PERSON>List,
  TabsTrigger,
} from '@/components/ui/tabs';
import { CalendarDateRangePicker } from '@/components/dashboard/dashboard-comp/date-range-picker';
import { Overview } from '@/components/dashboard/dashboard-comp/overview';
import { RecentInvoices } from '@/components/dashboard/dashboard-comp/recent-invoices';
import { Search } from '@/components/dashboard/dashboard-comp/search';
import { InvoiceStats } from '@/components/dashboard/dashboard-comp/invoice-stats';
import { VendorDistribution } from '@/components/dashboard/dashboard-comp/vendor-distribution';
import { DashboardSkeleton } from '@/components/dashboard/dashboard-comp/dashboard-skeleton';
import { UserSubscription } from '../dashboard-comp/user-subscription';
import { UsageDashboard } from '../usage/usage-dashboard';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { AIInsights } from '../dashboard-comp/ai-insights';
import { AnalyticsContent } from '@/components/dashboard/analytics/analytics-dashboard';

export default function DashboardPage() {
  return (
    <DashboardLayout>
      <div className="flex min-h-screen flex-col">
        <div className="flex-1 space-y-4 p-4 pt-6 md:p-8">
          <div className="flex flex-col items-start justify-between space-y-2 md:flex-row md:items-center md:space-y-0">
            <h2 className="text-3xl font-bold tracking-tight">
              Dashboard
            </h2>
            <div className="flex items-center space-x-2">
              <CalendarDateRangePicker />
              <Search />
            </div>
          </div>
          <Tabs defaultValue="overview" className="space-y-4">
            <TabsList className="tabs-container  bg-white dark:bg-[#0B1739] border border-[#0097B1]/30 dark:border-[#0097B1]/30 rounded-xl">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
              <TabsTrigger value="insights">AI Insights</TabsTrigger>
            </TabsList>
            <TabsContent value="overview" className="space-y-4">
              <Suspense fallback={<DashboardSkeleton />}>
                <InvoiceStats />
                <UsageDashboard />
                <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-7">
                  <Card
                    figmaBlue
                    className="col-span-1 md:col-span-2 lg:col-span-4"
                  >
                    <CardHeader className="border-b border-[#0097B1]/20 dark:border-[#0097B1]/20">
                      <CardTitle>Cash Flow</CardTitle>
                      <CardDescription>
                        Monthly income vs. expenses for the current
                        year
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="p-4">
                      <Overview />
                    </CardContent>
                  </Card>
                  <Card
                    figmaPurple
                    className="col-span-1 md:col-span-2 lg:col-span-3"
                  >
                    <CardHeader className="border-b border-[#4000FF]/20 dark:border-[#4000FF]/20">
                      <CardTitle>Your Subscription</CardTitle>
                      <CardDescription>
                        Current plan details and usage
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="p-4">
                      <UserSubscription />
                    </CardContent>
                  </Card>
                </div>
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
                  <Card figma className="lg:col-span-3">
                    <CardHeader className="border-b border-[#0097B1]/20 dark:border-[#0097B1]/20">
                      <CardTitle>Vendor Distribution</CardTitle>
                      <CardDescription>
                        Top vendors by invoice volume
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="pt-4">
                      <VendorDistribution />
                    </CardContent>
                  </Card>
                  <Card figmaBlue className="lg:col-span-4">
                    <CardHeader className="border-b border-[#0097B1]/20 dark:border-[#0097B1]/20">
                      <CardTitle>Recent Invoices</CardTitle>
                      <CardDescription>
                        Latest processed invoices
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="pt-4">
                      <RecentInvoices />
                    </CardContent>
                  </Card>
                </div>
              </Suspense>
            </TabsContent>
            <TabsContent value="analytics" className="space-y-4">
              <Suspense fallback={<DashboardSkeleton />}>
                <AnalyticsContent />
              </Suspense>
            </TabsContent>
            <TabsContent value="insights" className="space-y-4">
              <Card figmaPurple>
                <CardHeader className="border-b border-[#4000FF]/20 dark:border-[#4000FF]/20">
                  <CardTitle>AI-Powered Insights</CardTitle>
                  <CardDescription>
                    Smart recommendations based on your invoice data
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-4">
                  <AIInsights />
                </CardContent>
              </Card>
            </TabsContent>
            <TabsContent value="figma-cards" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Figma Card Examples</CardTitle>
                  <CardDescription>
                    Showcase of the new card styles inspired by the
                    Figma design
                  </CardDescription>
                </CardHeader>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </DashboardLayout>
  );
}
