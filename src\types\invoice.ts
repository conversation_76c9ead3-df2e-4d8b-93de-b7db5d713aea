export interface VendorInfo {
  name?: string;
  address?: string;
  taxId?: string;
  registrationNumber?: string;
  phone?: string;
  email?: string;
  website?: string;
  contactPerson?: string;
  [key: string]: string | number | boolean | undefined;
}

export interface CustomerInfo {
  name?: string;
  address?: string;
  billingAddress?: string;
  shippingAddress?: string;
  customerID?: string;
  contactPerson?: string;
  phone?: string;
  email?: string;
  [key: string]: string | number | boolean | undefined;
}

export interface LineItem {
  itemCode?: string;
  description?: string;
  quantity?: string;
  unit?: string;
  unitPrice?: string;
  discount?: string;
  taxRate?: string;
  amount?: string;
  [key: string]: string | number | boolean | undefined;
}

export interface AdditionalTax {
  name: string;
  rate: string;
  amount: string;
}

export interface FinancialInfo {
  subtotal?: string;
  tax?: string;
  taxRate?: string;
  discount?: string;
  shipping?: string;
  total?: string;
  currency?: string;
  amountPaid?: string;
  balanceDue?: string;
  taxName?: string;
  additionalTaxes?: AdditionalTax[];
  [key: string]:
    | string
    | number
    | boolean
    | AdditionalTax[]
    | undefined;
}

export interface PaymentInfo {
  method?: string;
  terms?: string;
  bankName?: string;
  accountName?: string;
  accountNumber?: string;
  routingNumber?: string;
  swiftCode?: string;
  dueDate?: string;
  [key: string]: string | number | boolean | undefined;
}

export interface ConfidenceScores {
  overall: number;
  fields?: Record<string, number>;
}

export interface CategorySuggestion {
  name: string;
  confidence: number;
}

export interface VendorTypeSuggestion {
  name: string;
  confidence: number;
}

export interface AuditIssue {
  type: string;
  severity: 'LOW' | 'MEDIUM' | 'HIGH';
  description: string;
  affectedFields?: string[];
}

export interface TaxCompliance {
  status: 'COMPLIANT' | 'NON_COMPLIANT' | 'UNKNOWN';
  details?: string;
}

export interface FraudIndicators {
  score: number;
  flags?: string[];
}

export interface AuditInfo {
  status: 'PASS' | 'WARNING' | 'FAIL';
  issues?: AuditIssue[];
  taxCompliance?: TaxCompliance;
  fraudIndicators?: FraudIndicators;
}

export interface SuggestionInfo {
  categories?: CategorySuggestion[];
  vendorTypes?: VendorTypeSuggestion[];
  selectedCategory?: string | null;
  selectedVendorType?: string | null;
  category?: string; // Deprecated but kept for backward compatibility
  vendorType?: string; // Deprecated but kept for backward compatibility
  matchedRules?: string[];
  invoiceType?: 'PURCHASE' | 'SALES'; // Indicates if this is an incoming or outgoing invoice
}

export interface MetaInfo {
  language?: string;
  languageName?: string;
  country?: string;
  countryCode?: string;
  region?: string; // e.g., "Europe", "Americas", "Asia", "Arab"
  confidence?: ConfidenceScores;
  processingTime?: number;
  suggestions?: SuggestionInfo;
  audit?: AuditInfo;
  enhancedData?: Record<string, unknown>; // Store enhanced extraction data
  // Backend debugging properties
  backendParsingIssue?: boolean; // Indicates if backend had JSON parsing issues
  recoveredFromError?: boolean; // Indicates if data was recovered from malformed backend response
  recoveredFromRegex?: boolean; // Indicates if data was recovered using regex extraction
}

export interface InvoiceData {
  id?: string;
  invoiceNumber?: string;
  date?: string;
  dueDate?: string;
  referenceCode?: string;
  poNumber?: string;
  orderNumber?: string;
  vendor?: VendorInfo;
  customer?: CustomerInfo;
  lineItems: LineItem[];
  financials?: FinancialInfo;
  payment?: PaymentInfo;
  notes?: string;
  termsAndConditions?: string;
  additionalFields?: Record<
    string,
    string | number | boolean | undefined
  >;
  // File URLs for storage
  fileUrl?: string;
  thumbnailUrl?: string;
  documentType?: string;
  // Meta information
  meta?: MetaInfo;
  source?: {
    type: string;
    from?: string;
    subject?: string;
    date?: string;
    messageId?: string;
  };
  [key: string]:
    | string
    | number
    | boolean
    | LineItem[]
    | VendorInfo
    | CustomerInfo
    | FinancialInfo
    | PaymentInfo
    | Record<string, string | number | boolean | undefined>
    | MetaInfo
    | undefined;
}
