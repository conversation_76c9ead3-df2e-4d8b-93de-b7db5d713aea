import { auth, currentUser } from '@clerk/nextjs/server';
import db from '@/db/db';
import { redirect } from 'next/navigation';
import crypto from 'crypto';

// Paddle API configuration
const PADDLE_API_URL =
  process.env.PADDLE_ENVIRONMENT === 'production'
    ? 'https://api.paddle.com'
    : 'https://sandbox-api.paddle.com';

const PADDLE_HEADERS = {
  Authorization: `Bearer ${process.env.PADDLE_API_KEY}`,
  'Content-Type': 'application/json',
};

// Paddle webhook event types
export type PaddleEventType =
  | 'subscription.created'
  | 'subscription.updated'
  | 'subscription.activated'
  | 'subscription.canceled'
  | 'subscription.paused'
  | 'subscription.resumed'
  | 'transaction.completed'
  | 'transaction.updated'
  | 'customer.created'
  | 'customer.updated';

// Paddle subscription status mapping
const PADDLE_STATUS_MAP: Record<string, string> = {
  active: 'active',
  trialing: 'trialing',
  past_due: 'past_due',
  canceled: 'canceled',
  unpaid: 'unpaid',
  paused: 'paused',
};

// Interface for Paddle webhook data
interface PaddleWebhookData {
  event_id: string;
  event_type: PaddleEventType;
  occurred_at: string;
  data: {
    id: string;
    status?: string;
    customer_id?: string;
    items?: Array<{
      price_id?: string;
      product_id: string;
      quantity: number;
      price?: {
        id: string;
        product_id: string;
      };
    }>;
    billing_cycle?: {
      interval: string;
      frequency: number;
    };
    next_billed_at?: string;
    canceled_at?: string;
    paused_at?: string;
    management_urls?: {
      update_payment_method: string;
      cancel: string;
    };
    collection_mode?: string;
    billing_details?: {
      payment_terms: {
        interval: string;
        frequency: number;
      };
    };
    custom_data?: Record<string, any>;
  };
}

/**
 * Create a checkout session for Paddle
 */
export async function createPaddleCheckout(
  priceId: string,
  customData?: Record<string, any>
) {
  try {
    const { userId } = await auth();
    if (!userId) {
      throw new Error('User not authenticated');
    }

    const user = await currentUser();
    if (!user) {
      throw new Error('User not found');
    }

    // Get user from database
    const dbUser = await db.user.findUnique({
      where: { clerkId: userId },
    });

    if (!dbUser) {
      throw new Error('Database user not found');
    }

    const checkoutData = {
      items: [
        {
          price_id: priceId,
          quantity: 1,
        },
      ],
      customer_email: user.emailAddresses[0]?.emailAddress,
      custom_data: {
        user_id: dbUser.id,
        clerk_id: userId,
        ...customData,
      },
      return_url: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/subscription?success=true`,
      discount_id: null, // Can be used for promotional codes
    };

    const response = await fetch(`${PADDLE_API_URL}/transactions`, {
      method: 'POST',
      headers: PADDLE_HEADERS,
      body: JSON.stringify(checkoutData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Paddle checkout error:', errorData);
      throw new Error(`Paddle API error: ${response.status}`);
    }

    const result = await response.json();
    return { success: true, data: result };
  } catch (error) {
    console.error('Error creating Paddle checkout:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Get customer portal URL for subscription management
 */
export async function getPaddleCustomerPortal(
  subscriptionId: string
) {
  try {
    const { userId } = await auth();
    if (!userId) {
      throw new Error('User not authenticated');
    }

    // Verify user owns this subscription
    const subscription = await db.subscription.findFirst({
      where: {
        paddleSubscriptionId: subscriptionId,
        user: { clerkId: userId },
      },
    });

    if (!subscription) {
      throw new Error('Subscription not found or access denied');
    }

    // Get subscription details from Paddle to get management URLs
    const response = await fetch(
      `${PADDLE_API_URL}/subscriptions/${subscriptionId}`,
      {
        headers: PADDLE_HEADERS,
      }
    );

    if (!response.ok) {
      throw new Error(
        `Failed to fetch subscription: ${response.status}`
      );
    }

    const subscriptionData = await response.json();
    const managementUrls = subscriptionData.data?.management_urls;

    if (!managementUrls) {
      throw new Error('Management URLs not available');
    }

    return {
      success: true,
      data: {
        update_payment_method: managementUrls.update_payment_method,
        cancel: managementUrls.cancel,
      },
    };
  } catch (error) {
    console.error('Error getting customer portal:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Cancel a Paddle subscription
 */
export async function cancelPaddleSubscription(
  subscriptionId: string,
  effectiveFrom:
    | 'immediately'
    | 'next_billing_period' = 'next_billing_period'
) {
  try {
    const { userId } = await auth();
    if (!userId) {
      throw new Error('User not authenticated');
    }

    // Verify user owns this subscription
    const subscription = await db.subscription.findFirst({
      where: {
        paddleSubscriptionId: subscriptionId,
        user: { clerkId: userId },
      },
    });

    if (!subscription) {
      throw new Error('Subscription not found or access denied');
    }

    const cancelData = {
      effective_from: effectiveFrom,
    };

    const response = await fetch(
      `${PADDLE_API_URL}/subscriptions/${subscriptionId}/cancel`,
      {
        method: 'POST',
        headers: PADDLE_HEADERS,
        body: JSON.stringify(cancelData),
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Paddle cancellation error:', errorData);
      throw new Error(
        `Failed to cancel subscription: ${response.status}`
      );
    }

    const result = await response.json();

    // Update local database
    await db.subscription.update({
      where: { id: subscription.id },
      data: {
        status: 'canceled',
        statusFormatted: 'Canceled',
        updatedAt: new Date(),
      },
    });

    return { success: true, data: result };
  } catch (error) {
    console.error('Error canceling subscription:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Verify Paddle webhook signature
 */
export function verifyPaddleWebhook(
  body: string,
  signature: string
): boolean {
  try {
    if (!process.env.PADDLE_WEBHOOK_SECRET) {
      console.warn(
        'PADDLE_WEBHOOK_SECRET not configured - skipping signature verification'
      );
      return true; // Allow webhook in development if secret not set
    }

    // Signature verification enabled for production
    // Development bypass removed - all webhooks now verified

    console.log('🔍 Verifying Paddle webhook signature...');

    // Paddle sends signature in format "ts=timestamp;h1=signature"
    // Parse both timestamp and hash
    const captures = signature.match(/^ts=(\d+);h1=(.+)$/);

    if (!captures) {
      console.error(
        '❌ Invalid signature format - expected ts=timestamp;h1=hash'
      );
      return false;
    }

    const [, timestamp, signatureHash] = captures;

    // Paddle signature calculation: HMAC-SHA256(timestamp + ":" + body)
    const signedPayload = timestamp + ':' + body;
    const expectedSignature = crypto
      .createHmac('sha256', process.env.PADDLE_WEBHOOK_SECRET)
      .update(signedPayload)
      .digest('hex');

    const isValid = crypto.timingSafeEqual(
      Buffer.from(expectedSignature, 'hex'),
      Buffer.from(signatureHash, 'hex')
    );

    if (isValid) {
      console.log(
        '✅ Paddle webhook signature verified successfully'
      );
    } else {
      console.error(
        '❌ Paddle webhook signature verification failed'
      );
    }

    return isValid;
  } catch (error) {
    console.error(
      '❌ Error verifying Paddle webhook signature:',
      error
    );
    return false;
  }
}

/**
 * Process Paddle webhook events
 */
export async function processPaddleWebhook(
  eventData: PaddleWebhookData
) {
  try {
    console.log(`Processing Paddle webhook: ${eventData.event_type}`);

    switch (eventData.event_type) {
      case 'subscription.created':
        await handleSubscriptionCreated(eventData);
        break;

      case 'subscription.activated':
        await handleSubscriptionActivated(eventData);
        break;

      case 'subscription.updated':
        await handleSubscriptionUpdated(eventData);
        break;

      case 'subscription.canceled':
        await handleSubscriptionCanceled(eventData);
        break;

      case 'subscription.paused':
        await handleSubscriptionPaused(eventData);
        break;

      case 'subscription.resumed':
        await handleSubscriptionResumed(eventData);
        break;

      case 'transaction.completed':
        await handleTransactionCompleted(eventData);
        break;

      default:
        console.log(
          `Unhandled webhook event type: ${eventData.event_type}`
        );
    }

    return { success: true };
  } catch (error) {
    console.error('Error processing Paddle webhook:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Handle subscription created webhook
 */
async function handleSubscriptionCreated(
  eventData: PaddleWebhookData
) {
  const { data } = eventData;
  const customData = data.custom_data;

  if (!customData?.user_id) {
    throw new Error('No user_id in custom_data');
  }

  const userId = customData.user_id;
  const item = data.items?.[0];

  if (!item) {
    throw new Error('No items in subscription data');
  }

  // Find or create plan by Paddle price ID
  const priceId = item.price_id || item.price?.id;

  let plan = await db.plan.findFirst({
    where: { paddlePriceId: priceId },
  });

  if (!plan) {
    // Create a new plan record for Paddle with a unique variantId
    const maxVariantId = await db.plan.findFirst({
      orderBy: { variantId: 'desc' },
      select: { variantId: true },
    });
    const nextVariantId = (maxVariantId?.variantId || 0) + 1;

    plan = await db.plan.create({
      data: {
        productId: parseInt(item.product_id) || 0,
        variantId: nextVariantId,
        paddlePriceId: priceId,
        name: `Paddle Plan ${priceId}`,
        description: 'Paddle subscription plan',
        price: '0', // Will be updated when we have pricing info
        interval: data.billing_cycle?.interval || 'month',
        intervalCount: data.billing_cycle?.frequency || 1,
      },
    });
  }

  // Create subscription record
  await db.subscription.create({
    data: {
      provider: 'paddle',
      paddleSubscriptionId: data.id,
      paddleCustomerId: data.customer_id || '',
      paddleProductId: item.product_id,
      paddlePriceId: item.price_id,
      name: `Subscription ${data.id}`,
      email: customData.email || '',
      status: PADDLE_STATUS_MAP[data.status || 'active'] || 'active',
      statusFormatted: data.status || 'Active',
      renewsAt: data.next_billed_at || null,
      endsAt: data.canceled_at || null,
      price: '0', // Will be updated from transaction data
      userId: userId,
      planId: plan.id,
      cancelUrl: data.management_urls?.cancel || null,
      updateUrl: data.management_urls?.update_payment_method || null,
    },
  });

  console.log(
    `Created Paddle subscription ${data.id} for user ${userId}`
  );
}

/**
 * Handle subscription activated webhook
 */
async function handleSubscriptionActivated(
  eventData: PaddleWebhookData
) {
  const { data } = eventData;

  const subscription = await db.subscription.findUnique({
    where: { paddleSubscriptionId: data.id },
  });

  if (!subscription) {
    console.warn(`Subscription ${data.id} not found for activation`);
    return;
  }

  await db.subscription.update({
    where: { paddleSubscriptionId: data.id },
    data: {
      status: 'active',
      statusFormatted: 'Active',
      updatedAt: new Date(),
    },
  });

  console.log(`Activated Paddle subscription ${data.id}`);
}

/**
 * Handle subscription updated webhook
 */
async function handleSubscriptionUpdated(
  eventData: PaddleWebhookData
) {
  const { data } = eventData;

  const subscription = await db.subscription.findUnique({
    where: { paddleSubscriptionId: data.id },
  });

  if (!subscription) {
    console.warn(`Subscription ${data.id} not found for update`);
    return;
  }

  await db.subscription.update({
    where: { paddleSubscriptionId: data.id },
    data: {
      status: PADDLE_STATUS_MAP[data.status || 'active'] || 'active',
      statusFormatted: data.status || 'Active',
      renewsAt: data.next_billed_at || null,
      endsAt: data.canceled_at || null,
      cancelUrl: data.management_urls?.cancel || null,
      updateUrl: data.management_urls?.update_payment_method || null,
      updatedAt: new Date(),
    },
  });

  console.log(`Updated Paddle subscription ${data.id}`);
}

/**
 * Handle subscription canceled webhook
 */
async function handleSubscriptionCanceled(
  eventData: PaddleWebhookData
) {
  const { data } = eventData;

  await db.subscription.updateMany({
    where: { paddleSubscriptionId: data.id },
    data: {
      status: 'canceled',
      statusFormatted: 'Canceled',
      endsAt: data.canceled_at || new Date().toISOString(),
      updatedAt: new Date(),
    },
  });

  console.log(`Canceled Paddle subscription ${data.id}`);
}

/**
 * Handle subscription paused webhook
 */
async function handleSubscriptionPaused(
  eventData: PaddleWebhookData
) {
  const { data } = eventData;

  await db.subscription.updateMany({
    where: { paddleSubscriptionId: data.id },
    data: {
      status: 'paused',
      statusFormatted: 'Paused',
      isPaused: true,
      updatedAt: new Date(),
    },
  });

  console.log(`Paused Paddle subscription ${data.id}`);
}

/**
 * Handle subscription resumed webhook
 */
async function handleSubscriptionResumed(
  eventData: PaddleWebhookData
) {
  const { data } = eventData;

  await db.subscription.updateMany({
    where: { paddleSubscriptionId: data.id },
    data: {
      status: 'active',
      statusFormatted: 'Active',
      isPaused: false,
      renewsAt: data.next_billed_at || null,
      updatedAt: new Date(),
    },
  });

  console.log(`Resumed Paddle subscription ${data.id}`);
}

/**
 * Handle transaction completed webhook
 */
async function handleTransactionCompleted(
  eventData: PaddleWebhookData
) {
  const { data } = eventData;

  // Update subscription with transaction details if it's a subscription payment
  if (data.custom_data?.subscription_id) {
    await db.subscription.updateMany({
      where: {
        paddleSubscriptionId: data.custom_data.subscription_id,
      },
      data: {
        paddleTransactionId: data.id,
        updatedAt: new Date(),
      },
    });
  }

  console.log(`Processed transaction ${data.id}`);
}

/**
 * Get user's active Paddle subscription
 */
export async function getUserPaddleSubscription() {
  try {
    const { userId } = await auth();
    if (!userId) {
      return null;
    }

    const subscription = await db.subscription.findFirst({
      where: {
        user: { clerkId: userId },
        provider: 'paddle',
        status: { in: ['active', 'trialing'] },
      },
      include: {
        plan: true,
      },
    });

    return subscription;
  } catch (error) {
    console.error('Error getting user Paddle subscription:', error);
    return null;
  }
}
