'use server';

import { auth } from '@clerk/nextjs/server';
import { put } from '@vercel/blob';
import { nanoid } from 'nanoid';
import { parseInvoiceData } from '@/lib/invoice-parser';
import type { InvoiceData } from '@/types/invoice';
import db from '@/db/db';

/**
 * Upload a file to Vercel Blob storage
 */
async function uploadFileToBlob(file: File): Promise<string | null> {
  try {
    // Create a unique filename with the original extension
    const fileExt = file.name.split('.').pop() || '';
    const uniqueFilename = `invoices/${nanoid()}.${fileExt}`;

    // Upload to Vercel Blob
    const { url } = await put(uniqueFilename, file, {
      access: 'public',
      addRandomSuffix: false,
    });

    return url;
  } catch (error) {
    console.error('Error uploading to Vercel Blob:', error);
    return null;
  }
}

/**
 * Process a single invoice file with enhanced features:
 * - Fraud detection
 * - Multi-document correlation
 * - Payment prediction
 * - Currency conversion
 * - Vendor profile learning
 */
async function processSingleInvoice(
  file: File,
  text: string,
  emailMetadata: {
    from?: string;
    subject?: string;
    date?: string;
    messageId?: string;
  } | null = null,
  userId?: string
): Promise<{ data?: InvoiceData; error?: string }> {
  try {
    // Performance optimization: Start file upload and text extraction in parallel
    const [fileUrl] = await Promise.all([uploadFileToBlob(file)]);

    if (!fileUrl) {
      return { error: 'Failed to upload file to storage' };
    }

    // Generate a thumbnail URL for preview (same as original for now)
    const thumbnailUrl = fileUrl;

    // Extract text from the document first
    const { extractTextFromDocument } = await import(
      '@/lib/document-ai'
    );
    const extractedText = await extractTextFromDocument(file);

    // Parse the text into structured invoice data
    const invoiceData = await parseInvoiceData(extractedText);

    // Add file URLs and source metadata to the extracted data
    let enhancedData: InvoiceData = {
      ...invoiceData,
      fileUrl,
      thumbnailUrl,
      source: emailMetadata
        ? {
            type: 'email',
            from: emailMetadata.from || 'Unknown',
            subject: emailMetadata.subject || 'No Subject',
            date: emailMetadata.date || new Date().toISOString(),
            messageId: emailMetadata.messageId,
          }
        : undefined,
    };

    // Enhanced processing has been moved to background jobs for better performance
    // The basic invoice data is returned immediately for fast user experience

    return { data: enhancedData };
  } catch (error) {
    console.error('Error processing single invoice:', error);

    if (error instanceof Error) {
      return { error: error.message };
    }

    return { error: 'An unknown error occurred during extraction' };
  }
}

/**
 * Extract data from one or multiple invoices using AI with parallel processing
 * Enhanced with fraud detection, multi-document correlation, and more
 */
export async function extractInvoiceData(formData: FormData) {
  const { userId } = await auth();

  if (!userId) {
    return {
      error: 'Authentication required',
    };
  }

  // Check invoice usage limits before processing
  const {
    checkCurrentUserInvoiceUsage,
    incrementCurrentUserInvoiceUsage,
  } = await import('@/lib/services/usage-service');

  const usageCheck = await checkCurrentUserInvoiceUsage();

  if (!usageCheck.allowed) {
    return {
      error: usageCheck.message || 'Invoice upload limit exceeded',
    };
  }

  try {
    // Check if we're processing a single file or multiple files
    const file = formData.get('file') as File;
    const files = formData.getAll('files') as File[];
    const text = formData.get('text') as string;

    // Check for email metadata if available
    let emailMetadata = null;
    const emailMetadataParam = formData.get('emailMetadata');
    if (
      emailMetadataParam &&
      typeof emailMetadataParam === 'string'
    ) {
      try {
        emailMetadata = JSON.parse(emailMetadataParam);
      } catch (err) {
        console.error('Error parsing email metadata:', err);
      }
    }

    // Single file processing
    if (file && !files.length) {
      if (!file) {
        return { error: 'No file provided' };
      }

      // Process invoice with basic extraction only (enhanced processing moved to background)
      const result = await processSingleInvoice(
        file,
        text,
        emailMetadata,
        userId
      );

      // If successful, save to database and trigger background enhancement
      if (result.data && !result.error) {
        // Import saveInvoiceToDatabase function
        const { saveInvoiceToDatabase } = await import(
          './save-invoice-to-db'
        );

        // Save to database
        const saveResult = await saveInvoiceToDatabase(result.data);

        if (saveResult.success && saveResult.data) {
          // Increment usage counter
          await incrementCurrentUserInvoiceUsage();

          // Trigger background enhancement job
          try {
            const response = await fetch(
              `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/jobs/invoice-enhancement`,
              {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  invoiceId: saveResult.data.id,
                  enhancementTypes: ['all'],
                }),
              }
            );

            if (!response.ok) {
              console.warn(
                'Failed to trigger background enhancement job:',
                await response.text()
              );
            } else {
              const jobResult = await response.json();
              console.log(
                'Background enhancement job started:',
                jobResult.jobId
              );
            }
          } catch (error) {
            console.warn(
              'Error triggering background enhancement job:',
              error
            );
            // Don't fail the main upload if background job fails
          }

          // Return the result with database ID
          return {
            data: {
              ...result.data,
              id: saveResult.data.id,
            },
          };
        } else {
          return {
            error:
              saveResult.error ||
              'Failed to save invoice to database',
          };
        }
      }

      return result;
    }

    // Multiple file processing with parallel execution
    else if (files.length) {
      // Process all files in parallel (basic extraction only)
      const processingPromises = files.map((file) =>
        processSingleInvoice(file, text, emailMetadata, userId)
      );

      // Wait for all processing to complete
      const results = await Promise.all(processingPromises);

      // Collect successful extractions and errors
      const extractedData: InvoiceData[] = [];
      const errors: string[] = [];
      const savedInvoices: any[] = [];

      // Import saveInvoiceToDatabase function
      const { saveInvoiceToDatabase } = await import(
        './save-invoice-to-db'
      );

      // Process results sequentially to avoid database conflicts
      for (const result of results) {
        if (result.data) {
          // Save to database
          const saveResult = await saveInvoiceToDatabase(result.data);

          if (saveResult.success && saveResult.data) {
            extractedData.push({
              ...result.data,
              id: saveResult.data.id,
            });
            savedInvoices.push(saveResult.data);

            // Increment usage counter
            await incrementCurrentUserInvoiceUsage();

            // Trigger background enhancement job
            try {
              const response = await fetch(
                `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/jobs/invoice-enhancement`,
                {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                  body: JSON.stringify({
                    invoiceId: saveResult.data.id,
                    enhancementTypes: ['all'],
                  }),
                }
              );

              if (!response.ok) {
                console.warn(
                  'Failed to trigger background enhancement job for invoice:',
                  saveResult.data.id
                );
              }
            } catch (error) {
              console.warn(
                'Error triggering background enhancement job:',
                error
              );
              // Don't fail the main upload if background job fails
            }
          } else {
            errors.push(
              saveResult.error || 'Failed to save invoice to database'
            );
          }
        }
        if (result.error) {
          errors.push(result.error);
        }
      }

      // Return the combined results
      return {
        data: extractedData,
        errors: errors.length ? errors : undefined,
        count: {
          total: files.length,
          successful: extractedData.length,
          failed: errors.length,
        },
      };
    }

    // No files provided
    else {
      return { error: 'No files provided' };
    }
  } catch (error) {
    console.error('Error extracting invoice data:', error);

    // Handle API key errors specifically
    if (error instanceof Error) {
      if (error.message.includes('API key')) {
        return {
          error:
            'API key error: Please check your API key configuration',
        };
      }
      return {
        error: error.message,
      };
    }

    return {
      error: 'An unknown error occurred during extraction',
    };
  }
}
