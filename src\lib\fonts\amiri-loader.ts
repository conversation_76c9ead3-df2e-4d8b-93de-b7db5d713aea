import { arrayBufferToBase64 } from "../utils/pdf"
import { jsPDF } from "jspdf"

// Loads Amiri font from public folder and adds to jsPDF
export async function loadAmiriFont(doc: jsPDF): Promise<boolean> {
  try {
    let regBase64: string, boldBase64: string

    if (typeof window === "undefined") {
      // SERVER: Use fs to read font files
      const fs = await import("fs/promises")
      const path = await import("path")
      const regularPath = path.resolve(process.cwd(), "public/fonts/Amiri-1.001/Amiri-Regular.ttf")
      const boldPath = path.resolve(process.cwd(), "public/fonts/Amiri-1.001/Amiri-Bold.ttf")
      regBase64 = Buffer.from(await fs.readFile(regularPath)).toString("base64")
      boldBase64 = Buffer.from(await fs.readFile(boldPath)).toString("base64")
    } else {
      // CLIENT: Use fetch and window.btoa
      const regularUrl = "/fonts/Amiri-1.001/Amiri-Regular.ttf"
      const boldUrl = "/fonts/Amiri-1.001/Amiri-Bold.ttf"
      const regRes = await fetch(regularUrl)
      if (!regRes.ok) throw new Error("Failed to load Amiri-Regular.ttf")
      regBase64 = arrayBufferToBase64(await regRes.arrayBuffer())
      const boldRes = await fetch(boldUrl)
      if (!boldRes.ok) throw new Error("Failed to load Amiri-Bold.ttf")
      boldBase64 = arrayBufferToBase64(await boldRes.arrayBuffer())
    }

    doc.addFileToVFS("Amiri-Regular.ttf", regBase64)
    doc.addFont("Amiri-Regular.ttf", "Amiri", "normal")
    doc.addFileToVFS("Amiri-Bold.ttf", boldBase64)
    doc.addFont("Amiri-Bold.ttf", "Amiri", "bold")
    return true
  } catch (e) {
    console.error("Amiri font load failed", e)
    return false
  }
} 