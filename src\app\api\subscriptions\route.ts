import { NextResponse } from "next/server";
import { currentUser } from "@clerk/nextjs/server";
import db from "@/db/db";

export async function GET() {
  try {
    const user = await currentUser();
    
    if (!user?.id) {
      return new NextResponse(JSON.stringify({ error: "Unauthorized" }), {
        status: 401,
        headers: {
          "Content-Type": "application/json",
        },
      });
    }
    
    // Find the user by clerkId
    const dbUser = await db.user.findUnique({
      where: { clerkId: user.id },
    });
    
    if (!dbUser) {
      return new NextResponse(JSON.stringify({ error: "User not found" }), {
        status: 404,
        headers: {
          "Content-Type": "application/json",
        },
      });
    }
    
    // Get user subscriptions
    const subscriptions = await db.subscription.findMany({
      where: { userId: dbUser.id },
      include: { plan: true },
    });
    
    return new NextResponse(JSON.stringify(subscriptions), {
      status: 200,
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    console.error("Error fetching subscriptions:", error);
    return new NextResponse(JSON.stringify({ error: "Internal Server Error" }), {
      status: 500,
      headers: {
        "Content-Type": "application/json",
      },
    });
  }
} 