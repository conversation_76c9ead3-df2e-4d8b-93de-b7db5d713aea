import React, { useState } from 'react';
import { UserRole } from '@prisma/client';
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { resendInvite, cancelInvite } from '@/lib/actions/team';
import { toast } from 'sonner';
import { formatDate } from '@/lib/utils';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

// Define types for invites
interface Invite {
  id: string;
  email: string;
  role: UserRole;
  invitedBy: string;
  invitedOn: string;
  expiresAt: Date;
}

interface PendingInvitesListProps {
  invites: Invite[];
  onRefresh: () => void;
}

export default function PendingInvitesList({ invites, onRefresh }: PendingInvitesListProps) {
  const [selectedInvite, setSelectedInvite] = useState<Invite | null>(null);
  const [isCancelDialogOpen, setIsCancelDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Get role badge
  const getRoleBadge = (role: UserRole) => {
    switch (role) {
      case UserRole.ADMIN:
        return <Badge className="bg-blue-600">Admin</Badge>;
      case UserRole.EDITOR:
        return (
          <Badge
            variant="outline"
            className="text-green-600 border-green-200 bg-green-50"
          >
            Editor
          </Badge>
        );
      case UserRole.VIEWER:
        return (
          <Badge
            variant="outline"
            className="text-amber-600 border-amber-200 bg-amber-50"
          >
            Viewer
          </Badge>
        );
      default:
        return <Badge variant="outline">{role}</Badge>;
    }
  };

  // Handle resend invite
  const handleResendInvite = async (inviteId: string) => {
    setIsSubmitting(true);
    try {
      const result = await resendInvite(inviteId);
      if (result.error) {
        toast.error(result.error);
      } else {
        toast.success('Invitation resent successfully');
        onRefresh();
      }
    } catch (error) {
      console.error('Failed to resend invitation:', error);
      toast.error('Failed to resend invitation');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle cancel invite
  const handleCancelInvite = async () => {
    if (!selectedInvite) return;
    
    setIsSubmitting(true);
    try {
      const result = await cancelInvite(selectedInvite.id);
      if (result.error) {
        toast.error(result.error);
      } else {
        toast.success('Invitation cancelled');
        onRefresh();
      }
    } catch (error) {
      console.error('Failed to cancel invitation:', error);
      toast.error('Failed to cancel invitation');
    } finally {
      setIsSubmitting(false);
      setIsCancelDialogOpen(false);
    }
  };

  if (invites.length === 0) {
    return (
      <Card className="shadow-sm">
        <CardContent className="p-6 text-center text-muted-foreground">
          No pending invitations found.
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b">
              <th className="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                Email
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                Role
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                Invited By
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                Date
              </th>
              <th className="px-4 py-3 text-right text-xs font-medium text-muted-foreground uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="divide-y">
            {invites.map((invite) => (
              <tr key={invite.id} className="hover:bg-muted/50">
                <td className="px-4 py-3 whitespace-nowrap">
                  <div className="font-medium">{invite.email}</div>
                  <div className="text-xs text-muted-foreground">
                    Expires {formatDate(invite.expiresAt)}
                  </div>
                </td>
                <td className="px-4 py-3 whitespace-nowrap">
                  {getRoleBadge(invite.role)}
                </td>
                <td className="px-4 py-3 whitespace-nowrap text-sm">
                  {invite.invitedBy}
                </td>
                <td className="px-4 py-3 whitespace-nowrap text-sm text-muted-foreground">
                  {invite.invitedOn}
                </td>
                <td className="px-4 py-3 whitespace-nowrap text-right">
                  <Button
                    variant="outline"
                    size="sm"
                    className="mr-2"
                    onClick={() => handleResendInvite(invite.id)}
                    disabled={isSubmitting}
                  >
                    Resend
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-red-600"
                    onClick={() => {
                      setSelectedInvite(invite);
                      setIsCancelDialogOpen(true);
                    }}
                    disabled={isSubmitting}
                  >
                    Cancel
                  </Button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Cancel Invite Dialog */}
      <AlertDialog open={isCancelDialogOpen} onOpenChange={setIsCancelDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Cancel Invitation</AlertDialogTitle>
            <AlertDialogDescription>
              This will cancel the invitation sent to {selectedInvite?.email}.
              They will no longer be able to join your organization using this invite.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isSubmitting}>Keep Invite</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleCancelInvite}
              disabled={isSubmitting}
              className="bg-red-600 hover:bg-red-700"
            >
              {isSubmitting ? 'Cancelling...' : 'Cancel Invite'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
} 