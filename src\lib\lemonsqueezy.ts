import { lemonSqueezySetup } from "@lemonsqueezy/lemonsqueezy.js";

/**
 * Ensures that required environment variables are set and sets up the Lemon
 * Squeezy JS SDK. Throws an error if any environment variables are missing or
 * if there's an error setting up the SDK.
 */
export function configureLemonSqueezy() {
  // console.log("🍋 Setting up Lemon Squeezy integration...");
  
  const requiredVars = [
    "LEMONSQUEEZY_API_KEY",
    "LEMONSQUEEZY_STORE_ID",
    "LEMONSQUEEZY_WEBHOOK_SECRET",
  ];

  const missingVars = requiredVars.filter((varName) => !process.env[varName]);

  if (missingVars.length > 0) {
    // console.error(`❌ LEMON CONFIG ERROR: Missing required environment variables: ${missingVars.join(", ")}`);
    
    // Log environment variable status for debugging (without revealing secrets)
    const envStatus = {
      LEMONSQUEEZY_API_KEY: process.env.LEMONSQUEEZY_API_KEY ? 
        `✅ Set (${process.env.LEMONSQUEEZY_API_KEY.substring(0, 5)}...${process.env.LEMONSQUEEZY_API_KEY.slice(-5)})` : 
        "❌ Not set",
      LEMONSQUEEZY_STORE_ID: process.env.LEMONSQUEEZY_STORE_ID || "❌ Not set",
      LEMONSQUEEZY_WEBHOOK_SECRET: process.env.LEMONSQUEEZY_WEBHOOK_SECRET ? 
        "✅ Set" : 
        "❌ Not set",
      NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL || "❌ Not set",
      WEBHOOK_URL: process.env.WEBHOOK_URL || "❌ Not set"
    };
    
    // console.error("📊 Environment variables status:", envStatus);
    
    throw new Error(
      `Missing required LEMONSQUEEZY env variables: ${missingVars.join(", ")}. Please check your .env file.`
    );
  }

  // Validate the WEBHOOK_URL format
  if (process.env.WEBHOOK_URL) {
    try {
      const webhookUrl = new URL(process.env.WEBHOOK_URL);
      // console.log(`🔗 Webhook URL validated: ${webhookUrl.origin}`);
      
      // Warn if the webhook URL includes paths that might conflict with our code
      if (webhookUrl.pathname.includes('/api/webhook')) {
        // console.warn(`⚠️ WEBHOOK_URL should NOT include '/api/webhook' path - this will be added automatically`);
      }
    } catch {
      // console.error(`❌ Invalid WEBHOOK_URL format: ${process.env.WEBHOOK_URL}`);
      throw new Error(`Invalid WEBHOOK_URL format. Must be a valid URL like 'https://your-domain.com'`);
    }
  } else {
    // console.warn("⚠️ WEBHOOK_URL is not set. Webhook creation will fail.");
  }

  try {
    // console.log("🔑 Initializing LemonSqueezy SDK with API key...");
    
    lemonSqueezySetup({
      apiKey: process.env.LEMONSQUEEZY_API_KEY,
      onError: (error) => {
        // console.error("❌ LemonSqueezy SDK Error:", error);
        throw new Error(`Lemon Squeezy API error: ${error.message}`);
      },
    });
    
    // console.log("✅ LemonSqueezy SDK setup successful");
  } catch (error) {
    // console.error("❌ Fatal error during LemonSqueezy setup:", error);
    throw error;
  }
  
  // Log successful configuration with store ID
  // console.log(`🏪 Configured for Lemon Squeezy store ID: ${process.env.LEMONSQUEEZY_STORE_ID}`);
}
