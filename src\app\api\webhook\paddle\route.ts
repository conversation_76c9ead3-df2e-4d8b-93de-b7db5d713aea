import { NextRequest, NextResponse } from 'next/server';
import {
  verifyPaddleWebhook,
  processPaddleWebhook,
} from '@/actions/paddle-actions';

export async function POST(req: NextRequest) {
  console.log('📣 PADDLE WEBHOOK RECEIVED');

  try {
    // Get the raw request body and signature
    const rawBody = await req.text();
    const signature = req.headers.get('paddle-signature');

    console.log(
      '📋 Paddle signature:',
      signature ? '✅ Provided' : '❌ Missing'
    );

    // Log webhook request details without sensitive data
    let eventType = 'Unknown';
    let eventId = 'Unknown';
    try {
      const bodyPreview = JSON.parse(rawBody);
      eventType = bodyPreview.event_type || 'Unknown event';
      eventId = bodyPreview.event_id || 'Not provided';
      console.log('📩 Paddle event:', eventType);
      console.log('🆔 Event ID:', eventId);
    } catch (parseError) {
      console.error(
        '❌ Failed to parse webhook payload for logging:',
        parseError
      );
    }

    // SECURITY: Verify the signature if webhook secret is configured
    if (signature) {
      const isValid = verifyPaddleWebhook(rawBody, signature);
      if (!isValid) {
        console.error(
          '🔒 SECURITY WARNING: Paddle webhook signature verification failed'
        );
        return NextResponse.json(
          { success: false, message: 'Invalid webhook signature' },
          { status: 401 }
        );
      }
      console.log(
        '🔒 Paddle webhook signature verified successfully'
      );
    } else {
      console.warn(
        '⚠️ No signature verification performed - missing signature'
      );
    }

    // Parse the webhook payload
    let payload;
    try {
      payload = JSON.parse(rawBody);
    } catch (parseError) {
      console.error(
        '❌ Failed to parse webhook payload:',
        parseError
      );
      return NextResponse.json(
        { success: false, message: 'Invalid JSON payload' },
        { status: 400 }
      );
    }

    if (!payload.event_type) {
      console.error('❌ Invalid webhook payload: missing event_type');
      return NextResponse.json(
        {
          success: false,
          message: 'Invalid payload: missing event_type',
        },
        { status: 400 }
      );
    }

    console.log(
      `🔄 Processing Paddle webhook event: ${payload.event_type}`
    );

    // Process the webhook event
    try {
      const result = await processPaddleWebhook(payload);

      if (result.success) {
        console.log(
          `✅ Successfully processed Paddle webhook event: ${payload.event_type}`
        );
      } else {
        console.error(
          `❌ Error processing Paddle webhook event: ${result.error}`
        );
        // Don't return error status to prevent retries for application logic errors
      }
    } catch (processingError) {
      console.error(
        `❌ Error processing Paddle webhook event ${eventId}:`,
        processingError
      );
      // Don't return error status to prevent retries for application logic errors
    }

    // Always acknowledge receipt of the webhook with a 200 OK response
    return NextResponse.json(
      {
        success: true,
        message: 'Paddle webhook received and acknowledged',
        eventId: eventId,
        eventType: eventType,
      },
      { status: 200 }
    );
  } catch (error) {
    // Catch-all for any unexpected errors
    console.error(
      '❌ CRITICAL ERROR processing Paddle webhook:',
      error
    );

    // For catastrophic errors, return 500 to signal Paddle to retry
    return NextResponse.json(
      {
        success: false,
        message: 'Critical error processing webhook',
        error: String(error),
      },
      { status: 500 }
    );
  }
}

// Add OPTIONS method handler for CORS support
export async function OPTIONS() {
  return NextResponse.json(
    {},
    {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers':
          'Content-Type, paddle-signature',
        'Access-Control-Max-Age': '86400',
      },
    }
  );
}
