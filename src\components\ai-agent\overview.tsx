import { motion } from "motion/react";
import { Sparkles, Receipt, FileText, BarChart2, Calendar } from "lucide-react";
import Colors from "../theme/Colors";

export const Overview = () => {
  return (
    <motion.div
      key="overview"
      className="max-w-4xl mx-auto mt-4 md:mt-8 w-full"
      initial={{ opacity: 0, scale: 0.98 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.98 }}
      transition={{ delay: 0.3 }}
    >
      <div className="rounded-2xl p-5 flex flex-col gap-4 leading-relaxed text-center max-w-2xl mx-auto bg-gradient-to-b from-card/80 to-card/40 backdrop-blur-md border border-border/30 shadow-xl">
        <div className="flex flex-col items-center gap-3">
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ delay: 0.5, type: "spring", stiffness: 300, damping: 20 }}
            className="size-14 flex items-center rounded-full justify-center ring-1 shrink-0 ring-primary/30 text-white shadow-lg"
            style={{ background: `linear-gradient(135deg, ${Colors.gradients.blueToPurple[0]}, ${Colors.gradients.blueToPurple[1]})` }}
          >
            <Sparkles size={24} />
          </motion.div>
          <h2
            className="text-2xl font-bold bg-clip-text text-transparent"
            style={{ background: `linear-gradient(90deg, ${Colors.gradients.blueToPurple[0]}, ${Colors.gradients.blueToPurple[1]})`, WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent' }}
          >
            Billix AI Agent
          </h2>
        </div>

        <p className="text-lg">
          Welcome to your financial management assistant. I can help you with:
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-left">
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.6 }}
            className="flex items-start gap-3 p-3 rounded-xl bg-background/50 border border-border/40 shadow-sm"
          >
            <Receipt className="mt-1 text-primary" size={20} />
            <div>
              <h3 className="font-medium">Invoice Management</h3>
              <p className="text-sm text-muted-foreground">Create, edit, and organize your invoices</p>
            </div>
          </motion.div>

          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.7 }}
            className="flex items-start gap-3 p-3 rounded-xl bg-background/50 border border-border/40 shadow-sm"
          >
            <FileText className="mt-1 text-primary" size={20} />
            <div>
              <h3 className="font-medium">Report Generation</h3>
              <p className="text-sm text-muted-foreground">Create financial reports with real-time data</p>
            </div>
          </motion.div>

          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.8 }}
            className="flex items-start gap-3 p-3 rounded-xl bg-background/50 border border-border/40 shadow-sm"
          >
            <BarChart2 className="mt-1 text-primary" size={20} />
            <div>
              <h3 className="font-medium">Financial Analytics</h3>
              <p className="text-sm text-muted-foreground">Analyze trends and get AI-powered insights</p>
            </div>
          </motion.div>

          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.9 }}
            className="flex items-start gap-3 p-3 rounded-xl bg-background/50 border border-border/40 shadow-sm"
          >
            <Calendar className="mt-1 text-primary" size={20} />
            <div>
              <h3 className="font-medium">Scheduled Reports</h3>
              <p className="text-sm text-muted-foreground">Automate report generation and delivery</p>
            </div>
          </motion.div>
        </div>

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1.0 }}
          className="flex flex-col items-center gap-2"
        >
          <p className="text-sm text-muted-foreground">
            Type a message below to get started with your financial assistant.
          </p>
          <div className="flex flex-wrap justify-center gap-2 text-xs mt-1">
            <span
              className="px-3 py-1.5 bg-primary/10 text-primary rounded-full cursor-pointer hover:bg-primary/20 transition-colors border border-primary/30 shadow-sm"
              onClick={() => {
                const event = new CustomEvent('set-input-text', {
                  detail: { text: "Generate a cash flow report for this month" }
                });
                window.dispatchEvent(event);
              }}
            >
              Generate a cash flow report
            </span>
            <span
              className="px-3 py-1.5 bg-primary/10 text-primary rounded-full cursor-pointer hover:bg-primary/20 transition-colors border border-primary/30 shadow-sm"
              onClick={() => {
                const event = new CustomEvent('set-input-text', {
                  detail: { text: "Help me create a new invoice" }
                });
                window.dispatchEvent(event);
              }}
            >
              Create a new invoice
            </span>
            <span
              className="px-3 py-1.5 bg-primary/10 text-primary rounded-full cursor-pointer hover:bg-primary/20 transition-colors border border-primary/30 shadow-sm"
              onClick={() => {
                const event = new CustomEvent('set-input-text', {
                  detail: { text: "Show me my financial analytics" }
                });
                window.dispatchEvent(event);
              }}
            >
              Show financial analytics
            </span>
          </div>
        </motion.div>
      </div>
    </motion.div>
  );
};
