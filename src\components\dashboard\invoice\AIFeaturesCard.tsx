"use client";

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ReceiptText } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from "@/components/ui/card";

interface AIFeaturesCardProps {
  onConfigureAI: () => void;
}

const AIFeaturesCard = ({ onConfigureAI }: AIFeaturesCardProps) => {
  return (
    <Card className="h-full flex flex-col">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center">
            <Sparkles className="h-4 w-4 mr-2 text-amber-500" />
            AI Processing
          </CardTitle>
          <Badge
            variant="outline"
            className="bg-amber-100 text-amber-800 dark:bg-amber-900/40 dark:text-amber-300"
          >
            AI-Powered
          </Badge>
        </div>
        <CardDescription>
          Smart invoice processing with advanced AI features
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4 flex-1">
        <div className="space-y-4">
          <AIFeature
            icon={
              <ReceiptText className="h-4 w-4 text-blue-600 dark:text-blue-400" />
            }
            title="Intelligent Data Extraction"
            description="Our AI extracts all invoice data with detailed line items and automatically validates and corrects information."
            bgColor="bg-blue-100 dark:bg-blue-900/50"
            gradientColor="from-blue-50 to-blue-50/30 dark:from-blue-950/50 dark:to-blue-950/20"
          />

          <AIFeature
            icon={
              <PieChart className="h-4 w-4 text-purple-600 dark:text-purple-400" />
            }
            title="Smart Categorization"
            description="AI suggests categories and vendor types with confidence scores based on invoice content. Suggestions improve over time as you select them."
            bgColor="bg-purple-100 dark:bg-purple-900/50"
            gradientColor="from-purple-50 to-purple-50/30 dark:from-purple-950/50 dark:to-purple-950/20"
            showProgress={true}
            confidenceValue={85}
          />

          <AIFeature
            icon={
              <BarChart className="h-4 w-4 text-green-600 dark:text-green-400" />
            }
            title="Automated Tax Calculation"
            description="Missing tax information is automatically calculated based on country-specific rates and verified against invoice totals."
            bgColor="bg-green-100 dark:bg-green-900/50"
            gradientColor="from-green-50 to-green-50/30 dark:from-green-950/50 dark:to-green-950/20"
          />

          <AIFeature
            icon={
              <BarChart className="h-4 w-4 text-amber-600 dark:text-amber-400" />
            }
            title="Intelligent Invoice Type Detection"
            description="Automatically detects if an invoice is for payment (expense) or from customers (income) based on invoice content and patterns."
            bgColor="bg-amber-100 dark:bg-amber-900/50"
            gradientColor="from-amber-50 to-amber-50/30 dark:from-amber-950/50 dark:to-amber-950/20"
          />
        </div>
      </CardContent>
      <CardFooter className="p-4 pt-0 mt-auto">
        <Button variant="primaryGradient" className="w-full" onClick={onConfigureAI}>
          <Sparkles className="mr-2 h-4 w-4" />
          Configure AI Settings
        </Button>
      </CardFooter>
    </Card>
  );
};

interface AIFeatureProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  bgColor: string;
  gradientColor: string;
  showProgress?: boolean;
  confidenceValue?: number;
}

const AIFeature = ({
  icon,
  title,
  description,
  bgColor,
  gradientColor,
  showProgress = false,
  confidenceValue,
}: AIFeatureProps) => {
  return (
    <div
      className={`border rounded-lg p-4 bg-gradient-to-r ${gradientColor} hover:shadow-sm transition-shadow`}
    >
      <div className="flex items-start gap-3">
        <div
          className={`h-8 w-8 rounded-full ${bgColor} flex items-center justify-center flex-shrink-0`}
        >
          {icon}
        </div>
        <div className="flex-1">
          <h3 className="font-medium">{title}</h3>
          <p className="text-sm text-muted-foreground mt-1">{description}</p>

          {showProgress && confidenceValue && (
            <div className="mt-3">
              <div className="flex justify-between items-center text-xs text-muted-foreground mb-1">
                <span>Confidence</span>
                <span className="font-medium">{confidenceValue}%</span>
              </div>
              <div className="h-1.5 w-full overflow-hidden rounded-full bg-primary/20">
                <div
                  className={`h-full transition-all ${
                    confidenceValue > 80
                      ? "bg-[#00BBFF]"
                      : confidenceValue > 60
                      ? "bg-[#01ADE6]"
                      : "bg-[#0C8597]"
                  }`}
                  style={{ width: `${confidenceValue}%` }}
                />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AIFeaturesCard;
