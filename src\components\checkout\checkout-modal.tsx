'use client';

import { CheckoutHeader } from '@/components/checkout/checkout-header';
import { CheckoutContents } from '@/components/checkout/checkout-contents';
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog';
import { toast } from 'sonner';

interface CheckoutModalProps {
  isOpen: boolean;
  onClose: () => void;
  priceId: string;
  userEmail?: string;
  returnUrl?: string;
}

export function CheckoutModal({ 
  isOpen, 
  onClose, 
  priceId, 
  userEmail,
  returnUrl 
}: CheckoutModalProps) {
  const handleCheckoutComplete = (data: any) => {
    console.log('Checkout completed:', data);
    toast.success('Payment successful! Redirecting...');
    
    // Redirect to success page
    const successUrl = returnUrl 
      ? `/dashboard/subscription?success=true&session_id=${data.id}&return_url=${encodeURIComponent(returnUrl)}`
      : `/dashboard/subscription?success=true&session_id=${data.id}`;
    
    setTimeout(() => {
      window.location.href = successUrl;
    }, 1500);
  };

  const handleCheckoutError = (error: string) => {
    console.error('Checkout error:', error);
    toast.error(error || 'Payment failed. Please try again.');
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl w-full h-[90vh] max-h-[800px] p-0 bg-black border-neutral-800 overflow-hidden sm:rounded-lg">
        <VisuallyHidden>
          <DialogTitle>Checkout</DialogTitle>
        </VisuallyHidden>
        <div className="flex flex-col h-full">
          <CheckoutHeader onClose={onClose} />
          
          <div className="flex-1 overflow-auto">
            <CheckoutContents
              priceId={priceId}
              userEmail={userEmail}
              onCheckoutComplete={handleCheckoutComplete}
              onCheckoutError={handleCheckoutError}
            />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
