"use client";

import React, { useState } from "react";
import { use<PERSON><PERSON>er } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, SubmitHandler, Control, Resolver } from "react-hook-form";
import { z } from "zod";
import { Report } from "@prisma/client";
import { scheduleReport, exportReport, updateSchedule } from "@/lib/actions/reports";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { CalendarIcon } from "lucide-react";
import { format, addDays, addMonths, setDate } from "date-fns";
import { toast } from "sonner";

// Define the schema ensuring sendImmediately is always a boolean
const formSchema = z.object({
  frequency: z.enum(["daily", "weekly", "monthly"], {
    required_error: "Please select a frequency.",
  }),
  nextRunDate: z.date({
    required_error: "Please select a date.",
  }),
  emailRecipients: z.string().min(1, {
    message: "Please enter at least one email address.",
  }),
  sendImmediately: z.boolean().default(false),
});

// Define the exact shape of the form values
type FormValues = z.infer<typeof formSchema>;

// Type for the scheduledReport prop, accommodating various fields it might have
interface FormScheduledReport {
  id: string; // Essential for editing mode
  frequency?: "daily" | "weekly" | "monthly" | string; // Explicitly define, was on PrismaScheduledReport
  nextRunTime?: Date | string; // Explicitly define, was on PrismaScheduledReport (Date)
  nextRunDate?: Date | string; // Custom field, can be Date object or string representation
  emailAddresses?: string; // Explicitly define, was on PrismaScheduledReport
  emailRecipients?: string[]; // Processed array of emails
  // Add other fields from PrismaScheduledReport if they are directly accessed on scheduledReport
}

interface ScheduleReportFormProps {
  report: Report & {
    name?: string; // For report.name usage
    emailAddresses?: string; // For report.emailAddresses usage
    nextRunTime?: Date | string; // For report.nextRunTime usage
  };
  initialFrequency?: string;
  scheduledReport?: FormScheduledReport;
  isEditing?: boolean;
}

export function ScheduleReportForm({
  report,
  initialFrequency,
  scheduledReport,
  isEditing = false
}: ScheduleReportFormProps) {
  // Extract email addresses from the scheduled report if available
  const getInitialEmailRecipients = () => {
    if (scheduledReport) {
      if (scheduledReport.emailRecipients && scheduledReport.emailRecipients.length > 0) {
        return scheduledReport.emailRecipients.join(',');
      }
      if (scheduledReport.emailAddresses) {
        return scheduledReport.emailAddresses;
      }
    }
    if (report.emailAddresses) {
      return report.emailAddresses;
    }
    return "";
  };
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Calculate default next run date based on frequency
  const getNextRunDate = (frequency: string) => {
    const now = new Date();

    switch (frequency) {
      case "daily":
        return addDays(now, 1);
      case "weekly":
        return addDays(now, 7);
      case "monthly": {
        // Set to 1st day of next month
        const nextMonth = addMonths(now, 1);
        return setDate(nextMonth, 1);
      }
      default:
        return now;
    }
  };

  // Get the next run time from the scheduled report if available
  const getInitialNextRunDate = () => {
    if (scheduledReport) {
      if (scheduledReport.nextRunTime) {
        return new Date(scheduledReport.nextRunTime);
      }
      if (scheduledReport.nextRunDate) {
        return new Date(scheduledReport.nextRunDate);
      }
    }
    if (report.nextRunTime) {
      return new Date(report.nextRunTime);
    }
    return getNextRunDate(defaultFrequency);
  };

  // Get the frequency from the scheduled report if available
  const getInitialFrequency = () => {
    if (scheduledReport && scheduledReport.frequency &&
        ["daily", "weekly", "monthly"].includes(scheduledReport.frequency)) {
      return scheduledReport.frequency as "daily" | "weekly" | "monthly";
    }
    if (initialFrequency && ["daily", "weekly", "monthly"].includes(initialFrequency)) {
      return initialFrequency as "daily" | "weekly" | "monthly";
    }
    return "weekly";
  };

  const defaultFrequency = getInitialFrequency();

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema) as Resolver<FormValues>,
    defaultValues: {
      frequency: defaultFrequency,
      nextRunDate: getInitialNextRunDate(),
      emailRecipients: getInitialEmailRecipients(),
      sendImmediately: isEditing ? false : defaultFrequency === "daily",
    },
  });

  // Update next run date when frequency changes
  React.useEffect(() => {
    const frequency = form.watch("frequency");
    form.setValue("nextRunDate", getNextRunDate(frequency));

    // Set sendImmediately to true for daily frequency
    if (frequency === "daily") {
      form.setValue("sendImmediately", true);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [form.watch("frequency"), form]);

  // Send email using Resend API
  const sendReportEmail = async (recipients: string[], reportName: string, pdfUrl: string, excelUrl: string) => {
    try {
      // Get the current authenticated user for proper personalization
      const currentUser = await fetch('/api/user/profile').then(res => res.json());
      const userFullName = currentUser?.name || currentUser?.fullName || "there";

      // Direct server action to send email via API route
      const response = await fetch('/api/email/send-report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          recipients,
          reportName,
          pdfUrl,
          excelUrl,
          userName: userFullName, // Use the user's actual name
          customSubject: `Your Report: ${reportName} is Ready`,
          customMessage: "Here is your requested report. You can download the PDF or Excel versions using the buttons below."
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => null);
        throw new Error(errorData?.error || 'Failed to send email');
      }

      return await response.json();
    } catch (error) {
      throw error;
    }
  };

  const onSubmit: SubmitHandler<FormValues> = async (data) => {
    setIsSubmitting(true);
    try {
      const emailRecipients = data.emailRecipients
        .split(",")
        .map((email) => email.trim())
        .filter((email) => email);

      if (emailRecipients.length === 0) {
        toast.error("Please provide at least one valid email recipient");
        setIsSubmitting(false);
        return;
      }

      if (isEditing && scheduledReport) {
        // Update the existing scheduled report
        await updateSchedule({
          id: scheduledReport.id,
          frequency: data.frequency,
          nextRunDate: data.nextRunDate,
          emailRecipients,
          isActive: true, // Ensure it's active when updated
        });

        toast.success("Schedule updated successfully");
      } else {
        // Create a new scheduled report
        await scheduleReport({
          reportId: report.id,
          frequency: data.frequency,
          nextRunDate: data.nextRunDate,
          emailRecipients,
          organizationId: report.organizationId || undefined,
        });

        // For daily reports or if sendImmediately is checked, send the report right away
        if (data.frequency === "daily" || data.sendImmediately) {
          // Use a specific toast ID so we can dismiss it later
          const loadingToastId = toast.loading("Generating and sending initial report...");

          try {
            // Export the report to get URLs
            const pdfResult = await exportReport(report.id, "pdf");

            if (!pdfResult || !pdfResult.fileUrl) {
              throw new Error("Failed to generate PDF report");
            }

            const excelResult = await exportReport(report.id, "excel");

            if (!excelResult || !excelResult.fileUrl) {
              throw new Error("Failed to generate Excel report");
            }

            // Send email with the exported report links
            await sendReportEmail(
              emailRecipients,
              report.title || report.name || "Untitled Report",
              pdfResult.fileUrl,
              excelResult.fileUrl
            );

            // Dismiss the loading toast and show success
            toast.dismiss(loadingToastId);
            toast.success("Initial report sent to recipients");
          } catch (exportError) {
            // Dismiss the loading toast and show error
            toast.dismiss(loadingToastId);
            toast.error(`Failed to send initial report: ${exportError instanceof Error ? exportError.message : 'Unknown error'}`);
            // Continue to success message for scheduling even if immediate send fails
          }
        }

        toast.success("Report scheduled successfully");
      }

      // Wait a moment before redirecting to ensure toasts are visible
      setTimeout(() => {
        router.push("/dashboard/reports");
        router.refresh();
      }, 1500);
    } catch (error) {
      toast.error(`Failed to ${isEditing ? 'update' : 'schedule'} report: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setIsSubmitting(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          {isEditing ? "Edit Schedule" : "Schedule Report"}: {report.title || report.name || "Untitled Report"}
        </CardTitle>
        <CardDescription>
          {isEditing
            ? "Update the schedule for automatic generation and delivery of this report."
            : "Set up a schedule to automatically generate and email this report."}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit as SubmitHandler<FormValues>)} className="space-y-6">
            <FormField
              control={form.control as unknown as Control<FormValues>}
              name="frequency"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Frequency</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select frequency" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="daily">Daily</SelectItem>
                      <SelectItem value="weekly">Weekly</SelectItem>
                      <SelectItem value="monthly">Monthly (1st of month)</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    {field.value === "daily"
                      ? "Report will be sent daily at the same time, with first report sent immediately."
                      : field.value === "monthly"
                      ? "Report will be sent on the 1st day of each month."
                      : "How often this report should be generated and sent."}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control as unknown as Control<FormValues>}
              name="nextRunDate"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Next Run Date</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant={"outline"}
                          className={`w-full pl-3 text-left font-normal ${
                            !field.value ? "text-muted-foreground" : ""
                          }`}
                        >
                          {field.value ? (
                            format(field.value, "PPP")
                          ) : (
                            <span>Pick a date</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={field.onChange}
                        disabled={(date) => date < new Date()}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <FormDescription>
                    When the next report should be generated.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control as unknown as Control<FormValues>}
              name="emailRecipients"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email Recipients</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="<EMAIL>, <EMAIL>"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Enter email addresses separated by commas.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {form.watch("frequency") !== "daily" && !isEditing && (
              <FormField
                control={form.control as unknown as Control<FormValues>}
                name="sendImmediately"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <input
                        type="checkbox"
                        className="h-4 w-4 mt-1"
                        checked={field.value}
                        onChange={(e) => field.onChange(e.target.checked)}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Send immediately</FormLabel>
                      <FormDescription>
                        Also send the report now, in addition to scheduling future delivery.
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />
            )}

            <div className="flex justify-end space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.back()}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting
                  ? (isEditing ? "Updating..." : "Scheduling...")
                  : (isEditing ? "Update Schedule" : "Schedule Report")}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}