import db from '@/db/db';

async function cleanupPlaceholderPlans() {
  console.log('🧹 Cleaning up placeholder plans...');

  try {
    // Find placeholder plans
    const placeholderPlans = await db.plan.findMany({
      where: {
        OR: [
          { name: { contains: 'Paddle Plan undefined' } },
          { name: { contains: 'Paddle Plan pri_' } },
          { price: '0' },
          { paddlePriceId: null },
        ],
      },
    });

    console.log(`Found ${placeholderPlans.length} placeholder plans`);

    for (const plan of placeholderPlans) {
      // Check if any subscriptions are using this plan
      const subscriptionsCount = await db.subscription.count({
        where: { planId: plan.id },
      });

      if (subscriptionsCount > 0) {
        console.log(
          `⚠️ Plan "${plan.name}" has ${subscriptionsCount} subscriptions, updating subscription to use correct plan...`
        );

        // Try to find a matching real plan based on the subscription's Paddle price ID
        const subscriptions = await db.subscription.findMany({
          where: { planId: plan.id },
        });

        for (const subscription of subscriptions) {
          if (subscription.paddlePriceId) {
            const correctPlan = await db.plan.findFirst({
              where: { paddlePriceId: subscription.paddlePriceId },
            });

            if (correctPlan) {
              await db.subscription.update({
                where: { id: subscription.id },
                data: { planId: correctPlan.id },
              });
              console.log(
                `✅ Updated subscription ${subscription.id} to use correct plan: ${correctPlan.name}`
              );
            }
          }
        }

        // Check again if any subscriptions are still using this plan
        const remainingSubscriptions = await db.subscription.count({
          where: { planId: plan.id },
        });

        if (remainingSubscriptions === 0) {
          await db.plan.delete({
            where: { id: plan.id },
          });
          console.log(`🗑️ Deleted placeholder plan: ${plan.name}`);
        } else {
          console.log(
            `⚠️ Still ${remainingSubscriptions} subscriptions using plan "${plan.name}", skipping deletion`
          );
        }
      } else {
        await db.plan.delete({
          where: { id: plan.id },
        });
        console.log(`🗑️ Deleted placeholder plan: ${plan.name}`);
      }
    }

    console.log('🎉 Cleanup completed!');

    // Show final state
    const finalPlans = await db.plan.findMany({
      orderBy: { sort: 'asc' },
    });

    console.log('\n📊 Final plans in database:');
    finalPlans.forEach((plan) => {
      console.log(
        `  - ${plan.name}: $${(parseInt(plan.price) / 100).toFixed(2)}/${plan.interval} (${plan.paddlePriceId || plan.variantId})`
      );
    });

    return { success: true, cleaned: placeholderPlans.length };
  } catch (error) {
    console.error('❌ Error cleaning up plans:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

// Function to run the cleanup if this file is executed directly
if (require.main === module) {
  cleanupPlaceholderPlans()
    .then((result) => {
      console.log('Cleanup result:', result);
      process.exit(0);
    })
    .catch((error) => {
      console.error('Cleanup failed:', error);
      process.exit(1);
    });
}
