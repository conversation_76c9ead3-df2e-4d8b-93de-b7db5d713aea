import { currentUser } from '@clerk/nextjs/server';
import db from '@/db/db';

/**
 * Gets the database user ID for the currently authenticated Clerk user
 * Creates a user record in the database if it doesn't exist
 */
export async function getCurrentUserId(): Promise<string | null> {
  try {
    // Get the current user directly from Clerk
    const user = await currentUser();

    if (!user || !user.emailAddresses || user.emailAddresses.length === 0) {
      return null;
    }

    const email = user.emailAddresses[0].emailAddress;

    // Try to find the user in the database by clerkId first
    let dbUser = await db.user.findUnique({
      where: { clerkId: user.id }
    });

    if (dbUser) {
      return dbUser.id;
    }

    // If not found by clerkId, try to find by email
    dbUser = await db.user.findUnique({
      where: { email }
    });

    if (dbUser) {
      // Update the existing user with the clerkId
      dbUser = await db.user.update({
        where: { id: dbUser.id },
        data: {
          clerkId: user.id,
          firstName: user.firstName || dbUser.firstName,
          lastName: user.lastName || dbUser.lastName,
          profileImageUrl: user.imageUrl || dbUser.profileImageUrl,
          lastActive: new Date(),
          status: "ACTIVE",
        }
      });
      return dbUser.id;
    }

    // If user doesn't exist in the database, create it
    dbUser = await db.user.create({
      data: {
        email,
        clerkId: user.id,
        firstName: user.firstName || "",
        lastName: user.lastName || "",
        profileImageUrl: user.imageUrl || "",
        role: "USER",
        lastActive: new Date(),
      }
    });

    return dbUser.id;
  } catch (error) {
    console.error('Error getting current user ID:', error);
    
    // If it's a Prisma unique constraint error, provide more context
    if (error instanceof Error && 'code' in error && error.code === 'P2002') {
      const prismaError = error as any;
      const fields = prismaError.meta?.target || ['unknown'];
      console.error(`User with ${fields.join(', ')} already exists in database`);
    }
    
    return null;
  }
}

/**
 * Checks if the current user is authorized to access a resource
 * @param resourceUserId The user ID associated with the resource
 */
export async function isAuthorized(resourceUserId: string): Promise<boolean> {
  const currentUserId = await getCurrentUserId();
  return currentUserId === resourceUserId;
}
