'use client';

import { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';

interface User {
  id: string;
  clerkId: string;
  email: string;
  firstName: string | null;
  lastName: string | null;
  profileImageUrl: string;
  createdAt: Date;
  updatedAt: Date;
  role: 'USER';
}

const AdvancedTab = ({ user }: { user: User | null | undefined }) => {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const handleExportData = () => {
    setIsLoading(true);

    // Simulate API call for data export
    setTimeout(() => {
      setIsLoading(false);
      toast({
        title: 'Data export initiated',
        description: 'Your data will be emailed to you shortly.',
      });
    }, 1500);
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Advanced Settings</CardTitle>
          <CardDescription>
            Manage advanced application settings.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Data Preferences</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="data-export">Export All Data</Label>
                  <p className="text-sm text-muted-foreground">
                    Download a copy of all your account data and
                    invoices.
                  </p>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleExportData}
                  disabled={isLoading}
                >
                  Coming Soon
                </Button>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-sm font-medium">Developer Options</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>API Access</Label>
                  <p className="text-sm text-muted-foreground">
                    Manage your API keys and access.
                  </p>
                </div>
                <Button disabled variant="outline" size="sm">
                  Coming Soon
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AdvancedTab;
