import { NextRequest, NextResponse } from "next/server";

export async function GET(req: NextRequest) {
  // Get the full URL
  const url = new URL(req.url);
  
  // Check if there's a query parameter
  if (url.searchParams.has("query")) {
    // Redirect to the query endpoint with all the same parameters
    return NextResponse.redirect(new URL(`/api/invoices/query${url.search}`, url.origin));
  }
  
  // If no query parameter, return a not found response
  return NextResponse.json({ error: "Not found" }, { status: 404 });
} 