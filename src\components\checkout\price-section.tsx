import { CheckoutLineItems } from '@/components/checkout/checkout-line-items';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';

interface PriceSectionProps {
  checkoutData: any;
  quantity: number;
  onQuantityChange: (quantity: number) => void;
}

export function PriceSection({ 
  checkoutData, 
  quantity, 
  onQuantityChange 
}: PriceSectionProps) {
  return (
    <div className="bg-neutral-900 border border-neutral-800 rounded-lg">
      <Accordion type="single" defaultValue="order-summary" collapsible>
        <AccordionItem value="order-summary" className="border-none">
          <AccordionTrigger className="px-6 py-4 text-white hover:no-underline">
            <div className="flex items-center justify-between w-full">
              <span className="font-semibold">Order summary</span>
              {checkoutData?.totals && (
                <span className="text-lg font-semibold">
                  {new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: checkoutData.totals.currency_code || 'USD',
                  }).format((checkoutData.totals.total || 0) / 100)}
                </span>
              )}
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6">
            <CheckoutLineItems
              checkoutData={checkoutData}
              quantity={quantity}
              onQuantityChange={onQuantityChange}
            />
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
}
