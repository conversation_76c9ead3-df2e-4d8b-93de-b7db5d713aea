'use client';

import { useState, useEffect, useCallback } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import {
  RadioGroup,
  RadioGroupItem,
} from '@/components/ui/radio-group';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import {
  FolderPlus,
  Folder,
  FileText,
  FileSpreadsheet,
  Loader2,
  Check,
  ChevronRight,
  AlertCircle,
} from 'lucide-react';
import { toast } from 'sonner';
import {
  createExportFolder,
  getExportFolders,
  exportInvoices,
} from '@/actions/export-actions';
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from '@/components/ui/alert';

interface ExportFolder {
  id: string;
  name: string;
  createdAt: Date;
  invoiceCount: number;
}

interface ExportDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  invoices: { id: string; [key: string]: unknown }[];
}

export function ExportDialog({
  isOpen,
  onOpenChange,
  invoices,
}: ExportDialogProps) {
  const [activeTab, setActiveTab] = useState('folder');
  const [exportFormat, setExportFormat] = useState('pdf');
  const [selectedFolder, setSelectedFolder] = useState<string | null>(
    null
  );
  const [newFolderName, setNewFolderName] = useState('');
  const [isCreatingFolder, setIsCreatingFolder] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [folders, setFolders] = useState<ExportFolder[]>([]);
  const [isLoadingFolders, setIsLoadingFolders] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedFields, setSelectedFields] = useState({
    invoiceNumber: true,
    date: true,
    dueDate: true,
    vendor: true,
    amount: true,
    status: true,
    category: true,
    lineItems: true,
    customer: true,
    notes: false,
    termsAndConditions: false,
    metadata: false,
    dynamicData: true,
  });

  const fetchFolders = useCallback(async () => {
    try {
      setIsLoadingFolders(true);
      setError(null);
      const result = await getExportFolders();

      if (result.success) {
        if (result.folders) {
          setFolders(result.folders);

          // If we have folders and none is selected, select the first one
          if (result.folders.length > 0 && !selectedFolder) {
            setSelectedFolder(result.folders[0].id);
          }
        } else {
          setFolders([]);
        }
      } else {
        throw new Error(result.error || 'Failed to fetch folders');
      }
    } catch (error) {
      console.error('Error fetching folders:', error);
      setError('Failed to load export folders. Please try again.');
    } finally {
      setIsLoadingFolders(false);
    }
  }, []);

  // Fetch folders when dialog opens
  useEffect(() => {
    if (isOpen) {
      fetchFolders();
    }
  }, [isOpen, fetchFolders]);

  const handleCreateFolder = async () => {
    if (!newFolderName.trim()) {
      toast.error('Please enter a folder name.');
      return;
    }

    try {
      setIsCreatingFolder(true);
      setError(null);
      const result = await createExportFolder(newFolderName);

      if (result.success && result.folder) {
        toast.success(`Folder "${newFolderName}" has been created.`);

        // Add the new folder to the list and select it
        setFolders((prev) => [
          ...prev,
          result.folder as ExportFolder,
        ]);
        setSelectedFolder(result.folder.id);
        setNewFolderName('');
        setActiveTab('export'); // Switch to export tab
      } else {
        throw new Error(result.error || 'Failed to create folder');
      }
    } catch (error) {
      console.error('Error creating folder:', error);
      setError('Failed to create folder. Please try again.');
    } finally {
      setIsCreatingFolder(false);
    }
  };

  const handleExport = async () => {
    if (!selectedFolder) {
      toast.error('Please select a folder to export to.');
      return;
    }

    try {
      setIsExporting(true);
      setError(null);

      const invoiceIds = invoices.map((invoice) => invoice.id);

      const result = await exportInvoices({
        invoiceIds,
        folderId: selectedFolder,
        format: exportFormat,
        fields: Object.entries(selectedFields)
          .filter(([, selected]) => selected)
          .map(([field]) => field),
      });

      if (result.success) {
        toast.success(
          `${invoices.length} invoice(s) exported successfully.`
        );

        // If there's a download URL, trigger download
        if (result.downloadUrl) {
          try {
            const link = document.createElement('a');
            link.href = result.downloadUrl;
            link.setAttribute(
              'download',
              result.fileName || 'invoice-export'
            );
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          } catch (downloadError) {
            console.error('Error downloading file:', downloadError);
            toast.error(
              'The export was created but could not be downloaded automatically. Please try again.'
            );
          }
        }

        onOpenChange(false);
      } else {
        throw new Error(result.error || 'Export failed');
      }
    } catch (error) {
      console.error('Error exporting invoices:', error);
      let errorMessage =
        'Failed to export invoices. Please try again.';

      // Handle specific error cases
      if (
        error instanceof Error &&
        error.message.includes('Font is not stored')
      ) {
        errorMessage =
          'Failed to generate PDF with Arabic text. Please try exporting as Excel instead.';
      }

      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsExporting(false);
    }
  };

  const handleFieldChange = (field: string, checked: boolean) => {
    setSelectedFields((prev) => ({
      ...prev,
      [field]: checked,
    }));
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Export Invoices</DialogTitle>
          <DialogDescription>
            Export {invoices.length} invoice
            {invoices.length !== 1 ? 's' : ''} to a folder.
          </DialogDescription>
        </DialogHeader>

        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <Tabs
          defaultValue={activeTab}
          onValueChange={setActiveTab}
          className="w-full"
        >
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="folder">
              <Folder className="mr-2 h-4 w-4" />
              Select Folder
            </TabsTrigger>
            <TabsTrigger
              value="export"
              disabled={!selectedFolder && folders.length > 0}
            >
              <FileText className="mr-2 h-4 w-4" />
              Export Options
            </TabsTrigger>
          </TabsList>

          <TabsContent value="folder" className="mt-4 space-y-4">
            {/* Create New Folder */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-md">
                  Create New Folder
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-end gap-2">
                  <div className="flex-1 space-y-2">
                    <Label htmlFor="folderName">Folder Name</Label>
                    <Input
                      id="folderName"
                      placeholder="Enter folder name"
                      value={newFolderName}
                      onChange={(e) =>
                        setNewFolderName(e.target.value)
                      }
                    />
                  </div>
                  <Button
                    onClick={handleCreateFolder}
                    disabled={
                      isCreatingFolder || !newFolderName.trim()
                    }
                  >
                    {isCreatingFolder ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <FolderPlus className="h-4 w-4 mr-2" />
                    )}
                    Create
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Existing Folders */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-md">
                  Select Folder
                </CardTitle>
              </CardHeader>
              <CardContent>
                {isLoadingFolders ? (
                  <div className="flex justify-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                  </div>
                ) : folders.length === 0 ? (
                  <div className="text-center py-8">
                    <Folder className="h-12 w-12 mx-auto text-muted-foreground" />
                    <h3 className="mt-4 text-lg font-medium">
                      No folders yet
                    </h3>
                    <p className="mt-2 text-sm text-muted-foreground">
                      Create a folder to start organizing your
                      exports.
                    </p>
                  </div>
                ) : (
                  <ScrollArea className="h-[200px]">
                    <div className="space-y-2">
                      {folders.map((folder) => (
                        <div
                          key={folder.id}
                          className={`flex items-center justify-between p-3 rounded-md cursor-pointer transition-colors ${
                            selectedFolder === folder.id
                              ? 'bg-primary/10 border border-primary/20'
                              : 'hover:bg-muted'
                          }`}
                          onClick={() => setSelectedFolder(folder.id)}
                        >
                          <div className="flex items-center">
                            <Folder
                              className={`h-5 w-5 mr-3 ${selectedFolder === folder.id ? 'text-primary' : 'text-muted-foreground'}`}
                            />
                            <div>
                              <p className="font-medium">
                                {folder.name}
                              </p>
                              <p className="text-xs text-muted-foreground">
                                {folder.invoiceCount} invoice
                                {folder.invoiceCount !== 1
                                  ? 's'
                                  : ''}{' '}
                                • Created{' '}
                                {new Date(
                                  folder.createdAt
                                ).toLocaleDateString()}
                              </p>
                            </div>
                          </div>
                          {selectedFolder === folder.id && (
                            <Check className="h-5 w-5 text-primary" />
                          )}
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                )}

                {folders.length > 0 && (
                  <div className="mt-4 flex justify-end">
                    <Button
                      onClick={() => setActiveTab('export')}
                      disabled={!selectedFolder}
                    >
                      Continue
                      <ChevronRight className="ml-2 h-4 w-4" />
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="export" className="mt-4">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-md">
                  Export Options
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Selected Folder */}
                {selectedFolder && (
                  <div className="bg-muted/50 p-3 rounded-md flex items-center">
                    <Folder className="h-5 w-5 mr-3 text-primary" />
                    <div>
                      <p className="font-medium">
                        {folders.find((f) => f.id === selectedFolder)
                          ?.name || 'Selected Folder'}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {invoices.length} invoice
                        {invoices.length !== 1 ? 's' : ''} will be
                        exported to this folder
                      </p>
                    </div>
                  </div>
                )}

                {/* Export Format */}
                <div className="space-y-3">
                  <Label>Export Format</Label>
                  <RadioGroup
                    value={exportFormat}
                    onValueChange={setExportFormat}
                    className="flex space-x-4"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="pdf" id="pdf" />
                      <Label
                        htmlFor="pdf"
                        className="flex items-center cursor-pointer"
                      >
                        <FileText className="h-4 w-4 mr-2 text-red-500" />
                        PDF
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="excel" id="excel" />
                      <Label
                        htmlFor="excel"
                        className="flex items-center cursor-pointer"
                      >
                        <FileSpreadsheet className="h-4 w-4 mr-2 text-green-600" />
                        Excel
                      </Label>
                    </div>
                  </RadioGroup>

                  {/* Add a note about PDF vs Excel */}
                  <div className="text-xs text-muted-foreground mt-1">
                    <p>
                      Note: Excel format is recommended if you have
                      invoices with Arabic text or if PDF export
                      fails.
                    </p>
                  </div>
                </div>

                <Separator />

                {/* Fields to Include */}
                <div className="space-y-3">
                  <Label>Fields to Include</Label>
                  <div className="grid grid-cols-2 gap-3">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="invoiceNumber"
                        checked={selectedFields.invoiceNumber}
                        onCheckedChange={(checked) =>
                          handleFieldChange(
                            'invoiceNumber',
                            checked as boolean
                          )
                        }
                      />
                      <Label htmlFor="invoiceNumber">
                        Invoice Number
                      </Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="date"
                        checked={selectedFields.date}
                        onCheckedChange={(checked) =>
                          handleFieldChange(
                            'date',
                            checked as boolean
                          )
                        }
                      />
                      <Label htmlFor="date">Issue Date</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="dueDate"
                        checked={selectedFields.dueDate}
                        onCheckedChange={(checked) =>
                          handleFieldChange(
                            'dueDate',
                            checked as boolean
                          )
                        }
                      />
                      <Label htmlFor="dueDate">Due Date</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="vendor"
                        checked={selectedFields.vendor}
                        onCheckedChange={(checked) =>
                          handleFieldChange(
                            'vendor',
                            checked as boolean
                          )
                        }
                      />
                      <Label htmlFor="vendor">Vendor</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="customer"
                        checked={selectedFields.customer}
                        onCheckedChange={(checked) =>
                          handleFieldChange(
                            'customer',
                            checked as boolean
                          )
                        }
                      />
                      <Label htmlFor="customer">Customer</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="amount"
                        checked={selectedFields.amount}
                        onCheckedChange={(checked) =>
                          handleFieldChange(
                            'amount',
                            checked as boolean
                          )
                        }
                      />
                      <Label htmlFor="amount">Amount</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="status"
                        checked={selectedFields.status}
                        onCheckedChange={(checked) =>
                          handleFieldChange(
                            'status',
                            checked as boolean
                          )
                        }
                      />
                      <Label htmlFor="status">Status</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="category"
                        checked={selectedFields.category}
                        onCheckedChange={(checked) =>
                          handleFieldChange(
                            'category',
                            checked as boolean
                          )
                        }
                      />
                      <Label htmlFor="category">Category</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="lineItems"
                        checked={selectedFields.lineItems}
                        onCheckedChange={(checked) =>
                          handleFieldChange(
                            'lineItems',
                            checked as boolean
                          )
                        }
                      />
                      <Label htmlFor="lineItems">Line Items</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="notes"
                        checked={selectedFields.notes}
                        onCheckedChange={(checked) =>
                          handleFieldChange(
                            'notes',
                            checked as boolean
                          )
                        }
                      />
                      <Label htmlFor="notes">Notes</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="termsAndConditions"
                        checked={selectedFields.termsAndConditions}
                        onCheckedChange={(checked) =>
                          handleFieldChange(
                            'termsAndConditions',
                            checked as boolean
                          )
                        }
                      />
                      <Label htmlFor="termsAndConditions">
                        Terms & Conditions
                      </Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="metadata"
                        checked={selectedFields.metadata}
                        onCheckedChange={(checked) =>
                          handleFieldChange(
                            'metadata',
                            checked as boolean
                          )
                        }
                      />
                      <Label htmlFor="metadata">Metadata</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="dynamicData"
                        checked={selectedFields.dynamicData}
                        onCheckedChange={(checked) =>
                          handleFieldChange(
                            'dynamicData',
                            checked as boolean
                          )
                        }
                      />
                      <Label htmlFor="dynamicData">
                        Custom Fields
                      </Label>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <DialogFooter className="flex justify-between">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
          >
            Cancel
          </Button>

          {activeTab === 'export' && (
            <Button
              onClick={handleExport}
              disabled={isExporting || !selectedFolder}
            >
              {isExporting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Exporting...
                </>
              ) : (
                <>
                  <FileText className="mr-2 h-4 w-4" />
                  Export {invoices.length} Invoice
                  {invoices.length !== 1 ? 's' : ''}
                </>
              )}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
