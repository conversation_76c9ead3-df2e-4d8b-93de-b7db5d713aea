import { Button } from '@/components/ui/button';
import { ChevronLeft, X } from 'lucide-react';
import Image from 'next/image';

interface CheckoutHeaderProps {
  onClose: () => void;
  onBack?: () => void;
  showBackButton?: boolean;
}

export function CheckoutHeader({ onClose, onBack, showBackButton = false }: CheckoutHeaderProps) {
  return (
    <header className="flex items-center justify-between p-6 border-b border-neutral-800 bg-black">
      <div className="flex items-center gap-4">
        {showBackButton && onBack && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onBack}
            className="text-gray-400 hover:text-white hover:bg-neutral-800"
          >
            <ChevronLeft className="h-4 w-4" />
            Back
          </Button>
        )}
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 bg-white rounded-lg flex items-center justify-center">
            <span className="text-black font-bold text-sm">B</span>
          </div>
          <span className="text-white font-semibold">Billix</span>
        </div>
      </div>
      
      <Button
        variant="ghost"
        size="sm"
        onClick={onClose}
        className="text-gray-400 hover:text-white hover:bg-neutral-800"
      >
        <X className="h-4 w-4" />
      </Button>
    </header>
  );
}
