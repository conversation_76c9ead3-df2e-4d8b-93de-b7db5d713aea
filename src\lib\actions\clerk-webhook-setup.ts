const CLERK_API_KEY = process.env.CLERK_API_KEY;
const CLERK_API_BASE = "https://api.clerk.dev/v1";
const WEBHOOK_URL = process.env.NEXT_PUBLIC_APP_URL + "/api/webhook/clerk";
const WEBHOOK_SECRET = process.env.CLERK_WEBHOOK_SECRET;

// Events to subscribe to
const EVENTS = [
  "user.created",
  "user.updated",
  "user.deleted",
  "session.created",
  "session.removed",
  "organizationMembership.created",
  "organizationMembership.deleted",
  "organization.created",
  "organization.updated",
  "organization.deleted",
];

async function setupWebhooks() {
  if (!CLERK_API_KEY) {
    console.error("CLERK_API_KEY is not set");
    return;
  }

  if (!WEBHOOK_URL) {
    console.error("WEBHOOK_URL is not set");
    return;
  }

  if (!WEBHOOK_SECRET) {
    console.error("WEBHOOK_SECRET is not set");
    return;
  }

  try {
    // First, list existing webhooks
    const existingWebhooks = await fetch(`${CLERK_API_BASE}/webhooks`, {
      headers: {
        Authorization: `Bearer ${CLERK_API_KEY}`,
        "Content-Type": "application/json",
      },
    }).then((res) => res.json());

    // Check if our webhook already exists
    const existingWebhook = existingWebhooks.data?.find(
      (webhook: { url: string }) => webhook.url === WEBHOOK_URL
    );

    if (existingWebhook) {
      console.log("Webhook already exists, updating...");

      // Update the webhook
      await fetch(`${CLERK_API_BASE}/webhooks/${existingWebhook.id}`, {
        method: "PATCH",
        headers: {
          Authorization: `Bearer ${CLERK_API_KEY}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          url: WEBHOOK_URL,
          active: true,
          subscribe_to_events: EVENTS,
        }),
      });

      console.log("Webhook updated successfully");
    } else {
      console.log("Creating new webhook...");

      // Create a new webhook
      await fetch(`${CLERK_API_BASE}/webhooks`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${CLERK_API_KEY}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          url: WEBHOOK_URL,
          active: true,
          subscribe_to_events: EVENTS,
        }),
      });

      console.log("Webhook created successfully");
    }
  } catch (error) {
    console.error("Error setting up webhooks:", error);
  }
}

// Export the function to be called from other files
export { setupWebhooks };
