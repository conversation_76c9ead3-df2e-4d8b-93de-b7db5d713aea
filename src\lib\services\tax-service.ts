"use server";

// Tax rate database with country-specific rates and rules
export const COUNTRY_TAX_RATES: Record<string, { 
  taxName: string, 
  defaultRate: number,
  additionalRates?: Record<string, number>,
  exemptionCategories?: string[],
  specialRules?: string[]
}> = {
  // Middle East and Arab countries
  "PS": { 
    taxName: "VAT", 
    defaultRate: 0.16,
    additionalRates: { "reduced": 0.08 },
    exemptionCategories: ["food", "medicine", "books", "exports"]
  },
  "IL": { 
    taxName: "VAT", 
    defaultRate: 0.17,
    exemptionCategories: ["exports", "tourism services", "fruits and vegetables"]
  },
  "JO": { 
    taxName: "GST", 
    defaultRate: 0.16,
    additionalRates: { "special": 0.10, "tourism": 0.08 }
  },
  "EG": { 
    taxName: "VAT", 
    defaultRate: 0.14,
    exemptionCategories: ["essential food items", "medicine", "financial services"]
  },
  "SA": { 
    taxName: "VAT", 
    defaultRate: 0.15,
    exemptionCategories: ["healthcare", "education", "residential rent"]
  },
  "AE": { 
    taxName: "VAT", 
    defaultRate: 0.05,
    exemptionCategories: ["healthcare", "education", "financial services", "residential properties"]
  },
  "QA": { 
    taxName: "No Tax", 
    defaultRate: 0.0
  },
  "KW": { 
    taxName: "No Tax", 
    defaultRate: 0.0
  },
  "BH": { 
    taxName: "VAT", 
    defaultRate: 0.10,
    exemptionCategories: ["healthcare", "education", "local transportation"]
  },
  "OM": { 
    taxName: "VAT", 
    defaultRate: 0.05,
    exemptionCategories: ["food items", "healthcare", "education"]
  },
  "LB": { 
    taxName: "VAT", 
    defaultRate: 0.11,
    exemptionCategories: ["food", "medicine", "books", "magazines"]
  },
  "SY": { 
    taxName: "VAT", 
    defaultRate: 0.10
  },
  "IQ": { 
    taxName: "Sales Tax", 
    defaultRate: 0.05
  },
  "YE": { 
    taxName: "GST", 
    defaultRate: 0.05
  },
  
  // Other regions
  "US": { 
    taxName: "Sales Tax", 
    defaultRate: 0.0825,
    specialRules: ["varies by state and locality"]
  },
  "GB": { 
    taxName: "VAT", 
    defaultRate: 0.2,
    additionalRates: { "reduced": 0.05, "zero": 0.0 }
  },
  "EU": { 
    taxName: "VAT", 
    defaultRate: 0.21,
    specialRules: ["varies by member state"]
  },
  "CA": { 
    taxName: "GST/HST", 
    defaultRate: 0.13,
    specialRules: ["varies by province"]
  },
  "AU": { 
    taxName: "GST", 
    defaultRate: 0.1
  },
  "NZ": { 
    taxName: "GST", 
    defaultRate: 0.15
  },
  "JP": { 
    taxName: "Consumption Tax", 
    defaultRate: 0.1
  },
  "SG": { 
    taxName: "GST", 
    defaultRate: 0.08
  },
  "IN": { 
    taxName: "GST", 
    defaultRate: 0.18,
    additionalRates: { "essential": 0.05, "standard": 0.12, "luxury": 0.28 }
  },
  "CN": { 
    taxName: "VAT", 
    defaultRate: 0.13,
    additionalRates: { "reduced": 0.09, "low": 0.06 }
  },
  "RU": { 
    taxName: "VAT", 
    defaultRate: 0.20,
    additionalRates: { "reduced": 0.10, "zero": 0.0 }
  },
  "BR": { 
    taxName: "ICMS", 
    defaultRate: 0.18,
    specialRules: ["varies by state"]
  },
  "DE": { 
    taxName: "VAT", 
    defaultRate: 0.19,
    additionalRates: { "reduced": 0.07 }
  },
  "FR": { 
    taxName: "VAT", 
    defaultRate: 0.2,
    additionalRates: { "reduced": 0.1, "super-reduced": 0.055 }
  },
  "IT": { 
    taxName: "VAT", 
    defaultRate: 0.22,
    additionalRates: { "reduced": 0.1, "super-reduced": 0.04 }
  },
  "ES": { 
    taxName: "VAT", 
    defaultRate: 0.21,
    additionalRates: { "reduced": 0.1, "super-reduced": 0.04 }
  }
};

// Currency to country mapping for tax detection
export const CURRENCY_TO_COUNTRY: Record<string, string> = {
  "USD": "US",
  "EUR": "EU",
  "GBP": "GB",
  "CAD": "CA",
  "AUD": "AU",
  "NZD": "NZ",
  "JPY": "JP",
  "SGD": "SG",
  "INR": "IN",
  "CNY": "CN",
  "RUB": "RU",
  "BRL": "BR",
  "ILS": "IL", // Israeli Shekel
  "JOD": "JO", // Jordanian Dinar
  "EGP": "EG", // Egyptian Pound
  "SAR": "SA", // Saudi Riyal
  "AED": "AE", // UAE Dirham
  "QAR": "QA", // Qatari Riyal
  "KWD": "KW", // Kuwaiti Dinar
  "BHD": "BH", // Bahraini Dinar
  "OMR": "OM", // Omani Rial
  "LBP": "LB", // Lebanese Pound
  "SYP": "SY", // Syrian Pound
  "IQD": "IQ", // Iraqi Dinar
  "YER": "YE"  // Yemeni Rial
};

/**
 * Calculate tax amount based on country and subtotal
 */
export function calculateTax(
  subtotal: number,
  countryCode: string,
  category?: string,
  isExempt: boolean = false
): { 
  taxAmount: number; 
  taxRate: number; 
  taxName: string;
  isEstimated: boolean;
} {
  // Default values
  let taxRate = 0;
  let taxName = "Tax";
  let isEstimated = true;
  
  // Get country tax info
  const countryTax = COUNTRY_TAX_RATES[countryCode];
  
  if (countryTax) {
    taxRate = countryTax.defaultRate;
    taxName = countryTax.taxName;
    isEstimated = false;
    
    // Check for exemptions
    if (isExempt || (category && countryTax.exemptionCategories?.includes(category.toLowerCase()))) {
      taxRate = 0;
    }
  } else {
    // If country not found, use a default rate
    taxRate = 0.1; // 10% as a fallback
    isEstimated = true;
  }
  
  // Calculate tax amount
  const taxAmount = subtotal * taxRate;
  
  return {
    taxAmount,
    taxRate,
    taxName,
    isEstimated
  };
}

/**
 * Validate tax calculation on an invoice
 */
export function validateTaxCalculation(
  subtotal: number,
  taxAmount: number,
  total: number,
  countryCode?: string
): { 
  isValid: boolean; 
  expectedTax?: number;
  expectedTotal?: number;
  discrepancy?: number;
  message?: string;
} {
  // Small epsilon for floating point comparison
  const epsilon = 0.01;
  
  // Calculate expected tax if country code is provided
  let expectedTax = taxAmount;
  if (countryCode && COUNTRY_TAX_RATES[countryCode]) {
    const { taxAmount: calculatedTax } = calculateTax(subtotal, countryCode);
    expectedTax = calculatedTax;
  }
  
  // Calculate expected total
  const expectedTotal = subtotal + expectedTax;
  
  // Check if the tax calculation is valid
  const taxDiscrepancy = Math.abs(taxAmount - expectedTax);
  const totalDiscrepancy = Math.abs(total - expectedTotal);
  
  // Tax is valid if the discrepancy is within epsilon
  const isTaxValid = taxDiscrepancy <= epsilon;
  const isTotalValid = totalDiscrepancy <= epsilon;
  
  if (isTaxValid && isTotalValid) {
    return { isValid: true };
  } else {
    return {
      isValid: false,
      expectedTax,
      expectedTotal,
      discrepancy: Math.max(taxDiscrepancy, totalDiscrepancy),
      message: !isTaxValid 
        ? `Tax amount appears incorrect. Expected: ${expectedTax.toFixed(2)}, Found: ${taxAmount.toFixed(2)}`
        : `Total amount appears incorrect. Expected: ${expectedTotal.toFixed(2)}, Found: ${total.toFixed(2)}`
    };
  }
}

/**
 * Get tax information for a specific country
 */
export function getTaxInfoForCountry(countryCode: string): {
  taxName: string;
  defaultRate: number;
  additionalRates?: Record<string, number>;
  exemptionCategories?: string[];
  specialRules?: string[];
} | null {
  return COUNTRY_TAX_RATES[countryCode] || null;
}
