"use server";

import { google, gmail_v1 } from 'googleapis';
import { OAuth2Client } from 'google-auth-library';
import { nanoid } from "nanoid";
import db from '@/db/db';

interface GmailTokens {
  access_token: string;
  refresh_token?: string;
  expiry_date: number;
}

interface GmailHeader {
  name: string;
  value: string;
}

interface GmailAttachment {
  filename: string;
  mimeType: string;
  body: {
    attachmentId: string;
    size?: number;
    data?: string;
  };
}

interface GmailMessagePayload {
  headers: GmailHeader[];
  mimeType: string;
  body?: {
    attachmentId?: string;
    size?: number;
    data?: string;
  };
  parts?: GmailMessagePayload[];
  filename?: string;
}

/**
 * Connect a user's Gmail account using OAuth
 */
export async function connectGmailAccount(userId: string, authCode: string) {
  try {
    const oauth2Client = new OAuth2Client(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      process.env.GOOGLE_REDIRECT_URI
    );
    
    // Exchange auth code for tokens
    const { tokens } = await oauth2Client.getToken(authCode);
    
    if (!tokens.access_token) {
      throw new Error("No access token received from Google");
    }
    
    // Set credentials to get user info
    oauth2Client.setCredentials({ access_token: tokens.access_token });
    
    // Get user's email from Gmail API
    const gmail = google.gmail({ version: 'v1', auth: oauth2Client });
    const profile = await gmail.users.getProfile({ userId: 'me' });
    const email = profile.data.emailAddress;
    
    if (!email) {
      throw new Error("Could not retrieve email address from Google");
    }
    
    // Store tokens in database for this user
    await storeGmailTokens(userId, {
      access_token: tokens.access_token as string,
      refresh_token: tokens.refresh_token || undefined,
      expiry_date: tokens.expiry_date || Date.now() + 3600 * 1000
    }, email);
    
    return { success: true, email };
  } catch (error) {
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

/**
 * Store Gmail tokens for a user
 */
async function storeGmailTokens(userId: string, tokens: GmailTokens, email: string) {
  try {
    // First check if the user exists in the database
    const user = await db.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      throw new Error(`User with ID ${userId} not found in database. Please complete account setup first.`);
    }
    
    // Check if user already has tokens stored
    const existingConnection = await db.emailIntegration.findFirst({
      where: { userId, provider: "gmail" }
    });
    
    if (existingConnection) {
      // Update existing tokens
      await db.emailIntegration.update({
        where: { id: existingConnection.id },
        data: {
          accessToken: tokens.access_token,
          refreshToken: tokens.refresh_token || existingConnection.refreshToken,
          expiresAt: new Date(tokens.expiry_date),
          email,
          active: true,
          updatedAt: new Date()
        }
      });
    } else {
      // Create new email integration record
      await db.emailIntegration.create({
        data: {
          id: nanoid(),
          userId,
          provider: "gmail",
          email,
          accessToken: tokens.access_token,
          refreshToken: tokens.refresh_token || "",
          expiresAt: new Date(tokens.expiry_date),
          active: true
        }
      });
    }
    
    // Create or update sync settings with defaults
    await db.emailSyncJob.upsert({
      where: { 
        userId_provider: {
          userId,
          provider: "gmail"
        }
      },
      create: {
        id: nanoid(),
        userId,
        provider: "gmail",
        frequency: "daily",
        autoProcess: true,
        includeRead: true,
        isActive: true
      },
      update: {
        isActive: true
      }
    });
    
    return { success: true };
  } catch (error) {
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

/**
 * Get Gmail tokens for a user
 */
async function getUserGmailTokens(userId: string): Promise<GmailTokens | null> {
  try {
    const emailIntegration = await db.emailIntegration.findFirst({
      where: { userId, provider: "gmail", active: true }
    });
    
    if (!emailIntegration) {
      throw new Error("No Gmail integration found for this user");
    }

    // Check if token is about to expire (within 5 minutes) or already expired
    const currentTime = new Date();
    const tokenExpiryTime = emailIntegration.expiresAt;
    const fiveMinutesFromNow = new Date(currentTime.getTime() + 5 * 60 * 1000);
    
    if (tokenExpiryTime <= fiveMinutesFromNow) {
      // Refresh the token
      if (!emailIntegration.refreshToken) {
        throw new Error("No refresh token available. User needs to reconnect their Gmail account.");
      }
      
      const oauth2Client = new OAuth2Client(
        process.env.GOOGLE_CLIENT_ID,
        process.env.GOOGLE_CLIENT_SECRET
      );
      
      oauth2Client.setCredentials({
        refresh_token: emailIntegration.refreshToken
      });
      
      try {
        const { credentials } = await oauth2Client.refreshAccessToken();
        
        if (!credentials.access_token) {
          throw new Error("Failed to refresh access token");
        }
        
        // Calculate proper expiry time
        const expiryDate = credentials.expiry_date || 
          (Date.now() + 3600 * 1000); // Default to 1 hour if no expiry date
        
        // Update tokens in database
        await db.emailIntegration.update({
          where: { id: emailIntegration.id },
          data: {
            accessToken: credentials.access_token,
            expiresAt: new Date(expiryDate),
            updatedAt: new Date()
          }
        });
        
        return {
          access_token: credentials.access_token,
          refresh_token: emailIntegration.refreshToken,
          expiry_date: expiryDate
        };
      } catch (refreshError) {
        // If refresh failed, mark integration as inactive
        await db.emailIntegration.update({
          where: { id: emailIntegration.id },
          data: { active: false, updatedAt: new Date() }
        });
        
        throw new Error(`Failed to refresh access token: ${refreshError instanceof Error ? refreshError.message : 'Unknown error'}`);
      }
    }
    
    // Return existing token if still valid
    return {
      access_token: emailIntegration.accessToken,
      refresh_token: emailIntegration.refreshToken || undefined,
      expiry_date: emailIntegration.expiresAt.getTime()
    };
  } catch (error) {
    return null;
  }
}

/**
 * Check if a user has connected their Gmail account
 */
export async function isGmailConnected(userId: string): Promise<{ connected: boolean, email?: string }> {
  try {
    const emailIntegration = await db.emailIntegration.findFirst({
      where: { userId, provider: "gmail", active: true }
    });
    
    if (!emailIntegration) {
      return { connected: false };
    }
    
    return { 
      connected: true,
      email: emailIntegration.email || undefined
    };
  } catch (error) {
    return { connected: false };
  }
}

/**
 * Record a sync history entry
 */
async function recordSyncHistory(userId: string, provider: string, processedCount: number, status: string, notes?: string) {
  try {
    // Create a sync history record in the database
    await db.emailSyncHistory.create({
      data: {
        id: nanoid(),
        userId,
        provider,
        processedCount,
        status,
        notes,
        createdAt: new Date()
      }
    });
    
    return { success: true };
  } catch (error) {
    // Non-critical function, so we just log the error but don't throw
    return { success: false, error };
  }
}

/**
 * Fetch invoice emails from a user's Gmail account
 */
export async function fetchInvoiceEmails(
  userId: string,
  dateRange?: { startDate: Date; endDate?: Date }
): Promise<{
  success: boolean;
  attachments: Array<{
    messageId: string;
    subject: string;
    from: string;
    date: string;
    filename: string;
    mimeType: string;
    data: Uint8Array | string;
    messageUrl?: string;
  }>;
  error?: string;
}> {
  try {
    // Get user's tokens
    const tokens = await getUserGmailTokens(userId);
    if (!tokens) {
      return { success: false, attachments: [], error: 'Failed to get Gmail tokens' };
    }
    
    const oauth2Client = new OAuth2Client(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET
    );
    oauth2Client.setCredentials(tokens);
    
    const gmail = google.gmail({ version: 'v1', auth: oauth2Client });
    
    // Get the Gmail sync settings for this user
    const syncSettings = await db.emailSyncJob.findFirst({
      where: { userId, provider: "gmail", isActive: true }
    });
    
    let query = 'has:attachment';
    
    // Add a broader keyword search
    query += ' (subject:invoice OR subject:receipt OR subject:order OR subject:bill OR subject:payment)';

    // Set date range for sync
    let startDate;
    if (dateRange?.startDate) {
      startDate = dateRange.startDate;
    } else if (syncSettings?.lastRun) {
      // Use the last sync time as the starting point
      startDate = syncSettings.lastRun;
    } else {
      // If no lastRun, default to last 30 days
      startDate = new Date();
      startDate.setDate(startDate.getDate() - 30);
    }

    // Set end date if provided
    const endDate = dateRange?.endDate || new Date();

    // Format dates correctly for Gmail query (YYYY/MM/DD)
    const formattedStartDate = startDate.toISOString().split('T')[0].replace(/-/g, '/');
    const formattedEndDate = endDate.toISOString().split('T')[0].replace(/-/g, '/');

    // Ensure dates are in the correct format for the Gmail API
    // Gmail uses YYYY/MM/DD format, not YYYY-MM-DD
    query += ` after:${formattedStartDate} before:${formattedEndDate}`;

    // Exclude read emails if needed
    if (syncSettings?.includeRead === false) {
      query += ' is:unread';
    }
    
    // Search for emails with attachments
    const response = await gmail.users.messages.list({
      userId: 'me',
      q: query,
      maxResults: 50
    });
    
    if (!response.data.messages || response.data.messages.length === 0) {
      // Update the lastRun time
      await db.emailSyncJob.update({
        where: { 
          userId_provider: {
            userId,
            provider: "gmail"
          }
        },
        data: {
          lastRun: new Date()
        }
      });
      
      await recordSyncHistory(userId, "gmail", 0, "success", "No emails with attachments found");
      return { success: true, attachments: [] };
    }
    
    // Extract attachments without processing them
    const attachments = await extractEmailAttachments(gmail as gmail_v1.Gmail, response.data.messages);
    
    // Update the lastRun time
    await db.emailSyncJob.update({
      where: { 
        userId_provider: {
          userId,
          provider: "gmail"
        }
      },
      data: {
        lastRun: new Date()
      }
    });
    
    // Record sync history
    await recordSyncHistory(
      userId, 
      "gmail", 
      attachments.length, 
      "success", 
      `Found ${attachments.length} attachments ready for processing`
    );
    
    return { success: true, attachments };
  } catch (error) {
    // Record failure in sync history
    await recordSyncHistory(
      userId, 
      "gmail", 
      0, 
      "error", 
      `Sync failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
    
    return { 
      success: false, 
      attachments: [],
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Extract email attachments without processing them
 */
async function extractEmailAttachments(
  gmail: gmail_v1.Gmail, 
  messages: gmail_v1.Schema$Message[]
): Promise<Array<{
  messageId: string;
  subject: string;
  from: string;
  date: string;
  filename: string;
  mimeType: string;
  data: Uint8Array | string;
  messageUrl?: string;
}>> {
  const extractedAttachments: Array<{
    messageId: string;
    subject: string;
    from: string;
    date: string;
    filename: string;
    mimeType: string;
    data: Uint8Array | string;
    messageUrl?: string;
  }> = [];
  
  // Process each message
  for (const message of messages) {
    try {
      if (!message.id) continue;
      
      // Get full message details
      const msg = await gmail.users.messages.get({
        userId: 'me',
        id: message.id,
        format: 'full'
      });
      
      if (!msg.data.payload?.headers) continue;
      
      const headers = msg.data.payload.headers;
      const subject = headers.find((h) => h.name === 'Subject')?.value || 'No Subject';
      const from = headers.find((h) => h.name === 'From')?.value || 'Unknown Sender';
      const date = headers.find((h) => h.name === 'Date')?.value || new Date().toISOString();
      
      // Get message URL (for reference)
      const messageUrl = `https://mail.google.com/mail/u/0/#inbox/${message.id}`;
      
      // Find attachments in the message
      const attachments = findAttachments(msg.data.payload as GmailMessagePayload);
      
      if (!attachments || attachments.length === 0) {
        continue;
      }
      
      // Extract each attachment's content
      for (const attachment of attachments) {
        try {
          if (!message.id || !attachment.body.attachmentId) continue;
          
          // Get attachment content
          const attachmentData = await gmail.users.messages.attachments.get({
            userId: 'me',
            messageId: message.id,
            id: attachment.body.attachmentId
          });
          
          // Return the attachment data for later processing
          extractedAttachments.push({
            messageId: message.id,
            subject,
            from,
            date,
            filename: attachment.filename,
            mimeType: attachment.mimeType,
            data: attachmentData.data.data || '',
            messageUrl
          });
          
        } catch (attachmentError) {
          // Continue to next attachment
          continue;
        }
      }
    } catch (error) {
      // Continue processing other emails
    }
  }
  
  return extractedAttachments;
}

/**
 * Find attachments in Gmail message payload
 */
function findAttachments(payload: GmailMessagePayload, attachments: GmailAttachment[] = []): GmailAttachment[] {
  if (!payload) return attachments;
  
  // Check if current part is an attachment
  const isAttachment = (
    payload.filename && 
    payload.filename.trim() !== '' && 
    payload.body && 
    payload.body.attachmentId
  );
  
  const isBinaryContent = (
    payload.mimeType && 
    !payload.mimeType.startsWith('text/') && 
    !payload.mimeType.includes('multipart/') &&
    payload.body && 
    payload.body.attachmentId
  );
  
  // Add to attachments if it's an attachment or binary content
  if ((isAttachment || isBinaryContent) && payload.body?.attachmentId) {
    attachments.push({
      filename: payload.filename || `attachment.${getExtensionFromMimeType(payload.mimeType)}`,
      mimeType: payload.mimeType,
      body: {
        attachmentId: payload.body.attachmentId,
        size: payload.body.size
      }
    });
  }
  
  // Recursively process parts (for multipart emails)
  if (payload.parts && payload.parts.length > 0) {
    for (const part of payload.parts) {
      findAttachments(part, attachments);
    }
  }
  
  return attachments;
}

/**
 * Helper to get file extension from MIME type
 */
function getExtensionFromMimeType(mimeType?: string): string {
  if (!mimeType) return 'bin';
  
  switch (mimeType.toLowerCase()) {
    case 'application/pdf': return 'pdf';
    case 'image/jpeg': return 'jpg';
    case 'image/png': return 'png';
    case 'image/tiff': return 'tiff';
    case 'application/vnd.ms-excel': return 'xls';
    case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': return 'xlsx';
    case 'text/csv': return 'csv';
    default: return 'bin';
  }
}

/**
 * Check if the file is a valid invoice type
 */
function isValidInvoiceFileType(filename: string): boolean {
  const lowerFilename = filename.toLowerCase();
  
  // First check for common invoice file extensions
  const validExtensions = [
    '.pdf', '.jpg', '.jpeg', '.png', '.tiff', '.tif', 
    '.xlsx', '.xls', '.csv', '.doc', '.docx', '.txt'
  ];
  
  for (const ext of validExtensions) {
    if (lowerFilename.endsWith(ext)) return true;
  }
  
  // If no extension matched, do a secondary check for invoice-related keywords in the filename
  const invoiceKeywords = ['invoice', 'receipt', 'bill', 'statement', 'order', 'purchase'];
  for (const keyword of invoiceKeywords) {
    if (lowerFilename.includes(keyword)) return true;
  }
  
  return false;
}

/**
 * Update email sync settings
 */
export async function updateEmailSyncSettings(
  userId: string, 
  settings: { 
    frequency?: 'hourly' | 'daily' | 'weekly', 
    autoProcess?: boolean,
    includeRead?: boolean,
    isActive?: boolean
  }
) {
  try {
    await db.emailSyncJob.upsert({
      where: { 
        userId_provider: {
          userId,
          provider: "gmail"
        }
      },
      create: {
        id: nanoid(),
        userId,
        provider: "gmail",
        frequency: settings.frequency || 'daily',
        autoProcess: settings.autoProcess !== false,
        includeRead: settings.includeRead !== false,
        isActive: settings.isActive !== false
      },
      update: {
        ...settings
      }
    });
    
    return { success: true };
  } catch (error) {
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

/**
 * Disconnect Gmail integration
 */
export async function disconnectGmail(userId: string) {
  try {
    // Get the integration
    const integration = await db.emailIntegration.findFirst({
      where: { userId, provider: "gmail", active: true }
    });
    
    if (!integration) {
      return { success: true, message: "No Gmail integration found" };
    }
    
    // Try to revoke tokens - revoke access token and refresh token separately
    // to avoid failures if one token is invalid
      const oauth2Client = new OAuth2Client(
        process.env.GOOGLE_CLIENT_ID,
        process.env.GOOGLE_CLIENT_SECRET
      );
      
    // Revoke access token if it exists
    if (integration.accessToken) {
      try {
      await oauth2Client.revokeToken(integration.accessToken);
      } catch (accessTokenError) {
        // Continue with the disconnection process
      }
    }
      
    // Revoke refresh token if it exists
      if (integration.refreshToken) {
      try {
        await oauth2Client.revokeToken(integration.refreshToken);
      } catch (refreshTokenError) {
        // Continue with the disconnection process
      }
    }
    
    // Mark the integration as inactive (don't delete for historical records)
    await db.emailIntegration.update({
      where: { id: integration.id },
      data: { 
        active: false,
        updatedAt: new Date()
      }
    });
    
    // Disable the sync job
    await db.emailSyncJob.updateMany({
      where: { userId, provider: "gmail" },
      data: { isActive: false }
    });
    
    return { success: true };
  } catch (error) {
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

/**
 * Run manual sync operation with specified date range
 */
export async function runManualEmailSync(
  userId: string,
  dateRange?: { startDate: Date; endDate?: Date }
): Promise<{
  success: boolean;
  attachments: Array<{
    messageId: string;
    subject: string;
    from: string;
    date: string;
    filename: string;
    mimeType: string;
    data: Uint8Array | string;
    messageUrl?: string;
  }>;
  error?: string;
}> {
  try {
    const result = await fetchInvoiceEmails(userId, dateRange);
    return result;
  } catch (error) {
    return {
      success: false,
      attachments: [],
      error: error instanceof Error ? error.message : "Unknown error"
    };
  }
} 