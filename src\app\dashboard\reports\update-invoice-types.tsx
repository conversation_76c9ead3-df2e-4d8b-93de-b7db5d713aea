"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { RefreshCw } from "lucide-react";
import { toast } from "sonner";

export default function UpdateInvoiceTypesButton() {
  const [loading, setLoading] = useState(false);

  const handleUpdateInvoiceTypes = async () => {
    setLoading(true);
    toast.loading("Updating invoice types...");

    try {
      const response = await fetch("/api/invoices/update-types", {
        method: "POST",
      });

      if (response.ok) {
        const data = await response.json();
        toast.success(data.message || "Invoice types updated successfully");
      } else {
        toast.error("Failed to update invoice types");
      }
    } catch (error) {
      console.error("Error updating invoice types:", error);
      toast.error("An error occurred while updating invoice types");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={handleUpdateInvoiceTypes}
      disabled={loading}
      className="flex items-center gap-2"
    >
      <RefreshCw className="h-4 w-4" />
      {loading ? "Updating..." : "Update Invoice Types"}
    </Button>
  );
}
