'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import {
  FileIcon,
  CheckIcon,
  BarChart2Icon,
  LineChartIcon,
  PieChartIcon,
  FileTextIcon,
  MailIcon,
  DownloadIcon,
  Loader2
} from 'lucide-react';
import { ReportType } from '@/lib/report-types';
import { toast } from 'sonner';

interface ReportCreatorProps {
  id: string;
  title: string;
  reportType: ReportType;
  format?: string;
  isComplete?: boolean;
  progress?: {
    step: number;
    totalSteps: number;
    message: string;
    percentage: number;
  };
  onDownload?: () => void;
  onEmail?: () => void;
}

export function ReportCreator({
  title,
  reportType,
  format = 'PDF',
  isComplete = false,
  progress,
  onDownload,
  onEmail
}: ReportCreatorProps) {
  const [status, setStatus] = useState<'creating' | 'complete'>(isComplete ? 'complete' : 'creating');
  const [isDownloading, setIsDownloading] = useState(false);
  const [isEmailing, setIsEmailing] = useState(false);

  useEffect(() => {
    if (isComplete) {
      setStatus('complete');
    }
  }, [isComplete]);

  const handleDownload = async () => {
    if (!onDownload) return;

    try {
      setIsDownloading(true);
      await onDownload();
      toast.success('Report downloaded successfully');
    } catch (error) {
      console.error('Error downloading report:', error);
      toast.error('Failed to download report');
    } finally {
      setIsDownloading(false);
    }
  };

  const handleEmail = async () => {
    if (!onEmail) return;

    try {
      setIsEmailing(true);
      await onEmail();
      toast.success('Report email sent successfully');
    } catch (error) {
      console.error('Error sending report email:', error);
      toast.error('Failed to send report email');
    } finally {
      setIsEmailing(false);
    }
  };

  // Get the appropriate icon based on report type
  const getReportIcon = () => {
    switch (reportType) {
      case ReportType.INVOICE_SUMMARY:
        return <FileTextIcon className="h-5 w-5" />;
      case ReportType.CASH_FLOW:
        return <LineChartIcon className="h-5 w-5" />;
      case ReportType.PROFIT_LOSS:
        return <BarChart2Icon className="h-5 w-5" />;
      case ReportType.VENDOR_ANALYSIS:
        return <PieChartIcon className="h-5 w-5" />;
      case ReportType.CATEGORY_BREAKDOWN:
        return <PieChartIcon className="h-5 w-5" />;
      case ReportType.BALANCE_SHEET:
        return <BarChart2Icon className="h-5 w-5" />;
      default:
        return <FileIcon className="h-5 w-5" />;
    }
  };

  // Format report type for display
  const formatReportType = (type: ReportType) => {
    return type.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
  };

  return (
    <Card className="w-full mb-4 border border-primary/30 shadow-md bg-primary/5">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="text-lg font-semibold flex items-center">
              <span className="mr-2">Creating Report</span>
              {status === 'complete' && (
                <Badge className="bg-green-500 text-white">
                  <CheckIcon className="h-3 w-3 mr-1" /> Created
                </Badge>
              )}
            </CardTitle>
            <CardDescription>
              {title} - {formatReportType(reportType)} ({format})
            </CardDescription>
          </div>

          {status === 'creating' && (
            <div className="flex items-center">
              <div className="flex gap-1.5 mr-2">
                <motion.div
                  className="h-2 w-2 bg-primary rounded-full"
                  animate={{ opacity: [0.4, 1, 0.4] }}
                  transition={{ duration: 1.5, repeat: Infinity, delay: 0 }}
                />
                <motion.div
                  className="h-2 w-2 bg-primary rounded-full"
                  animate={{ opacity: [0.4, 1, 0.4] }}
                  transition={{ duration: 1.5, repeat: Infinity, delay: 0.3 }}
                />
                <motion.div
                  className="h-2 w-2 bg-primary rounded-full"
                  animate={{ opacity: [0.4, 1, 0.4] }}
                  transition={{ duration: 1.5, repeat: Infinity, delay: 0.6 }}
                />
              </div>
              <span className="text-sm text-primary font-medium">Processing...</span>
            </div>
          )}
        </div>
      </CardHeader>

      <CardContent>
        <AnimatePresence>
          {status === 'creating' && progress && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="mt-2"
            >
              <div className="mb-2 flex justify-between items-center">
                <span className="text-sm font-medium">{progress.message}</span>
                <span className="text-sm text-muted-foreground">{progress.step} of {progress.totalSteps}</span>
              </div>
              <Progress value={progress.percentage} className="h-2" />
            </motion.div>
          )}
        </AnimatePresence>

        <div className="mt-4 flex items-center">
          <div className="mr-3 p-2 bg-primary/10 rounded-lg">
            {getReportIcon()}
          </div>
          <div>
            <h4 className="text-sm font-medium">{formatReportType(reportType)}</h4>
            <p className="text-xs text-muted-foreground">{format} Format</p>
          </div>
        </div>
      </CardContent>

      <CardFooter className="pt-2">
        {status === 'creating' && (
          <div className="w-full flex justify-center">
            <Button variant="ghost" size="sm" disabled className="text-primary">
              <FileIcon className="h-4 w-4 mr-1" />
              Generating report...
            </Button>
          </div>
        )}

        {status === 'complete' && (
          <div className="w-full flex justify-between">
            <span className="text-sm text-green-600">
              Report created successfully
            </span>
            <div className="flex gap-2">
              {onEmail && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleEmail}
                  disabled={isEmailing}
                >
                  {isEmailing ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                      Sending...
                    </>
                  ) : (
                    <>
                      <MailIcon className="h-4 w-4 mr-1" />
                      Email
                    </>
                  )}
                </Button>
              )}
              {onDownload && (
                <Button
                  variant="default"
                  size="sm"
                  onClick={handleDownload}
                  disabled={isDownloading}
                >
                  {isDownloading ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                      Downloading...
                    </>
                  ) : (
                    <>
                      <DownloadIcon className="h-4 w-4 mr-1" />
                      Download
                    </>
                  )}
                </Button>
              )}
            </div>
          </div>
        )}
      </CardFooter>
    </Card>
  );
}
