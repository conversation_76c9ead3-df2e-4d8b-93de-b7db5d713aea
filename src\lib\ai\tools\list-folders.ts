import { tool } from 'ai';
import { z } from 'zod';
import db from '@/db/db';
import { getAuthenticatedUserId } from '@/lib/auth-helpers';

export const listFolders = tool({
  description: 'Returns all of the user\'s export folders, each with its ID, name, and the list of invoice IDs contained.',
  parameters: z.object({
    searchTerm: z.string().optional().describe('Optional search term to filter folders by name'),
    limit: z.number().min(1).max(100).default(20).optional(),
    page: z.number().min(1).default(1).optional(),
  }),
  execute: async ({ searchTerm, limit = 20, page = 1 }) => {
    try {
      // Get the authenticated user ID
      const userId = await getAuthenticatedUserId();

      // Build the where clause for the query
      const where: Record<string, unknown> = {
        // Always filter by the current user
        userId,
      };

      // Add search term filter if provided
      if (searchTerm) {
        where.name = {
          contains: searchTerm,
          mode: 'insensitive',
        };
      }

      // Calculate pagination
      const skip = (page - 1) * limit;

      // Query the database
      const [folders, total] = await Promise.all([
        db.exportFolder.findMany({
          where,
          include: {
            invoices: {
              include: {
                invoice: true,
              },
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
          skip,
          take: limit,
        }),
        db.exportFolder.count({ where }),
      ]);

      // Format the response
      return {
        folders: folders.map(folder => ({
          id: folder.id,
          name: folder.name,
          invoiceCount: folder.invoiceCount,
          invoiceIds: folder.invoices.map(fi => fi.invoiceId),
          createdAt: folder.createdAt.toISOString(),
        })),
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      };
    } catch (error) {
      console.error('Error listing folders:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  },
});
