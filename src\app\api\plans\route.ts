import { NextResponse } from "next/server";
import { currentUser } from "@clerk/nextjs/server";
import db from "@/db/db";
import { syncPlans } from "@/actions/lemon-actions";

export async function GET() {
  try {
    const user = await currentUser();
    
    if (!user?.id) {
      return new NextResponse(JSON.stringify({ error: "Unauthorized" }), {
        status: 401,
        headers: {
          "Content-Type": "application/json",
        },
      });
    }
    
    // Get all plans from database
    let plans = await db.plan.findMany({
      orderBy: { sort: 'asc' }
    });
    
    // If no plans are found, sync them from Lemon Squeezy
    if (plans.length === 0) {
      await syncPlans();
      
      // Re-fetch from database to ensure we get the correct data
      plans = await db.plan.findMany({
        orderBy: { sort: 'asc' }
      });
    }
    
    return new NextResponse(JSON.stringify(plans), {
      status: 200,
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    console.error("Error fetching plans:", error);
    return new NextResponse(JSON.stringify({ error: "Internal Server Error" }), {
      status: 500,
      headers: {
        "Content-Type": "application/json",
      },
    });
  }
} 