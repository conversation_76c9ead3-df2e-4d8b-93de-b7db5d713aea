"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { ReportsList } from "./reports-list";
import { ReportTemplates } from "./report-templates";
import { ReportScheduler } from "./report-scheduler";
import { CustomReportBuilder } from "./custom-report-builder";
import { ReportMetrics } from "./report-metrics";
import { ReportHeader } from "./report-header";
import DashboardLayout from "@/components/layout/DashboardLayout";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Info } from "lucide-react";

export default function ReportsDashboard() {
  const [activeTab, setActiveTab] = useState("overview");

  return (
    <DashboardLayout>
      <div className="flex min-h-screen w-full flex-col">
        <ReportHeader activeTab={activeTab} />

        <main className="flex-1 space-y-6 p-6 md:p-8">
          <Alert className="mb-4 dark:bg-blue-500  bg-blue-50 border-blue-200">
            <Info className="h-4 w-4 mr-2" />
            <AlertDescription>
              Reports analyze data based on when invoices were added to the system, not their issue date.
            </AlertDescription>
          </Alert>
          
          <ReportMetrics />

          <Tabs
            defaultValue="overview"
            className="space-y-4"
            onValueChange={setActiveTab}
          >
            <TabsList className="grid w-full grid-cols-4 lg:w-auto">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="templates">Templates</TabsTrigger>
              <TabsTrigger value="scheduled">Scheduled</TabsTrigger>
              <TabsTrigger value="custom">Custom Reports</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              <ReportsList />
            </TabsContent>

            <TabsContent value="templates" className="space-y-4">
              <ReportTemplates />
            </TabsContent>

            <TabsContent value="scheduled" className="space-y-4">
              <ReportScheduler />
            </TabsContent>

            <TabsContent value="custom" className="space-y-4">
              <CustomReportBuilder />
            </TabsContent>
          </Tabs>
        </main>
      </div>
    </DashboardLayout>
  );
}
