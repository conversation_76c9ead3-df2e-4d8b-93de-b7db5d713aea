import type { InvoiceData } from "@/types/invoice";

export interface ProcessingFile {
  file: File;
  progress: number;
  status: 'pending' | 'processing' | 'completed' | 'error' | 'duplicate';
  error?: string;
  data?: InvoiceData;
  previewUrl?: string;
  source?: 'upload' | 'email';
  isDuplicate?: boolean;
  duplicateInvoiceNumber?: string;
  emailMetadata?: {
    messageId?: string;
    subject?: string;
    from?: string;
    date?: string;
    messageUrl?: string;
  };
} 