'use client';
import { useEffect, useState, useRef } from 'react';
import { ChartContainer, ChartTooltip } from '@/components/ui/chart';
import {
  Area,
  AreaChart,
  CartesianGrid,
  XAxis,
  YAxis,
  ResponsiveContainer,
  Legend,
  ReferenceLine,
} from 'recharts';
import { formatCurrency } from '@/lib/utils';
import { getExpenseTrends } from '@/lib/actions/analytics';
import type { FilterValues } from './data-filters';

interface ExpenseTrendsProps {
  dateRange: {
    from: Date;
    to: Date;
  };
  height?: number;
  filters: FilterValues;
}

interface ExpenseData {
  name: string;
  netSales: number;
  grossProfit: number;
  grossMargin: number;
}

export function ExpenseTrends({
  dateRange,
  filters,
}: ExpenseTrendsProps) {
  const [data, setData] = useState<ExpenseData[]>([]);
  const [loading, setLoading] = useState(true);
  const chartRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    async function loadExpenseData() {
      setLoading(true);
      try {
        // For demo purposes, generate sample data if the API returns empty data
        const expenseData = await getExpenseTrends(
          dateRange.from,
          dateRange.to,
          filters
        );

        setData(expenseData || []);
      } catch (error) {
        console.error('Failed to load expense trends:', error);
        setData([]);
      } finally {
        setLoading(false);
      }
    }

    loadExpenseData();
  }, [dateRange, filters]);

  // Premium color palette with vibrant gradients
  const chartConfig = {
    netSales: {
      label: 'Net Sales',
      color: 'hsl(210, 90%, 60%)', // Rich blue
    },
    grossProfit: {
      label: 'Gross Profit',
      color: 'hsl(170, 80%, 45%)', // Emerald green
    },
    grossMargin: {
      label: 'Gross Margin',
      color: 'hsl(260, 80%, 65%)', // Royal purple
    },
  };

  // Calculate average values for reference lines
  const avgNetSales =
    data.length > 0
      ? data.reduce((sum, item) => sum + item.netSales, 0) /
        data.length
      : 0;

  const avgGrossProfit =
    data.length > 0
      ? data.reduce((sum, item) => sum + item.grossProfit, 0) /
        data.length
      : 0;

  // Make sure we have reasonable min/max values to display properly
  const minValue =
    data.length > 0
      ? Math.max(
          0,
          Math.min(
            ...data.flatMap((item) => [
              item.netSales,
              item.grossProfit,
            ])
          ) * 0.7
        )
      : 0;

  const maxValue =
    data.length > 0
      ? Math.max(
          ...data.flatMap((item) => [item.netSales, item.grossProfit])
        ) * 1.3
      : 10000;

  // Ensure we have at least some range to display
  const yAxisDomain =
    maxValue - minValue < 1000 ? [0, 10000] : [minValue, maxValue];

  return (
    <div className="w-full h-full flex flex-col" ref={chartRef}>
      <ChartContainer
        config={chartConfig}
        className="flex-1 min-h-[350px] p-6 rounded-2xl bg-[#0f1c3f] border border-[#1a2c5c] shadow-xl hover:shadow-2xl transition-all duration-500 ease-out relative overflow-hidden"
      >
        <div className="w-full h-full">
          {/* Decorative background elements */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            <div className="absolute -right-20 -top-20 w-64 h-64 rounded-full bg-blue-500/5 blur-3xl"></div>
            <div className="absolute -left-20 -bottom-20 w-64 h-64 rounded-full bg-emerald-500/5 blur-3xl"></div>
          </div>

          <div className="mb-4">
            <h2 className="text-xl font-bold text-white">
              Expense Trends
            </h2>
            <p className="text-sm text-blue-300/80">
              Monthly expense patterns over time
            </p>
          </div>

          {loading ? (
            <div className="flex h-full items-center justify-center">
              <div className="flex flex-col items-center gap-4">
                <div className="relative h-16 w-16">
                  <div className="absolute inset-0 rounded-full bg-blue-500/10 animate-ping"></div>
                  <div className="absolute inset-2 rounded-full bg-blue-500/20 animate-pulse"></div>
                  <div
                    className="absolute inset-4 rounded-full bg-blue-500/30 animate-pulse"
                    style={{ animationDelay: '0.2s' }}
                  ></div>
                  <div
                    className="absolute inset-6 rounded-full bg-blue-500/40 animate-pulse"
                    style={{ animationDelay: '0.4s' }}
                  ></div>
                </div>
                <p className="text-blue-300 font-medium text-lg">
                  Loading chart data...
                </p>
                <div className="w-64 h-2 bg-blue-500/10 rounded-full overflow-hidden">
                  <div
                    className="h-full bg-blue-500/60 rounded-full animate-[loading_2s_ease-in-out_infinite]"
                    style={{ width: '30%' }}
                  ></div>
                </div>
              </div>
            </div>
          ) : data.length === 0 ? (
            <div className="flex h-full items-center justify-center">
              <div className="text-center p-8 rounded-xl border border-dashed border-blue-500/20 bg-[#162a54]/50 max-w-md mx-auto">
                <div className="w-20 h-20 mx-auto mb-4 rounded-full bg-blue-500/5 flex items-center justify-center">
                  <svg
                    className="w-10 h-10 text-blue-400/40"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={1.5}
                      d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                    />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold mb-2 text-white">
                  No data available
                </h3>
                <p className="text-blue-300/80 text-sm mb-4">
                  We couldn&apos;t find any expense data matching your
                  current filters.
                </p>
                <div className="text-sm text-blue-400 font-medium">
                  Try adjusting your filters or date range to see
                  results
                </div>
              </div>
            </div>
          ) : (
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart
                data={data}
                margin={{
                  top: 20,
                  right: 40,
                  left: 0,
                  bottom: 0,
                }}
              >
                <defs>
                  {/* Enhanced gradients with multiple color stops for more depth and dimension */}
                  <linearGradient
                    id="netSalesGradient"
                    x1="0"
                    y1="0"
                    x2="0"
                    y2="1"
                  >
                    <stop
                      offset="0%"
                      stopColor="#4d7cfe"
                      stopOpacity={0.95}
                    />
                    <stop
                      offset="40%"
                      stopColor="#4d7cfe"
                      stopOpacity={0.5}
                    />
                    <stop
                      offset="70%"
                      stopColor="#4d7cfe"
                      stopOpacity={0.2}
                    />
                    <stop
                      offset="100%"
                      stopColor="#4d7cfe"
                      stopOpacity={0.05}
                    />
                  </linearGradient>
                  <linearGradient
                    id="grossProfitGradient"
                    x1="0"
                    y1="0"
                    x2="0"
                    y2="1"
                  >
                    <stop
                      offset="0%"
                      stopColor="#10b981"
                      stopOpacity={0.95}
                    />
                    <stop
                      offset="40%"
                      stopColor="#10b981"
                      stopOpacity={0.5}
                    />
                    <stop
                      offset="70%"
                      stopColor="#10b981"
                      stopOpacity={0.2}
                    />
                    <stop
                      offset="100%"
                      stopColor="#10b981"
                      stopOpacity={0.05}
                    />
                  </linearGradient>

                  {/* Add sophisticated drop shadow filters for chart elements */}
                  <filter
                    id="areaShadowBlue"
                    x="-10%"
                    y="-10%"
                    width="120%"
                    height="130%"
                  >
                    <feDropShadow
                      dx="0"
                      dy="4"
                      stdDeviation="6"
                      floodColor="#2563eb"
                      floodOpacity="0.3"
                    />
                  </filter>
                  <filter
                    id="areaShadowGreen"
                    x="-10%"
                    y="-10%"
                    width="120%"
                    height="130%"
                  >
                    <feDropShadow
                      dx="0"
                      dy="4"
                      stdDeviation="6"
                      floodColor="#059669"
                      floodOpacity="0.3"
                    />
                  </filter>

                  {/* Glow effect for active elements */}
                  <filter
                    id="glow"
                    x="-20%"
                    y="-20%"
                    width="140%"
                    height="140%"
                  >
                    <feGaussianBlur stdDeviation="4" result="blur" />
                    <feComposite
                      in="SourceGraphic"
                      in2="blur"
                      operator="over"
                    />
                  </filter>
                </defs>
                <CartesianGrid
                  strokeDasharray="5 5"
                  vertical={false}
                  horizontal={true}
                  stroke="#2c3e67"
                  opacity={0.3}
                  strokeWidth={1}
                />
                <XAxis
                  dataKey="name"
                  tickLine={false}
                  axisLine={false}
                  padding={{ left: 15, right: 15 }}
                  stroke="#a3b8e0"
                  fontSize={13}
                  tick={{ fill: '#a3b8e0', fontWeight: 500 }}
                  dy={10}
                />
                <YAxis
                  tickFormatter={(value) => `$${value / 1000}k`}
                  tick={{ fill: '#a3b8e0', fontWeight: 500 }}
                  tickLine={false}
                  axisLine={false}
                  width={60}
                  domain={yAxisDomain}
                  stroke="#a3b8e0"
                  fontSize={12}
                  padding={{ top: 20 }}
                />
                <Legend
                  layout="horizontal"
                  verticalAlign="top"
                  align="center"
                  wrapperStyle={{
                    paddingBottom: '20px',
                  }}
                  iconType="circle"
                  iconSize={10}
                  formatter={(value) => (
                    <span
                      style={{
                        color: '#ffffff',
                        fontWeight: 600,
                        fontSize: '14px',
                        padding: '4px 10px',
                        borderRadius: '6px',
                        backgroundColor: 'rgba(30, 58, 138, 0.4)',
                        boxShadow: '0 2px 6px rgba(0,0,0,0.2)',
                      }}
                    >
                      {value}
                    </span>
                  )}
                />
                <ChartTooltip
                  content={({ active, payload, label }) => {
                    if (active && payload && payload.length) {
                      return (
                        <div className="rounded-xl border border-blue-500/20 bg-[#162a54]/95 backdrop-blur-md p-5 shadow-2xl min-w-[250px]">
                          <div className="flex items-center justify-between mb-3 pb-2 border-b border-blue-500/20">
                            <span className="font-semibold text-lg text-white">
                              {label}
                            </span>
                            <span className="text-xs px-2 py-1 bg-blue-500/20 rounded-md font-medium text-blue-300">
                              {label} 2023
                            </span>
                          </div>
                          <div className="space-y-3">
                            {payload.map((entry, index) => (
                              <div
                                key={`tooltip-${index}`}
                                className="flex items-center justify-between gap-4 py-1"
                              >
                                <div className="flex items-center gap-2">
                                  <div
                                    className="h-4 w-4 rounded-full"
                                    style={{
                                      backgroundColor: entry.color,
                                    }}
                                  />
                                  <span className="font-medium text-blue-100">
                                    {entry.name}:
                                  </span>
                                </div>
                                <span className="font-bold text-lg text-white">
                                  {formatCurrency(
                                    entry.value as number
                                  )}
                                </span>
                              </div>
                            ))}
                          </div>

                          {/* Show growth indicators */}
                          {payload.length > 1 && (
                            <div className="mt-3 pt-2 border-t border-blue-500/20">
                              <div className="flex items-center justify-between">
                                <span className="text-xs text-blue-300">
                                  Profit Margin:
                                </span>
                                <span className="text-sm font-semibold text-white">
                                  {(
                                    ((payload[1].value as number) /
                                      (payload[0].value as number)) *
                                    100
                                  ).toFixed(1)}
                                  %
                                </span>
                              </div>
                            </div>
                          )}
                        </div>
                      );
                    }
                    return null;
                  }}
                  wrapperStyle={{ outline: 'none' }}
                />

                {/* Reference lines for averages */}
                <ReferenceLine
                  y={avgNetSales}
                  stroke="#4d7cfe"
                  strokeDasharray="3 3"
                  strokeWidth={2}
                  opacity={0.7}
                  label={{
                    value: 'Avg Sales',
                    position: 'right',
                    fill: '#4d7cfe',
                    fontSize: 12,
                    fontWeight: 500,
                  }}
                />

                <ReferenceLine
                  y={avgGrossProfit}
                  stroke="#10b981"
                  strokeDasharray="3 3"
                  strokeWidth={2}
                  opacity={0.7}
                  label={{
                    value: 'Avg Profit',
                    position: 'right',
                    fill: '#10b981',
                    fontSize: 12,
                    fontWeight: 500,
                  }}
                />

                <Area
                  type="monotone"
                  dataKey="netSales"
                  stackId="1"
                  stroke="#4d7cfe"
                  strokeWidth={3}
                  fill="url(#netSalesGradient)"
                  animationDuration={2000}
                  filter="url(#areaShadowBlue)"
                  activeDot={{
                    r: 8,
                    strokeWidth: 3,
                    stroke: '#fff',
                    fill: '#4d7cfe',
                    strokeOpacity: 1,
                    filter: 'url(#glow)',
                    className: 'animate-pulse',
                  }}
                  dot={{
                    r: 4,
                    strokeWidth: 2,
                    stroke: '#fff',
                    fill: '#4d7cfe',
                    strokeOpacity: 0.8,
                  }}
                />
                <Area
                  type="monotone"
                  dataKey="grossProfit"
                  stackId="2"
                  stroke="#10b981"
                  strokeWidth={3}
                  fill="url(#grossProfitGradient)"
                  animationDuration={2000}
                  animationBegin={300}
                  filter="url(#areaShadowGreen)"
                  activeDot={{
                    r: 8,
                    strokeWidth: 3,
                    stroke: '#fff',
                    fill: '#10b981',
                    strokeOpacity: 1,
                    filter: 'url(#glow)',
                    className: 'animate-pulse',
                  }}
                  dot={{
                    r: 4,
                    strokeWidth: 2,
                    stroke: '#fff',
                    fill: '#10b981',
                    strokeOpacity: 0.8,
                  }}
                />
              </AreaChart>
            </ResponsiveContainer>
          )}

          {/* Floating stats cards */}
          {!loading && data.length > 0 && (
            <div className="absolute top-4 right-4 flex gap-3">
              <div className="px-4 py-2 rounded-lg bg-blue-500/10 backdrop-blur-sm border border-blue-500/20 shadow-md">
                <div className="text-xs text-blue-300 font-medium">
                  Avg. Sales
                </div>
                <div className="text-sm font-bold text-white">
                  ${Math.floor(avgNetSales).toLocaleString()}
                </div>
              </div>
              <div className="px-4 py-2 rounded-lg bg-emerald-500/10 backdrop-blur-sm border border-emerald-500/20 shadow-md">
                <div className="text-xs text-emerald-300 font-medium">
                  Avg. Profit
                </div>
                <div className="text-sm font-bold text-white">
                  ${Math.floor(avgGrossProfit).toLocaleString()}
                </div>
              </div>
            </div>
          )}
        </div>
      </ChartContainer>
    </div>
  );
}
