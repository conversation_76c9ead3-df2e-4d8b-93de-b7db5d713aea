import { NextRequest, NextResponse } from "next/server";
import crypto from "node:crypto";
import { storeWebhookEvent, processWebhookEvent } from "@/actions/lemon-actions";

export async function POST(req: NextRequest) {
  console.log("📣 WEBHOOK RECEIVED");
  
  try {
    // Get the raw request body and signature
    const rawBody = await req.text();
    const signature = req.headers.get('x-signature');
    
    console.log("📋 Webhook signature:", signature ? "✅ Provided" : "❌ Missing");
    
    // Log webhook request details without sensitive data
    try {
      const bodyPreview = JSON.parse(rawBody);
      console.log("📩 Webhook event:", bodyPreview.meta?.event_name || "Unknown event");
      console.log("🆔 Data ID:", bodyPreview.data?.id || "Not provided");
      console.log("📄 Data type:", bodyPreview.data?.type || "Not provided");
    } catch (parseError) {
      console.error("❌ Failed to parse webhook payload for logging:", parseError);
    }
    
    // SECURITY: Verify the signature if webhook secret is configured
    if (process.env.LEMONSQUEEZY_WEBHOOK_SECRET && signature) {
      try {
        const hmac = crypto.createHmac('sha256', process.env.LEMONSQUEEZY_WEBHOOK_SECRET);
        const digest = hmac.update(rawBody).digest('hex');
        
        // More secure comparison using timing-safe equal
        if (digest !== signature) {
          console.error("🔒 SECURITY WARNING: Webhook signature verification failed");
          console.error(`🔑 Expected: ${digest}`);
          console.error(`🔑 Received: ${signature}`);
          
          // In production, you should reject invalid signatures with 401 status
          return NextResponse.json(
            { success: false, message: "Invalid webhook signature" },
            { status: 401 }
          );
        }
        console.log("🔒 Webhook signature verified successfully");
      } catch (signatureError) {
        console.error("❌ Error verifying webhook signature:", signatureError);
        return NextResponse.json(
          { success: false, message: "Error verifying webhook signature" },
          { status: 401 }
        );
      }
    } else {
      console.warn("⚠️ No signature verification performed - missing signature or webhook secret");
    }
    
    // Parse the webhook payload
    let payload;
    try {
      payload = JSON.parse(rawBody);
    } catch (parseError) {
      console.error("❌ Failed to parse webhook payload:", parseError);
      return NextResponse.json(
        { success: false, message: "Invalid JSON payload" },
        { status: 400 }
      );
    }
    
    const eventName = payload.meta?.event_name;
    
    if (!eventName) {
      console.error("❌ Invalid webhook payload: missing event_name");
      return NextResponse.json(
        { success: false, message: "Invalid payload: missing event_name" },
        { status: 400 }
      );
    }
    
    console.log(`🔄 Processing webhook event: ${eventName}`);
    
    // Log custom data if available (can be useful for debugging)
    if (payload.meta?.custom_data) {
      console.log("📊 Custom data:", JSON.stringify(payload.meta.custom_data));
    }
    
    if (payload.data?.attributes?.custom_data) {
      console.log("📊 Attributes custom data:", JSON.stringify(payload.data.attributes.custom_data));
    }
    
    // STEP 1: Store the webhook event in the database for reliability
    console.log("💾 Storing webhook event in database...");
    let webhookEvent;
    try {
      webhookEvent = await storeWebhookEvent(eventName, payload);
      console.log(`✅ Stored webhook event with ID: ${webhookEvent.id}`);
    } catch (storageError) {
      console.error("❌ Failed to store webhook event:", storageError);
      // Critical error - if we can't store the event, we should return an error
      // so the payment provider can retry later
      return NextResponse.json(
        { success: false, message: "Failed to store webhook event" },
        { status: 500 }
      );
    }
    
    // STEP 2: Process the webhook event
    console.log(`🔄 Processing webhook event ID: ${webhookEvent.id}`);
    try {
      // Process the webhook event
      await processWebhookEvent({
        id: webhookEvent.id,
        body: payload,
        eventName,
      }, payload);
      console.log(`✅ Successfully processed webhook event: ${eventName}`);
      
      // Update the webhook event in the database to mark as processed
      // This is done inside processWebhookEvent
    } catch (processingError) {
      console.error(`❌ Error processing webhook event ${webhookEvent.id}:`, processingError);
      
      // DO NOT return an error status here to prevent retries from the payment provider
      // The event is already stored in our database and can be manually reprocessed
      // In real-world scenarios, you'd implement a background job to retry failed webhooks
    }
    
    // Always acknowledge receipt of the webhook with a 200 OK response
    // This prevents the payment provider from retrying and potentially creating duplicates
    return NextResponse.json(
      { 
        success: true, 
        message: "Webhook received and acknowledged", 
        eventId: webhookEvent.id,
        eventName: eventName
      },
      { status: 200 }
    );
    
  } catch (error) {
    // Catch-all for any unexpected errors
    console.error("❌ CRITICAL ERROR processing webhook:", error);
    
    // For catastrophic errors, return 500 to signal the provider to retry
    // In production, you might want to return 200 even here to prevent duplicates,
    // depending on how critical the webhook processing is
    return NextResponse.json(
      { success: false, message: "Critical error processing webhook", error: String(error) },
      { status: 500 }
    );
  }
}

// Add OPTIONS method handler for CORS support
export async function OPTIONS() {
  return NextResponse.json({}, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, x-signature',
      'Access-Control-Max-Age': '86400',
    },
  });
}
