import { z } from 'zod';
import { DataStreamWriter, streamObject, tool } from 'ai';
import { generateUUID } from '@/lib/utils';
import { myProvider } from '../models';
import db from '@/db/db';

interface RequestSuggestionsProps {
  userId: string;
  dataStream: DataStreamWriter;
}

export const requestSuggestions = ({
  userId,
  dataStream,
}: RequestSuggestionsProps) =>
  tool({
    description: 'Request suggestions for a document',
    parameters: z.object({
      documentId: z
        .string()
        .describe('The ID of the document to request edits'),
    }),
    execute: async ({ documentId }) => {
      const documents = await db.document.findMany({
        where: { id: documentId }
      });

      if (!documents || documents.length === 0 || !documents[0].content) {
        return {
          error: 'Document not found',
        };
      }

      const document = documents[0];

      const suggestions: Array<{
        id: string;
        documentId: string;
        originalText: string;
        suggestedText: string;
        description: string;
        isResolved: boolean;
      }> = [];

      const { elementStream } = streamObject({
        model: myProvider.languageModel('artifact-model'),
        system:
          'You are a help writing assistant. Given a piece of writing, please offer suggestions to improve the piece of writing and describe the change. It is very important for the edits to contain full sentences instead of just words. Max 5 suggestions.',
        prompt: document.content || '',
        output: 'array',
        schema: z.object({
          originalSentence: z.string().describe('The original sentence'),
          suggestedSentence: z.string().describe('The suggested sentence'),
          description: z.string().describe('The description of the suggestion'),
        }),
      });

      for await (const element of elementStream) {
        const suggestion = {
          originalText: element.originalSentence,
          suggestedText: element.suggestedSentence,
          description: element.description,
          id: generateUUID(),
          documentId: documentId,
          isResolved: false,
        };

        dataStream.writeData({
          type: 'suggestion',
          content: suggestion,
        });

        suggestions.push(suggestion);
      }

      if (userId) {
        // Save suggestions to database
        await db.suggestion.createMany({
          data: suggestions.map((suggestion) => ({
            ...suggestion,
            userId,
            createdAt: new Date(),
            documentCreatedAt: document.createdAt,
          })),
        });
      }

      return {
        id: documentId,
        title: document.title,
        kind: document.kind,
        message: 'Suggestions have been added to the document',
      };
    },
  });
