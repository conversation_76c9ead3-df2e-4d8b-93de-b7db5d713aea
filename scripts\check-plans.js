const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkPlans() {
  console.log('🔍 Checking existing plans...');

  try {
    const allPlans = await prisma.plan.findMany({
      select: {
        id: true,
        productName: true,
        name: true,
        chatLimit: true,
        invoiceLimit: true,
        price: true,
        paddlePriceId: true,
      },
    });

    console.log(`\n📊 Found ${allPlans.length} plans in database:`);

    if (allPlans.length === 0) {
      console.log('❌ No plans found in database!');
    } else {
      allPlans.forEach((plan, index) => {
        console.log(`\n${index + 1}. Plan ID: ${plan.id}`);
        console.log(`   Product Name: ${plan.productName || 'NULL'}`);
        console.log(`   Name: ${plan.name}`);
        console.log(`   Price: $${plan.price}`);
        console.log(`   Chat Limit: ${plan.chatLimit || 'NULL'}`);
        console.log(
          `   Invoice Limit: ${plan.invoiceLimit || 'NULL'}`
        );
        console.log(
          `   Paddle Price ID: ${plan.paddlePriceId || 'NULL'}`
        );
      });
    }

    return { success: true, plans: allPlans };
  } catch (error) {
    console.error('❌ Error checking plans:', error);
    return { success: false, error: error.message };
  } finally {
    await prisma.$disconnect();
  }
}

// Run the check
checkPlans()
  .then((result) => {
    if (result.success) {
      console.log('\n✅ Plan check completed successfully!');
      process.exit(0);
    } else {
      console.error('\n❌ Plan check failed:', result.error);
      process.exit(1);
    }
  })
  .catch((error) => {
    console.error('\n💥 Unexpected error:', error);
    process.exit(1);
  });
