"use client"

import { useState, useEffect } from "react"
import { useRouter, usePathname, useSearchParams } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { Card, CardContent } from "@/components/ui/card"
import { format } from "date-fns"
import { Search, Filter, CalendarIcon, X, CheckCircle2, Clock, AlertCircle, XCircle, Tag } from "lucide-react"
import { Badge } from "@/components/ui/badge"

export function InvoiceFilters() {
  const router = useRouter()
  const pathname = usePathname()
  const searchParams = useSearchParams()

  const [search, setSearch] = useState(searchParams.get("search") || "")
  const [status, setStatus] = useState(searchParams.get("status") || "")
  const [category, setCategory] = useState(searchParams.get("category") || "")
  const [vendor, setVendor] = useState(searchParams.get("vendor") || "")
  const [date, setDate] = useState<Date | undefined>(
    searchParams.get("date") ? new Date(searchParams.get("date") as string) : undefined,
  )

  const [categories, setCategories] = useState<{ id: string; name: string }[]>([])
  const [vendors, setVendors] = useState<{ id: string; name: string }[]>([])
  const [activeFilters, setActiveFilters] = useState(0)

  // Fetch categories and vendors
  useEffect(() => {
    // This would be replaced with actual API calls
    setCategories([
      { id: "technology", name: "Technology" },
      { id: "office-supplies", name: "Office Supplies" },
      { id: "marketing", name: "Marketing" },
      { id: "utilities", name: "Utilities" },
      { id: "travel", name: "Travel" },
    ])

    setVendors([
      { id: "acme-inc", name: "Acme Inc" },
      { id: "globex", name: "Globex Corporation" },
      { id: "initech", name: "Initech" },
      { id: "umbrella-corp", name: "Umbrella Corporation" },
    ])
  }, [])

  // Count active filters
  useEffect(() => {
    let count = 0
    if (status) count++
    if (category) count++
    if (vendor) count++
    if (date) count++
    setActiveFilters(count)
  }, [status, category, vendor, date])

  const createQueryString = (params: Record<string, string | null>) => {
    const newParams = new URLSearchParams(searchParams.toString())

    Object.entries(params).forEach(([key, value]) => {
      if (value === null) {
        newParams.delete(key)
      } else {
        newParams.set(key, value)
      }
    })

    return newParams.toString()
  }

  const handleSearch = () => {
    router.push(`${pathname}?${createQueryString({ search })}`)
  }

  const handleStatusChange = (value: string) => {
    setStatus(value)
    router.push(`${pathname}?${createQueryString({ status: value || null })}`)
  }

  const handleCategoryChange = (value: string) => {
    setCategory(value)
    router.push(`${pathname}?${createQueryString({ category: value || null })}`)
  }

  const handleVendorChange = (value: string) => {
    setVendor(value)
    router.push(`${pathname}?${createQueryString({ vendor: value || null })}`)
  }

  const handleDateChange = (date: Date | undefined) => {
    setDate(date)
    router.push(
      `${pathname}?${createQueryString({
        date: date ? format(date, "yyyy-MM-dd") : null,
      })}`,
    )
  }

  const clearFilters = () => {
    setStatus("")
    setCategory("")
    setVendor("")
    setDate(undefined)
    router.push(pathname)
  }

  return (
    <Card className="mb-6">
      <CardContent className="p-4">
        <div className="flex flex-col space-y-4">
          <div className="flex flex-col sm:flex-row gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search invoices..."
                className="pl-8"
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    handleSearch()
                  }
                }}
              />
            </div>
            <div className="flex flex-wrap gap-2">
              <Select value={status} onValueChange={handleStatusChange}>
                <SelectTrigger className="w-[140px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">All Statuses</SelectItem>
                  <SelectItem value="PAID">
                    <div className="flex items-center">
                      <CheckCircle2 className="mr-2 h-4 w-4 text-green-500" />
                      Paid
                    </div>
                  </SelectItem>
                  <SelectItem value="PENDING">
                    <div className="flex items-center">
                      <Clock className="mr-2 h-4 w-4 text-amber-500" />
                      Pending
                    </div>
                  </SelectItem>
                  <SelectItem value="OVERDUE">
                    <div className="flex items-center">
                      <AlertCircle className="mr-2 h-4 w-4 text-red-500" />
                      Overdue
                    </div>
                  </SelectItem>
                  <SelectItem value="CANCELLED">
                    <div className="flex items-center">
                      <XCircle className="mr-2 h-4 w-4 text-gray-500" />
                      Cancelled
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>

              <Select value={category} onValueChange={handleCategoryChange}>
                <SelectTrigger className="w-[160px]">
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">All Categories</SelectItem>
                  {categories.map((cat) => (
                    <SelectItem key={cat.id} value={cat.id}>
                      <div className="flex items-center">
                        <Tag className="mr-2 h-4 w-4 text-purple-500" />
                        {cat.name}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={vendor} onValueChange={handleVendorChange}>
                <SelectTrigger className="w-[160px]">
                  <SelectValue placeholder="Vendor" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">All Vendors</SelectItem>
                  {vendors.map((v) => (
                    <SelectItem key={v.id} value={v.id}>
                      {v.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant={date ? "default" : "outline"}
                    className="w-[160px] justify-start text-left font-normal"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {date ? format(date, "PPP") : "Pick a date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar mode="single" selected={date} onSelect={handleDateChange} initialFocus />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          {activeFilters > 0 && (
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Filter className="h-4 w-4 mr-2 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">
                  {activeFilters} active filter{activeFilters !== 1 ? "s" : ""}
                </span>
                {status && (
                  <Badge variant="secondary" className="ml-2">
                    Status: {status.charAt(0) + status.slice(1).toLowerCase()}
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-4 w-4 ml-1 p-0"
                      onClick={() => handleStatusChange("")}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                )}
                {category && (
                  <Badge variant="secondary" className="ml-2">
                    Category: {categories.find((c) => c.id === category)?.name || category}
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-4 w-4 ml-1 p-0"
                      onClick={() => handleCategoryChange("")}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                )}
                {vendor && (
                  <Badge variant="secondary" className="ml-2">
                    Vendor: {vendors.find((v) => v.id === vendor)?.name || vendor}
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-4 w-4 ml-1 p-0"
                      onClick={() => handleVendorChange("")}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                )}
                {date && (
                  <Badge variant="secondary" className="ml-2">
                    Date: {format(date, "PP")}
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-4 w-4 ml-1 p-0"
                      onClick={() => handleDateChange(undefined)}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                )}
              </div>
              <Button variant="ghost" size="sm" onClick={clearFilters} className="text-muted-foreground">
                Clear All
              </Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
