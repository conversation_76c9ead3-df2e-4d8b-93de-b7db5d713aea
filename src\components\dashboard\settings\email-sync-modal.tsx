"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON>Header, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Calendar, CalendarIcon, Loader2 } from "lucide-react";
import { format, subMonths, startOfMonth, endOfMonth, startOfYear } from "date-fns";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { cn } from "@/lib/utils";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";

interface EmailSyncModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSync: (dateRange: { startDate: Date; endDate?: Date }) => Promise<void>;
  isLoading?: boolean;
}

const predefinedRanges = [
  {
    id: "this-month",
    label: "This month's invoices",
    getDateRange: () => ({
      startDate: startOfMonth(new Date()),
      endDate: new Date(),
    }),
  },
  {
    id: "last-month",
    label: "Last month's invoices",
    getDateRange: () => {
      const lastMonth = subMonths(new Date(), 1);
      return {
        startDate: startOfMonth(lastMonth),
        endDate: endOfMonth(lastMonth),
      };
    },
  },
  {
    id: "this-year",
    label: "This year's invoices",
    getDateRange: () => ({
      startDate: startOfYear(new Date()),
      endDate: new Date(),
    }),
  },
  {
    id: "last-90-days",
    label: "Last 90 days",
    getDateRange: () => {
      const ninetyDaysAgo = new Date();
      ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);
      return {
        startDate: ninetyDaysAgo,
        endDate: new Date(),
      };
    },
  },
  {
    id: "last-6-months",
    label: "Last 6 months",
    getDateRange: () => {
      const sixMonthsAgo = subMonths(new Date(), 6);
      return {
        startDate: sixMonthsAgo,
        endDate: new Date(),
      };
    },
  },
  {
    id: "custom",
    label: "Custom date range",
    getDateRange: () => ({
      startDate: new Date(),
      endDate: new Date(),
    }),
  },
];

export function EmailSyncModal({ isOpen, onClose, onSync, isLoading = false }: EmailSyncModalProps) {
  const [selectedRange, setSelectedRange] = useState(predefinedRanges[0].id);
  const [customStartDate, setCustomStartDate] = useState<Date | undefined>(new Date());
  const [customEndDate, setCustomEndDate] = useState<Date | undefined>(new Date());
  const [openStartDate, setOpenStartDate] = useState(false);
  const [openEndDate, setOpenEndDate] = useState(false);
  
  const handleSync = async () => {
    try {
      let dateRange;
      
      if (selectedRange === "custom") {
        if (!customStartDate) {
          return; // Don't proceed if no date is selected
        }
        dateRange = {
          startDate: customStartDate,
          endDate: customEndDate,
        };
      } else {
        const rangeConfig = predefinedRanges.find(r => r.id === selectedRange);
        if (!rangeConfig) return;
        dateRange = rangeConfig.getDateRange();
      }
      
      await onSync(dateRange);
    } catch (error) {
      console.error("Sync error:", error);
    }
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={open => !isLoading && !open && onClose()}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Sync Email Invoices</DialogTitle>
          <DialogDescription>
            Choose a date range to sync your email invoices
          </DialogDescription>
        </DialogHeader>
        
        <div className="py-4">
          <RadioGroup 
            value={selectedRange} 
            onValueChange={setSelectedRange}
            className="space-y-3"
          >
            {predefinedRanges.map((range) => (
              <div key={range.id} className="flex items-center space-x-2">
                <RadioGroupItem value={range.id} id={range.id} />
                <Label 
                  htmlFor={range.id} 
                  className="flex-1 cursor-pointer"
                >
                  {range.label}
                </Label>
                {range.id !== "custom" && range.id === selectedRange && (
                  <span className="text-xs text-muted-foreground">
                    {format(range.getDateRange().startDate, "MMM d, yyyy")} - {format(range.getDateRange().endDate, "MMM d, yyyy")}
                  </span>
                )}
              </div>
            ))}
          </RadioGroup>
          
          {selectedRange === "custom" && (
            <div className="grid grid-cols-2 gap-4 mt-4">
              <div className="space-y-2">
                <Label htmlFor="start-date">Start Date</Label>
                <Popover open={openStartDate} onOpenChange={setOpenStartDate}>
                  <PopoverTrigger asChild>
                    <Button
                      id="start-date"
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !customStartDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {customStartDate ? format(customStartDate, "PPP") : <span>Pick a date</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <CalendarComponent
                      mode="single"
                      selected={customStartDate}
                      onSelect={(date) => {
                        setCustomStartDate(date);
                        setOpenStartDate(false);
                      }}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="end-date">End Date (Optional)</Label>
                <Popover open={openEndDate} onOpenChange={setOpenEndDate}>
                  <PopoverTrigger asChild>
                    <Button
                      id="end-date"
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !customEndDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {customEndDate ? format(customEndDate, "PPP") : <span>Pick a date</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <CalendarComponent
                      mode="single"
                      selected={customEndDate}
                      onSelect={(date) => {
                        setCustomEndDate(date);
                        setOpenEndDate(false);
                      }}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
          )}
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button onClick={handleSync} disabled={isLoading || (selectedRange === "custom" && !customStartDate)}>
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Syncing...
              </>
            ) : (
              <>
                <Calendar className="mr-2 h-4 w-4" />
                Sync Invoices
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 