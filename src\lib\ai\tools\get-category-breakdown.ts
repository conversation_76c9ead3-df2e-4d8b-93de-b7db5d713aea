import { tool } from 'ai';
import { z } from 'zod';
import db from '@/db/db';
import { getAuthenticatedUserId } from '@/lib/auth-helpers';

export const getCategoryBreakdown = tool({
  description: 'Summarizes invoice totals grouped by category over a given period, showing where money was allocated.',
  parameters: z.object({
    startDate: z.string().optional().describe('Optional start date for the period to analyze (YYYY-MM-DD)'),
    endDate: z.string().optional().describe('Optional end date for the period to analyze (YYYY-MM-DD)'),
    currency: z.string().optional().describe('Optional currency to filter by (e.g., USD, EUR)'),
    includeUncategorized: z.boolean().default(true).optional().describe('Whether to include uncategorized invoices'),
  }),
  execute: async ({ startDate, endDate, currency, includeUncategorized = true }) => {
    try {
      // Get the authenticated user ID
      const userId = await getAuthenticatedUserId();

      // Build the where clause for the query
      const where: Record<string, unknown> = {
        userId,
        status: {
          not: 'CANCELLED', // Exclude cancelled invoices
        },
      };

      // Add date filters if provided
      if (startDate) {
        where.issueDate = {
          ...(where.issueDate || {}),
          gte: new Date(startDate),
        };
      }

      if (endDate) {
        where.issueDate = {
          ...(where.issueDate || {}),
          lte: new Date(endDate),
        };
      }

      // Add currency filter if provided
      if (currency) {
        where.currency = currency;
      }

      // Get all invoices for the user with the applied filters, including their categories
      const invoices = await db.invoice.findMany({
        where,
        select: {
          id: true,
          amount: true,
          currency: true,
          issueDate: true,
          category: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      // Group invoices by category
      const categorySpend: Record<string, {
        categoryId: string | null;
        categoryName: string;
        totalAmount: number;
        invoiceCount: number;
        currencies: Record<string, number>;
        monthlyTrend: Record<string, number>;
      }> = {};

      // Calculate category spend
      for (const invoice of invoices) {
        const categoryId = invoice.category?.id || null;
        const categoryName = invoice.category?.name || 'Uncategorized';
        
        // Skip uncategorized if not included
        if (!includeUncategorized && !categoryId) {
          continue;
        }

        const amount = invoice.amount || 0;
        const currencyKey = invoice.currency || 'UNKNOWN';
        const categoryKey = categoryId || 'uncategorized';
        
        // Create category entry if it doesn't exist
        if (!categorySpend[categoryKey]) {
          categorySpend[categoryKey] = {
            categoryId,
            categoryName,
            totalAmount: 0,
            invoiceCount: 0,
            currencies: {},
            monthlyTrend: {},
          };
        }

        // Update category stats
        categorySpend[categoryKey].totalAmount += amount;
        categorySpend[categoryKey].invoiceCount++;

        // Update currency breakdown
        if (!categorySpend[categoryKey].currencies[currencyKey]) {
          categorySpend[categoryKey].currencies[currencyKey] = 0;
        }
        categorySpend[categoryKey].currencies[currencyKey] += amount;

        // Update monthly trend
        if (invoice.issueDate) {
          const monthYear = `${invoice.issueDate.getFullYear()}-${String(invoice.issueDate.getMonth() + 1).padStart(2, '0')}`;
          if (!categorySpend[categoryKey].monthlyTrend[monthYear]) {
            categorySpend[categoryKey].monthlyTrend[monthYear] = 0;
          }
          categorySpend[categoryKey].monthlyTrend[monthYear] += amount;
        }
      }

      // Convert to array and sort by total amount
      const categoryAnalysis = Object.values(categorySpend)
        .sort((a, b) => b.totalAmount - a.totalAmount);

      // Calculate total spend across all categories
      const totalSpend = categoryAnalysis.reduce((sum, category) => sum + category.totalAmount, 0);

      // Calculate percentages of total spend
      const categoriesWithPercentage = categoryAnalysis.map(category => ({
        ...category,
        percentageOfTotal: totalSpend > 0 ? (category.totalAmount / totalSpend) * 100 : 0,
      }));

      // Prepare the result
      return {
        categories: categoriesWithPercentage,
        totalCategories: categoryAnalysis.length,
        totalSpend,
        timeframe: {
          startDate: startDate ? new Date(startDate).toISOString() : null,
          endDate: endDate ? new Date(endDate).toISOString() : null,
        },
        topCategory: categoriesWithPercentage.length > 0 ? categoriesWithPercentage[0] : null,
      };
    } catch (error) {
      console.error('Error analyzing category breakdown:', error);
      return {
        success: false,
        message: 'Failed to analyze category breakdown. Please try again.',
        error: error instanceof Error ? error.message : String(error),
      };
    }
  },
});
