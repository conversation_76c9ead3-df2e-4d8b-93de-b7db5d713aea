"use client";

import React, { useCallback } from "react";
import { FileUp, Upload } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { FilePreview } from "@/components/dashboard/pages/upload/file-preview";
import { X } from "lucide-react";

interface ProcessingFile {
  file: File;
  previewUrl?: string;
  status: 'pending' | 'processing' | 'completed' | 'error' | 'duplicate';
}

interface FileUploadZoneProps {
  files: ProcessingFile[];
  isProcessing: boolean;
  maxFiles: number;
  onAddFiles: (files: File[]) => void;
  onRemoveFile: (index: number) => void;
}

export function FileUploadZone({ 
  files, 
  isProcessing,
  maxFiles,
  onAddFiles,
  onRemoveFile
}: FileUploadZoneProps) {
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    const fileList = e.dataTransfer.files;
    if (!fileList || fileList.length === 0) return;
    
    // Convert FileList to array of File objects
    const filesArray = Array.from(fileList).slice(0, maxFiles);
    onAddFiles(filesArray);
  }, [maxFiles, onAddFiles]);

  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const fileList = e.target.files;
    if (!fileList || fileList.length === 0) return;
    
    // Convert FileList to array of File objects
    const filesArray = Array.from(fileList).slice(0, maxFiles);
    onAddFiles(filesArray);
  }, [maxFiles, onAddFiles]);

  return (
    <Card
      className={`border-2 border-dashed ${
        files.length > 0
          ? "border-green-500"
          : "border-muted-foreground/25"
      } rounded-lg hover:border-muted-foreground/50 transition-colors`}
      onDragOver={handleDragOver}
      onDrop={handleDrop}
    >
      <CardContent className="flex flex-col items-center justify-center p-6 text-center min-h-[300px]">
        {files.length > 0 ? (
          <div className="w-full">
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {files.map((file, index) => (
                <div key={index} className="relative group">
                  <div className="relative border rounded-md overflow-hidden aspect-square">
                    <FilePreview
                      src={file.previewUrl || ""}
                      type={file.file.type}
                    />
                    <button
                      type="button"
                      onClick={() => onRemoveFile(index)}
                      className="absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                      disabled={isProcessing}
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                  <p className="mt-1 text-xs text-muted-foreground truncate">
                    {file.file.name}
                  </p>
                </div>
              ))}
              {files.length < maxFiles && (
                <div 
                  className="border-2 border-dashed border-muted-foreground/25 rounded-md flex items-center justify-center aspect-square cursor-pointer hover:border-muted-foreground/50 transition-colors"
                  onClick={() => document.getElementById("file-upload")?.click()}
                >
                  <div className="text-center p-4">
                    <FileUp className="mx-auto h-8 w-8 text-muted-foreground" />
                    <p className="mt-2 text-xs text-muted-foreground">Add more</p>
                  </div>
                </div>
              )}
            </div>
            <input
              id="file-upload"
              type="file"
              className="hidden"
              multiple
              accept="image/jpeg,image/png,image/webp,application/pdf"
              onChange={handleFileInputChange}
            />
          </div>
        ) : (
          <div className="py-12">
            <div className="mb-4 flex justify-center">
              <Upload className="h-12 w-12 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-semibold">
              Drag and drop your invoices here
            </h3>
            <p className="mt-2 text-sm text-muted-foreground">
              Supports JPG, PNG, WEBP and PDF files up to 10MB
            </p>
            <p className="text-sm text-muted-foreground">
              Upload up to {maxFiles} invoices at once
            </p>
            <div className="mt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() =>
                  document.getElementById("file-upload")?.click()
                }
              >
                <FileUp className="mr-2 h-4 w-4" />
                Select Files
              </Button>
              <input
                id="file-upload"
                type="file"
                className="hidden"
                multiple
                accept="image/jpeg,image/png,image/webp,application/pdf"
                onChange={handleFileInputChange}
              />
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
} 