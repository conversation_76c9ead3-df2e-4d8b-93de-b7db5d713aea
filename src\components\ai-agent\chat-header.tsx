'use client';

import { useRouter } from 'next/navigation';
import { useWindowSize } from 'usehooks-ts';
import { motion } from 'motion/react';

import { Button } from '@/components/ui/button';
import { PlusIcon } from './icons';
import { useSidebar } from '@/components/ui/sidebar';
import { memo } from 'react';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider,
} from '@/components/ui/tooltip';
import {
  VisibilityType,
  VisibilitySelector,
} from './visibility-selector';
import { SidebarToggle } from './sidebar-toggle';
import { ModelSelector } from './model-selector';
import { PhoneCall, Sparkles } from 'lucide-react';
import Colors from '../theme/Colors';

function PureChatHeader({
  chatId,
  selectedModelId,
  selectedVisibilityType,
  isReadonly,
}: {
  chatId: string;
  selectedModelId: string;
  selectedVisibilityType: VisibilityType;
  isReadonly: boolean;
}) {
  const router = useRouter();
  const { open } = useSidebar();

  const { width: windowWidth } = useWindowSize();

  return (
    <TooltipProvider>
      <motion.header
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="flex sticky top-0 bg-gradient-to-r from-background/95 via-background/90 to-background/95 backdrop-blur-lg py-3 items-center px-4 md:px-6 gap-3 border-b border-border/30 z-10 shadow-sm"
      >
        <SidebarToggle />

        <div className="flex items-center gap-2 md:gap-3">
          {(!open || windowWidth < 768) && (
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  className="order-2 md:order-1 md:px-3 px-3 h-9 ml-auto md:ml-0 rounded-full border-border/60 hover:border-primary/40 hover:bg-background/80 transition-all duration-200"
                  onClick={() => {
                    router.push('/dashboard/chat');
                    router.refresh();
                  }}
                >
                  <PlusIcon />
                  <span className="md:inline hidden text-sm">
                    New Chat
                  </span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>New Chat</TooltipContent>
            </Tooltip>
          )}

          {!isReadonly && (
            <ModelSelector
              selectedModelId={selectedModelId}
              className="order-1 md:order-2"
            />
          )}

          {!isReadonly && (
            <VisibilitySelector
              chatId={chatId}
              selectedVisibilityType={selectedVisibilityType}
              className="order-1 md:order-3"
            />
          )}
        </div>

        <div className="flex items-center ml-auto gap-2">
          <div className="hidden md:block text-sm font-medium text-primary/80 mr-2">
            <span className="flex items-center">
              <Sparkles
                size={14}
                className="mr-1.5 text-primary animate-pulse"
              />
              Billix Agent
            </span>
          </div>

          <Button className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white flex md:flex py-1.5 px-4 h-9 order-4 rounded-full shadow-md hover:shadow-lg transition-all duration-200">
            <PhoneCall size={16} className="mr-2" />
            Voice Call
          </Button>
        </div>
      </motion.header>
    </TooltipProvider>
  );
}

export const ChatHeader = memo(
  PureChatHeader,
  (prevProps, nextProps) => {
    return prevProps.selectedModelId === nextProps.selectedModelId;
  }
);
