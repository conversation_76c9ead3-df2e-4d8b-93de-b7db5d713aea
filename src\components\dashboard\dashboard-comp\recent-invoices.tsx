import { useEffect, useState } from "react";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";

interface Invoice {
  id: string;
  vendorName: string;
  amount: number;
  status: string;
  createdAt: string;
}

export function RecentInvoices() {
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    setLoading(true);
    fetch("/api/invoices/stats")
      .then(async (res) => {
        if (!res.ok) throw new Error("Failed to fetch invoices");
        const apiData = await res.json();
        setInvoices(apiData.recentInvoices || []);
        setError(null);
      })
      .catch((err) => setError(err.message))
      .finally(() => setLoading(false));
  }, []);

  if (loading) return <div className="text-center py-8">Loading invoices...</div>;
  if (error) return <div className="text-center text-red-500 py-8">{error}</div>;

  return (
    <div className="space-y-5">
      <table className="w-full">
        <thead>
          <tr className="text-left text-sm border-b border-[rgba(255,255,255,0.08)]">
            <th className="font-normal text-gray-400 pb-3">Name</th>
            <th className="font-normal text-gray-400 pb-3">Date</th>
            <th className="font-normal text-gray-400 pb-3 text-right">Amount</th>
            <th className="font-normal text-gray-400 pb-3 text-right">Status</th>
          </tr>
        </thead>
        <tbody>
          {invoices.map((invoice) => (
            <tr
              key={invoice.id}
              className="border-b border-[rgba(255,255,255,0.04)] hover:bg-[#141f41]/30"
            >
              <td className="py-3">
                <div className="flex items-center gap-3">
                  <Avatar className="h-10 w-10 rounded-full bg-[#15314D] text-white">
                    <AvatarFallback className="font-medium bg-[#15314D] text-white">
                      {invoice.vendorName?.[0] || "?"}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium">{invoice.vendorName || "Unknown"}</p>
                    <p className="text-sm text-gray-400">{invoice.id}</p>
                  </div>
                </div>
              </td>
              <td className="py-3 text-sm text-gray-300">
                {new Date(invoice.createdAt).toLocaleDateString('en-US', { 
                  month: 'short', 
                  day: 'numeric', 
                  year: 'numeric' 
                })}
              </td>
              <td className="py-3 text-right">
                <p className="font-medium">
                  $
                  {(invoice.amount ?? 0).toLocaleString(undefined, {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                  })}
                </p>
              </td>
              <td className="py-3 text-right">
                <StatusBadge status={invoice.status} />
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}

function StatusBadge({ status }: { status: string }) {
  const statusColors = {
    PAID: {
      bg: "bg-[#14434B]",
      text: "text-[#00FF7E]"
    },
    PENDING: {
      bg: "bg-[#323036]",
      text: "text-[#FFB909]"
    },
    OVERDUE: {
      bg: "bg-[#34283C]",
      text: "text-[#DE3D3C]"
    }
  };

  const labels = {
    PAID: "Paid",
    PENDING: "Pending",
    OVERDUE: "Overdue",
  };

  const label = labels[status as keyof typeof labels] || status;
  const color = statusColors[status as keyof typeof statusColors] || { bg: "bg-gray-400", text: "text-white" };

  return (
    <div className={`inline-block px-3 py-1 rounded-md ${color.bg} ${color.text} text-sm font-medium`}>
      {label}
    </div>
  );
}
