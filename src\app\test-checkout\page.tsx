'use client';

import React, { useState } from 'react';
import { useUser } from '@clerk/nextjs';
import { CheckoutModal } from '@/components/checkout';

export default function TestCheckoutPage() {
  const [showCheckoutModal, setShowCheckoutModal] = useState(false);
  const { user } = useUser();

  const testPriceId = 'pri_01jz83q8503fhrqh3gwsqa23zb'; // Business monthly plan

  return (
    <div className="min-h-screen bg-black text-white flex flex-col items-center justify-center p-8">
      <h1 className="text-3xl font-bold mb-8">Test Checkout Modal</h1>
      
      <button
        onClick={() => setShowCheckoutModal(true)}
        className="bg-white text-black px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
      >
        Open Checkout Modal
      </button>

      <div className="mt-8 text-sm text-gray-400">
        <p>User: {user?.emailAddresses[0]?.emailAddress || 'Not signed in'}</p>
        <p>Price ID: {testPriceId}</p>
      </div>

      <CheckoutModal
        isOpen={showCheckoutModal}
        onClose={() => setShowCheckoutModal(false)}
        priceId={testPriceId}
        userEmail={user?.emailAddresses[0]?.emailAddress}
        returnUrl="/dashboard"
      />
    </div>
  );
}
