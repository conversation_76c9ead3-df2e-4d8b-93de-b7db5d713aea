import { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { FileText, DollarSign, AlertCircle, TrendingUp } from "lucide-react";

interface InvoiceStatsData {
  totalCount: number;
  totalAmount: number;
  paidCount: number;
  paidAmount: number;
  pendingCount: number;
  pendingAmount: number;
  overdueCount: number;
  overdueAmount: number;
  processingEfficiency: number;
  efficiencyChange: number;
  pendingInvoices: number;
}

export function InvoiceStats() {
  const [stats, setStats] = useState<InvoiceStatsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    setLoading(true);
    fetch("/api/invoices/stats")
      .then(async (res) => {
        if (!res.ok) throw new Error("Failed to fetch stats");
        const data = await res.json();
        console.log(data.summary,"data>>>>>>>>>>>>>>>")
        setStats({
          totalCount: data.summary?.total?.count ?? 0,
          totalAmount: data.summary?.total?.amount ?? 0,
          paidCount: data.summary?.paid?.count ?? 0,
          paidAmount: data.summary?.paid?.amount ?? 0,
          pendingCount: data.summary?.pending?.count ?? 0,
          pendingAmount: data.summary?.pending?.amount ?? 0,
          overdueCount: data.summary?.overdue?.count ?? 0,
          overdueAmount: data.summary?.overdue?.amount ?? 0,
          processingEfficiency: data.summary?.paidPercentage ?? 0,
          efficiencyChange: 0, // You can calculate this if you want
          pendingInvoices: data.summary?.pending?.count ?? 0,
        });
        setError(null);
      })
      .catch((err) => setError(err.message))
      .finally(() => setLoading(false));
  }, []);

  if (loading) {
    return <div className="text-center py-8">Loading stats...</div>;
  }
  if (error) {
    return <div className="text-center text-red-500 py-8">{error}</div>;
  }
  if (!stats) return null;

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card figmaBlue className="rounded-[24px]">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 border-b border-[#0097B1]/20">
          <CardTitle className="text-sm font-medium">Total Invoices</CardTitle>
          <div className="rounded-full p-2 text-white">
            <FileText className="h-4 w-4" />
          </div>
        </CardHeader>
        <CardContent className="pt-4">
          <div className="text-4xl font-bold">{stats.totalCount}</div>
          <div className="flex items-center pt-1">
            {/* You can add a percentage change if available */}
          </div>
        </CardContent>
      </Card>

      <Card figma className="rounded-[24px]">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 border-b border-[#0097B1]/20">
          <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
          <div className="rounded-full p-2 text-white">
            <DollarSign className="h-4 w-4" />
          </div>
        </CardHeader>
        <CardContent className="pt-4">
          <div className="text-4xl font-bold">${(stats.totalAmount ?? 0).toLocaleString()}</div>
          <div className="flex items-center pt-1">
            {/* You can add a percentage change if available */}
          </div>
        </CardContent>
      </Card>

      <Card figmaBlue className="rounded-[24px]">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 border-b border-[#0097B1]/20">
          <CardTitle className="text-sm font-medium">Pending Payments</CardTitle>
          <div className="rounded-full p-2 text-white">
            <AlertCircle className="h-4 w-4" />
          </div>
        </CardHeader>
        <CardContent className="pt-4">
          <div className="text-4xl font-bold">${(stats.pendingAmount ?? 0).toLocaleString()}</div>
          <div className="flex items-center pt-1">
            <span className="text-xs text-gray-400">
              {stats.pendingInvoices ?? 0} invoices awaiting payment
            </span>
          </div>
        </CardContent>
      </Card>

      <Card figmaPurple className="rounded-[24px]">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 border-b border-[#4000FF]/20">
          <CardTitle className="text-sm font-medium">Processing Efficiency</CardTitle>
          <div className="rounded-full p-2 text-white">
            <TrendingUp className="h-4 w-4" />
          </div>
        </CardHeader>
        <CardContent className="pt-4">
          <div className="text-4xl font-bold">{stats.processingEfficiency}%</div>
          <div className="flex items-center pt-1">
            <span className="text-xs font-medium text-[#00b2ff]">
              {stats.efficiencyChange > 0 ? `+${stats.efficiencyChange}%` : `${stats.efficiencyChange}%`}
            </span>
            <span className="ml-1 text-xs text-gray-400">from last month</span>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
