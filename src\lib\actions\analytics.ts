"use server";

import { auth } from "@clerk/nextjs/server";
import db from "@/db/db";
import { InvoiceStatus } from "@prisma/client";
import { format } from "date-fns";
import { revalidatePath } from "next/cache";
import type {
  FinancialMetrics,
  RevenueAnalysis,
  CashFlowAnalysis,
  AccountsAnalysis,
  MonthlyData,
  CustomerAnalysis,
  AgingData
} from "@/types/analytics";

type FilterValues = {
  vendor?: string;
  category?: string;
  status?: string;
  search?: string;
  amountRange?: [number, number];
};



// Helper function to get month-year string
function getMonthYear(date: Date): string {
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
}

// Helper function to calculate aging buckets
function calculateAging(dueDate: Date, currentDate: Date = new Date()): string {
  const daysDiff = Math.floor((currentDate.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24));

  if (daysDiff <= 0) return 'Current';
  if (daysDiff <= 30) return '1-30 days';
  if (daysDiff <= 60) return '31-60 days';
  if (daysDiff <= 90) return '61-90 days';
  return '90+ days';
}

// Get vendors for filter dropdown
export async function getVendors() {
  try {
    const { userId } = await auth();

    if (!userId) {
      throw new Error("Unauthorized");
    }

    // Get the database user ID from Clerk ID
    const user = await db.user.findUnique({
      where: { clerkId: userId },
      select: { id: true },
    });

    if (!user) {
      throw new Error("User not found");
    }

    // Get all vendors
    const vendors = await db.vendor.findMany({
      where: {
        userId: user.id,
      },
      select: {
        id: true,
        name: true,
      },
      orderBy: {
        name: "asc",
      },
    });

    return vendors;
  } catch (error) {
    console.error("Error fetching vendors:", error);
    throw error;
  }
}

// Get categories for filter dropdown
export async function getCategories() {
  try {
    const { userId } = await auth();

    if (!userId) {
      throw new Error("Unauthorized");
    }

    // Get the database user ID from Clerk ID
    const user = await db.user.findUnique({
      where: { clerkId: userId },
      select: { id: true },
    });

    if (!user) {
      throw new Error("User not found");
    }

    // Get all categories
    const categories = await db.category.findMany({
      where: {
        userId: user.id,
      },
      select: {
        id: true,
        name: true,
      },
      orderBy: {
        name: "asc",
      },
    });

    return categories;
  } catch (error) {
    console.error("Error fetching categories:", error);
    throw error;
  }
}

// Helper function to build filter conditions
function buildFilterConditions(filters: FilterValues, dateRange?: { from: Date; to: Date }) {
  const conditions: Record<string, unknown> = {};

  if (filters.vendor && filters.vendor !== "all") {
    conditions.vendorId = filters.vendor;
  }

  if (filters.category && filters.category !== "all") {
    conditions.categoryId = filters.category;
  }

  if (filters.status && filters.status !== "all") {
    conditions.status = filters.status;
  }

  if (filters.search) {
    conditions.OR = [
      { invoiceNumber: { contains: filters.search, mode: "insensitive" } },
      { vendorName: { contains: filters.search, mode: "insensitive" } },
      { description: { contains: filters.search, mode: "insensitive" } },
      { vendor: { name: { contains: filters.search, mode: "insensitive" } } },
    ];
  }

  if (filters.amountRange && filters.amountRange.length === 2) {
    conditions.amount = {
      gte: filters.amountRange[0],
      lte: filters.amountRange[1],
    };
  }

  // Add date range filter based on database timestamps (createdAt/updatedAt)
  // instead of issueDate to ensure we're showing current data
  if (dateRange) {
    conditions.createdAt = {
      gte: dateRange.from,
      lte: dateRange.to,
    };
  }

  return conditions;
}

// Comprehensive Financial metrics with real data calculations
export async function getFinancialMetrics(
  startDate: Date,
  endDate: Date,
  filters: FilterValues
): Promise<FinancialMetrics> {
  try {
    const { userId } = await auth();

    if (!userId) {
      throw new Error("Unauthorized");
    }

    // Get the database user ID from Clerk ID
    const user = await db.user.findUnique({
      where: { clerkId: userId },
      select: { id: true },
    });

    if (!user) {
      throw new Error("User not found");
    }

    // Build filter conditions
    const filterConditions = buildFilterConditions(filters, { from: startDate, to: endDate });

    // Get all invoices with comprehensive data
    const invoices = await db.invoice.findMany({
      where: {
        userId: user.id,
        ...filterConditions,
      },
      select: {
        id: true,
        amount: true,
        status: true,
        invoiceType: true,
        vendorId: true,
        vendorName: true,
        createdAt: true,
        updatedAt: true,
        issueDate: true,
        dueDate: true,
        paidDate: true,
        currency: true,
        vendor: {
          select: {
            id: true,
            name: true,
          }
        }
      }
    });

    // Debug: Log invoice types to understand what we have
    console.log("Invoice types found:", [...new Set(invoices.map(inv => inv.invoiceType))]);
    console.log("Total invoices:", invoices.length);
    console.log("Invoices with amounts:", invoices.filter(inv => inv.amount !== null).length);

    // Separate invoices by type for proper financial accounting
    // Handle different invoice type values: PAYMENT/SALE/SALES = Revenue, PURCHASE = Expenses
    const salesInvoices = invoices.filter(inv =>
      (inv.invoiceType === 'PAYMENT' || inv.invoiceType === 'SALE' || inv.invoiceType === 'SALES') && inv.amount !== null
    );
    const purchaseInvoices = invoices.filter(inv =>
      inv.invoiceType === 'PURCHASE' && inv.amount !== null
    );
    const validInvoices = invoices.filter(invoice => invoice.amount !== null);

    console.log("Sales invoices:", salesInvoices.length);
    console.log("Purchase invoices:", purchaseInvoices.length);
    console.log("Valid invoices:", validInvoices.length);

    // Core Financial Calculations
    const totalRevenue = salesInvoices.reduce((sum, inv) => sum + (inv.amount || 0), 0);
    const totalExpenses = purchaseInvoices.reduce((sum, inv) => sum + (inv.amount || 0), 0);
    const netProfitLoss = totalRevenue - totalExpenses;
    const grossProfit = totalRevenue * 0.7; // Simplified: assume 30% COGS
    const grossMargin = totalRevenue > 0 ? (grossProfit / totalRevenue) * 100 : 0;

    // Accounts Receivable (unpaid sales invoices)
    const unpaidSalesInvoices = salesInvoices.filter(inv => inv.status !== InvoiceStatus.PAID);
    const accountsReceivable = unpaidSalesInvoices.reduce((sum, inv) => sum + (inv.amount || 0), 0);

    // Accounts Payable (unpaid purchase invoices)
    const unpaidPurchaseInvoices = purchaseInvoices.filter(inv => inv.status !== InvoiceStatus.PAID);
    const accountsPayable = unpaidPurchaseInvoices.reduce((sum, inv) => sum + (inv.amount || 0), 0);

    // Cash Flow Analysis
    const paidSalesInvoices = salesInvoices.filter(inv => inv.status === InvoiceStatus.PAID);
    const paidPurchaseInvoices = purchaseInvoices.filter(inv => inv.status === InvoiceStatus.PAID);
    const cashInflow = paidSalesInvoices.reduce((sum, inv) => sum + (inv.amount || 0), 0);
    const cashOutflow = paidPurchaseInvoices.reduce((sum, inv) => sum + (inv.amount || 0), 0);
    const cashFlow = cashInflow - cashOutflow;
    const operatingCashFlow = cashFlow; // Simplified

    // Tax Calculations (simplified)
    const totalTaxPayable = totalRevenue * 0.2; // 20% tax rate
    const totalTaxClaimable = totalExpenses * 0.2; // 20% claimable on expenses
    const netTaxPosition = totalTaxPayable - totalTaxClaimable;

    // Get unique customers and suppliers
    const customerSet = new Set();
    const supplierSet = new Set();

    salesInvoices.forEach(inv => {
      const identifier = inv.vendorId || inv.vendorName || inv.vendor?.name;
      if (identifier) customerSet.add(identifier);
    });

    purchaseInvoices.forEach(inv => {
      const identifier = inv.vendorId || inv.vendorName || inv.vendor?.name;
      if (identifier) supplierSet.add(identifier);
    });

    const activeCustomers = customerSet.size;
    const activeSuppliers = supplierSet.size;

    // Payment Metrics
    const paidInvoices = validInvoices.filter(inv => inv.status === InvoiceStatus.PAID);
    const overdueInvoices = validInvoices.filter(inv => inv.status === InvoiceStatus.OVERDUE);
    const totalInvoices = invoices.length;

    const paymentRate = totalInvoices > 0 ? (paidInvoices.length / totalInvoices) * 100 : 0;

    // Calculate average days to payment
    let totalDaysToPayment = 0;
    let invoicesWithPaymentDates = 0;

    paidInvoices.forEach(invoice => {
      if (invoice.createdAt && invoice.paidDate) {
        const createdDate = new Date(invoice.createdAt);
        const paidDate = new Date(invoice.paidDate);
        const daysDiff = Math.floor((paidDate.getTime() - createdDate.getTime()) / (1000 * 60 * 60 * 24));

        if (daysDiff >= 0) {
          totalDaysToPayment += daysDiff;
          invoicesWithPaymentDates++;
        }
      }
    });

    const averageDaysToPayment = invoicesWithPaymentDates > 0 ? totalDaysToPayment / invoicesWithPaymentDates : 0;

    // Calculate month-over-month trends
    const prevMonthStart = new Date(startDate);
    prevMonthStart.setMonth(prevMonthStart.getMonth() - 1);
    const prevMonthEnd = new Date(endDate);
    prevMonthEnd.setMonth(prevMonthEnd.getMonth() - 1);

    const prevMonthConditions = buildFilterConditions(filters, { from: prevMonthStart, to: prevMonthEnd });

    const prevMonthInvoices = await db.invoice.findMany({
      where: {
        userId: user.id,
        ...prevMonthConditions,
      },
      select: {
        amount: true,
        invoiceType: true,
      }
    });

    const prevMonthRevenue = prevMonthInvoices
      .filter(inv => (inv.invoiceType === 'PAYMENT' || inv.invoiceType === 'SALE' || inv.invoiceType === 'SALES') && inv.amount !== null)
      .reduce((sum, inv) => sum + (inv.amount || 0), 0);

    const prevMonthExpenses = prevMonthInvoices
      .filter(inv => inv.invoiceType === 'PURCHASE' && inv.amount !== null)
      .reduce((sum, inv) => sum + (inv.amount || 0), 0);

    const prevMonthProfit = prevMonthRevenue - prevMonthExpenses;

    const monthOverMonthRevenue = prevMonthRevenue > 0 ? ((totalRevenue - prevMonthRevenue) / prevMonthRevenue) * 100 : 0;
    const monthOverMonthExpenses = prevMonthExpenses > 0 ? ((totalExpenses - prevMonthExpenses) / prevMonthExpenses) * 100 : 0;
    const monthOverMonthProfit = prevMonthProfit !== 0 ? ((netProfitLoss - prevMonthProfit) / Math.abs(prevMonthProfit)) * 100 : 0;

    // Calculate average invoice values
    const averageInvoiceValue = validInvoices.length > 0 ? validInvoices.reduce((sum, inv) => sum + (inv.amount || 0), 0) / validInvoices.length : 0;
    const averageSaleValue = salesInvoices.length > 0 ? totalRevenue / salesInvoices.length : 0;
    const averagePurchaseValue = purchaseInvoices.length > 0 ? totalExpenses / purchaseInvoices.length : 0;

    return {
      // Core Financial Metrics
      totalRevenue,
      totalExpenses,
      netProfitLoss,
      grossProfit,
      grossMargin,

      // Accounts
      accountsReceivable,
      accountsPayable,

      // Cash Flow
      cashFlow,
      operatingCashFlow,

      // Tax Information
      totalTaxPayable,
      totalTaxClaimable,
      netTaxPosition,

      // Payment Metrics
      averageDaysToPayment,
      paymentRate,

      // Counts
      totalInvoices,
      salesInvoiceCount: salesInvoices.length,
      purchaseInvoiceCount: purchaseInvoices.length,
      overdueInvoiceCount: overdueInvoices.length,
      paidInvoiceCount: paidInvoices.length,

      // Trends
      monthOverMonthRevenue,
      monthOverMonthExpenses,
      monthOverMonthProfit,

      // Additional Metrics
      averageInvoiceValue,
      averageSaleValue,
      averagePurchaseValue,
      activeCustomers,
      activeSuppliers,
    };
  } catch (error) {
    console.error("Error fetching financial metrics:", error);
    throw error;
  }
}

// Expense trends
export async function getExpenseTrends(
  startDate: Date,
  endDate: Date,
  filters: FilterValues
) {
  try {
    const { userId } = await auth();

    if (!userId) {
      throw new Error("Unauthorized");
    }

    // Get the database user ID from Clerk ID
    const user = await db.user.findUnique({
      where: { clerkId: userId },
      select: { id: true },
    });

    if (!user) {
      throw new Error("User not found");
    }

    // Build filter conditions with date range
    const filterConditions = buildFilterConditions(filters, { from: startDate, to: endDate });

    // Get all invoices with date filtering
    const invoices = await db.invoice.findMany({
      where: {
        userId: user.id,
        ...filterConditions,
      },
      select: {
        amount: true,
        createdAt: true,
        lineItems: true,
      },
    });

    // We'll use real data instead of sample data

    // If we have real invoice data, extract real trends
    if (invoices.length > 0) {
      // Define type for invoice
      type InvoiceWithLineItems = typeof invoices[0];

      // Group invoices by month
      const invoicesByMonth = new Map<string, InvoiceWithLineItems[]>();

      invoices.forEach(invoice => {
        // Ensure we have a valid createdAt timestamp and amount
        if (!invoice.createdAt || invoice.amount === null) return;

        // Use the database timestamp (createdAt) for grouping by month
        const month = format(new Date(invoice.createdAt), "MMM");

        if (!invoicesByMonth.has(month)) {
          invoicesByMonth.set(month, []);
        }

        invoicesByMonth.get(month)?.push(invoice);
      });

      // If we have data for at least one month, create real trends
      if (invoicesByMonth.size > 0) {
        return Array.from(invoicesByMonth.entries()).map(([month, monthInvoices]) => {
          // Calculate total sales for the month
          const netSales = monthInvoices.reduce(
            (sum: number, invoice: InvoiceWithLineItems) => sum + (invoice.amount || 0),
            0
          );

          // Calculate cost (simplified: 60% of sales)
          const totalCost = netSales * 0.6;

          // Calculate gross profit
          const grossProfit = netSales - totalCost;

          // Calculate gross margin (as percentage)
          const grossMargin = netSales > 0 ? (grossProfit / netSales) * 100 : 0;

          return {
            name: month,
            netSales,
            grossProfit,
            grossMargin,
          };
        }).sort((a, b) => {
          // Sort months in calendar order
          const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
          return months.indexOf(a.name) - months.indexOf(b.name);
        });
      }
    }

    // Return empty data array if no real data is available
    return [
      { name: "Jan", netSales: 0, grossProfit: 0, grossMargin: 0 },
      { name: "Feb", netSales: 0, grossProfit: 0, grossMargin: 0 },
      { name: "Mar", netSales: 0, grossProfit: 0, grossMargin: 0 },
      { name: "Apr", netSales: 0, grossProfit: 0, grossMargin: 0 },
      { name: "May", netSales: 0, grossProfit: 0, grossMargin: 0 },
      { name: "Jun", netSales: 0, grossProfit: 0, grossMargin: 0 },
    ];
  } catch (error) {
    console.error("Error fetching expense trends:", error);
    throw error;
  }
}

// Vendor analysis
export async function getVendorAnalysis(
  startDate: Date,
  endDate: Date,
  filters: FilterValues
) {
  try {
    const { userId } = await auth();

    if (!userId) {
      throw new Error("Unauthorized");
    }

    // Get the database user ID from Clerk ID
    const user = await db.user.findUnique({
      where: { clerkId: userId },
      select: { id: true },
    });

    if (!user) {
      throw new Error("User not found");
    }

    // Build filter conditions with date range
    const filterConditions = buildFilterConditions(filters, { from: startDate, to: endDate });

    // Get invoices with filtering
    const invoices = await db.invoice.findMany({
      where: {
        userId: user.id,
        ...filterConditions,
      },
      select: {
        id: true,
        amount: true,
        status: true,
        createdAt: true,
        vendor: {
          select: {
            id: true,
            name: true,
          }
        },
        vendorId: true,
        vendorName: true,
      }
      // No limit - get all invoices
    });

    // Create a vendor map for all invoices including those without vendor relationship
    const vendorMap = new Map();

    invoices.forEach(invoice => {
      const vendorId = invoice.vendor?.id || invoice.vendorId || invoice.vendorName || 'Unknown Vendor';
      const vendorName = invoice.vendor?.name || invoice.vendorName || 'Unknown Vendor';

      if (!vendorMap.has(vendorId)) {
        vendorMap.set(vendorId, {
          id: vendorId,
          name: vendorName,
          totalAmount: 0,
          invoiceCount: 0,
        });
      }

      const vendor = vendorMap.get(vendorId);
      vendor.totalAmount += invoice.amount || 0;
      vendor.invoiceCount += 1;
    });

    // Convert map to array
    const vendorData = Array.from(vendorMap.values());

    // Sort vendors by total amount
    vendorData.sort((a, b) => b.totalAmount - a.totalAmount);

    // Calculate total spend
    const totalSpend = vendorData.reduce(
      (sum, vendor) => sum + vendor.totalAmount,
      0
    );

    // Get all vendors, not just top 5
    const topVendors = vendorData;

    // Format for chart display
    const vendorAnalysis = topVendors.map((vendor, index) => {
      const percentage = totalSpend > 0 ? (vendor.totalAmount / totalSpend) * 100 : 0;
      // Professional color palette for vendors
      const colors = [
        "#3366CC", // Blue
        "#00A3A3", // Teal
        "#6666FF", // Indigo
        "#33CC99", // Green
        "#FF6666", // Red
        "#FF9933", // Orange
        "#9966CC", // Purple
        "#3399FF", // Light Blue
        "#FF6699", // Pink
        "#00CC99", // Mint
        "#FF9966", // Coral
        "#6699CC", // Steel Blue
        "#CC99FF", // Lavender
        "#33CCCC", // Turquoise
        "#FF6633", // Burnt Orange
      ];

      return {
        id: vendor.id,
        name: vendor.name,
        value: vendor.totalAmount,
        percentage: Number.parseFloat(percentage.toFixed(1)),
        color: colors[index % colors.length],
      };
    });

    // Format all invoices as payment history (no limit)
    const paymentHistory = invoices
      // Don't filter out null amounts
      //.filter(invoice => invoice.amount !== null && invoice.amount > 0)
      .sort((a, b) => {
        if (!a.createdAt) return 1;
        if (!b.createdAt) return -1;
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      })
      .map(invoice => {
        return {
          id: invoice.id,
          vendor: invoice.vendor?.name || invoice.vendorName || 'Unknown Vendor',
          date: invoice.createdAt ? invoice.createdAt.toISOString() : new Date().toISOString(),
          amount: invoice.amount || 0,
          status: invoice.status,
        };
      });

    console.log(`DEBUG - Payment history entries: ${paymentHistory.length}`);

    return {
      vendorAnalysis,
      paymentHistory,
    };
  } catch (error) {
    console.error("Error fetching vendor analysis:", error);
    throw error;
  }
}

// Category breakdown
export async function getCategoryBreakdown(
  startDate: Date,
  endDate: Date,
  filters: FilterValues
) {
  try {
    const { userId } = await auth();

    if (!userId) {
      throw new Error("Unauthorized");
    }

    // Get the database user ID from Clerk ID
    const user = await db.user.findUnique({
      where: { clerkId: userId },
      select: { id: true },
    });

    if (!user) {
      throw new Error("User not found");
    }

    // Build filter conditions with date range
    const filterConditions = buildFilterConditions(filters, { from: startDate, to: endDate });

    // Get invoices with filtering
    const invoices = await db.invoice.findMany({
      where: {
        userId: user.id,
        ...filterConditions,
      },
      select: {
        amount: true,
        categoryId: true,
        category: {
          select: {
            id: true,
            name: true,
            color: true
          }
        },
        createdAt: true
      }
      // No limit - get all invoices
    });

    // Create a category map from all invoices
    const categoryMap = new Map();

    // Process all invoices and group by category
    invoices.forEach(invoice => {
      // Include invoices even with null amounts for count purposes
      // Use 0 for calculations involving null amounts
      const amount = invoice.amount || 0;

      const categoryId = invoice.category?.id || 'uncategorized';
      const categoryName = invoice.category?.name || 'Uncategorized';

      // Professional color palette for categories
      const categoryColors = [
        "#3366CC", // Blue
        "#00A3A3", // Teal
        "#6666FF", // Indigo
        "#33CC99", // Green
        "#FF6666", // Red
        "#FF9933", // Orange
        "#9966CC", // Purple
        "#3399FF", // Light Blue
        "#FF6699", // Pink
        "#00CC99", // Mint
        "#FF9966", // Coral
        "#6699CC", // Steel Blue
        "#CC99FF", // Lavender
        "#33CCCC", // Turquoise
        "#FF6633", // Burnt Orange
      ];

      // Use category color if available, otherwise assign from our palette
      const categoryColor = invoice.category?.color ||
        (categoryId === 'uncategorized' ? '#94a3b8' : categoryColors[Math.abs(categoryName.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0)) % categoryColors.length]);

      if (!categoryMap.has(categoryId)) {
        categoryMap.set(categoryId, {
          id: categoryId,
          name: categoryName,
          value: 0,
          count: 0,
          color: categoryColor,
        });
      }

      const category = categoryMap.get(categoryId);
      category.value += amount;
      category.count += 1;
    });

    // Convert map to array
    let categoryBreakdown = Array.from(categoryMap.values());

    // Sort categories by spend
    categoryBreakdown.sort((a, b) => b.value - a.value);

    // Calculate total spend
    const totalSpend = categoryBreakdown.reduce(
      (sum, category) => sum + category.value,
      0
    );

    // Calculate percentage for each category
    categoryBreakdown = categoryBreakdown.map(category => {
      const percentage = totalSpend > 0 ? (category.value / totalSpend) * 100 : 0;
      return {
        ...category,
        percentage: Number.parseFloat(percentage.toFixed(1)),
      };
    });

    // If there's no data, add a default uncategorized entry to prevent empty charts
    if (categoryBreakdown.length === 0) {
      categoryBreakdown.push({
        id: 'uncategorized',
        name: 'Uncategorized',
        value: 0,
        count: 0,
        color: '#94a3b8',
        percentage: 0
      });
    }

    return categoryBreakdown;
  } catch (error) {
    console.error("Error fetching category breakdown:", error);
    throw error;
  }
}

// Invoice status analysis
export async function getInvoiceStatusAnalysis(
  startDate: Date,
  endDate: Date,
  filters: FilterValues
) {
  try {
    const { userId } = await auth();                                                

    if (!userId) {
      throw new Error("Unauthorized");
    }

    // Get the database user ID from Clerk ID
    const user = await db.user.findUnique({
      where: { clerkId: userId },
      select: { id: true },
    });

    if (!user) {
      throw new Error("User not found");
    }

    // Build filter conditions with date range
    const filterConditions = buildFilterConditions(filters, { from: startDate, to: endDate });

    // Get invoices with filtering
    const invoices = await db.invoice.findMany({
      where: {
        userId: user.id,
        ...filterConditions,
      },
      select: {
        amount: true,
        status: true,
        dueDate: true,
        createdAt: true
      }
      // No limit - get all invoices
    });

    // Use all invoices instead of filtered
    const allInvoices = invoices;

    // Map status to colors with our professional palette
    const statusColors = {
      PAID: "#33CC99", // Professional green
      PENDING: "#FF9933", // Professional orange
      OVERDUE: "#FF6666", // Professional red
      CANCELLED: "#6699CC", // Professional slate blue
    };

    // Process invoice statuses
    const statusMap = new Map();

    // Initialize with all statuses to ensure they appear even with zero values
    Object.values(InvoiceStatus).forEach(status => {
      statusMap.set(status, {
        name: status,
        value: 0,
        count: 0,
        color: statusColors[status as keyof typeof statusColors],
      });
    });

    // Count invoices by status
    allInvoices.forEach(invoice => {
      // Include invoices with null amounts, just use 0 for calculations
      const amount = invoice.amount || 0;

      const status = invoice.status;
      const statusData = statusMap.get(status);

      if (statusData) {
        statusData.value += amount;
        statusData.count++;
      }
    });

    // Convert to array
    const statusData = Array.from(statusMap.values());

    // Calculate total amount
    const totalAmount = statusData.reduce((sum, data) => sum + data.value, 0);

    // Calculate percentages
    const statusAnalysis = statusData.map((data) => {
      const percentage = totalAmount > 0 ? (data.value / totalAmount) * 100 : 0;
      return {
        ...data,
        percentage: Number.parseFloat(percentage.toFixed(1)),
      };
    });

    // Get aging analysis
    // Get current date for aging calculations
    const now = new Date();

    // Define aging ranges with professional color palette
    const agingRanges = [
      { name: "Current", days: 0, color: "#33CC99" }, // Professional green
      { name: "1-30 days", days: 30, color: "#3399FF" }, // Professional light blue
      { name: "31-60 days", days: 60, color: "#9966CC" }, // Professional purple
      { name: "61-90 days", days: 90, color: "#FF9933" }, // Professional orange
      { name: "90+ days", days: Number.POSITIVE_INFINITY, color: "#FF6666" }, // Professional red
    ];

    // Process invoices for aging
    const agingMap = new Map();

    // Initialize all aging ranges
    agingRanges.forEach(range => {
      agingMap.set(range.name, {
        name: range.name,
        value: 0,
        count: 0,
        color: range.color,
      });
    });

    // Calculate aging for each invoice
    allInvoices.forEach(invoice => {
      // Skip if no due date, but include null amounts as zero
      if (!invoice.dueDate) return;

      const amount = invoice.amount || 0;

      // Calculate days overdue based on the due date
      // This is still valid to use dueDate for aging calculation since it's about payment terms
      const dueDate = new Date(invoice.dueDate);
      const daysOverdue = Math.floor((now.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24));

      // Find the appropriate aging range
      let agingRange = agingRanges[0]; // Default to "Current"

      if (daysOverdue > 0) {
        for (const range of agingRanges) {
          if (daysOverdue <= range.days || range.days === Number.POSITIVE_INFINITY) {
            agingRange = range;
            break;
          }
        }
      }

      // Add to the aging map
      const agingData = agingMap.get(agingRange.name);
      if (agingData) {
        agingData.value += amount;
        agingData.count++;
      }
    });

    // Convert to array
    const agingData = Array.from(agingMap.values());

    return {
      statusAnalysis,
      agingData,
    };
  } catch (error) {
    console.error("Error fetching invoice status analysis:", error);
    throw error;
  }
}

// Get AI insights
export async function getAIInsights(
  startDate: Date,
  endDate: Date,
  filters: FilterValues
) {
  try {
    const { userId } = await auth();

    if (!userId) {
      throw new Error("Unauthorized");
    }

    // Get the database user ID from Clerk ID
    const user = await db.user.findUnique({
      where: { clerkId: userId },
      select: { id: true, firstName: true, lastName: true },
    });

    if (!user) {
      throw new Error("User not found");
    }

    // Get financial metrics
    const metrics = await getFinancialMetrics(startDate, endDate, filters);

    // Get additional data for context
    const [vendorAnalysis, categoryBreakdown, invoiceStatusAnalysis] = await Promise.all([
      getVendorAnalysis(startDate, endDate, filters),
      getCategoryBreakdown(startDate, endDate, filters),
      getInvoiceStatusAnalysis(startDate, endDate, filters),
    ]);

    // Get the most recent 10 invoices to understand recent patterns
    const recentInvoices = await db.invoice.findMany({
      where: { userId: user.id },
      orderBy: { createdAt: 'desc' },
      take: 10,
      select: {
        amount: true,
        status: true,
        vendorName: true,
        category: { select: { name: true } },
        createdAt: true,
      }
    });

    // Check for Anthropic API key
    if (!process.env.ANTHROPIC_API_KEY) {
      console.warn("Anthropic API key not found, returning sample insights");
      return generateSampleInsights({
        totalInvoices: metrics.totalInvoices,
        totalAmount: metrics.totalRevenue + metrics.totalExpenses,
        pendingPayments: metrics.accountsReceivable,
        paidAmount: metrics.totalRevenue,
        overdueAmount: metrics.accountsReceivable,
        cashFlow: metrics.cashFlow
      });
    }

    try {
      // Use Claude Sonnet 4 for AI insights generation
      const { anthropic } = await import('@ai-sdk/anthropic');
      const { generateText } = await import('ai');

      // Prepare enhanced input data for OpenAI with more comprehensive financial data
      const financialContext = {
        user: {
          name: `${user.firstName || 'User'} ${user.lastName || ''}`.trim(),
        },
        period: {
          startDate: startDate.toISOString().split('T')[0],
          endDate: endDate.toISOString().split('T')[0],
          daysInPeriod: Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)),
        },
        financialMetrics: {
          // Basic metrics
          totalInvoices: metrics.totalInvoices,
          totalAmount: metrics.totalRevenue + metrics.totalExpenses,
          averageInvoiceAmount: metrics.averageInvoiceValue,
          pendingPayments: metrics.accountsReceivable,
          activeVendors: metrics.activeSuppliers + metrics.activeCustomers,
          monthOverMonthChange: metrics.monthOverMonthRevenue,

          // Enhanced metrics
          paidAmount: metrics.totalRevenue || 0,
          overdueAmount: metrics.accountsReceivable || 0,
          paymentRate: metrics.paymentRate || 0,
          averageDaysToPay: metrics.averageDaysToPayment || 0,
          cashFlow: metrics.cashFlow || 0,
          paidInvoiceCount: metrics.paidInvoiceCount || 0,
          overdueInvoiceCount: metrics.overdueInvoiceCount || 0,
        },
        topVendors: vendorAnalysis.vendorAnalysis.slice(0, 5).map(v => ({
          name: v.name,
          value: v.value,
          percentage: v.percentage,
        })),
        allVendors: vendorAnalysis.vendorAnalysis.map(v => ({
          name: v.name,
          value: v.value,
          percentage: v.percentage,
        })),
        topCategories: categoryBreakdown.slice(0, 5).map(c => ({
          name: c.name,
          value: c.value,
          percentage: c.percentage,
        })),
        allCategories: categoryBreakdown.map(c => ({
          name: c.name,
          value: c.value,
          percentage: c.percentage,
        })),
        invoiceStatus: {
          statuses: invoiceStatusAnalysis.statusAnalysis.map(s => ({
            name: s.name,
            value: s.value,
            count: s.count,
            percentage: s.percentage,
          })),
          aging: invoiceStatusAnalysis.agingData.map(a => ({
            name: a.name,
            value: a.value,
            count: a.count,
          })),
        },
        recentInvoices: recentInvoices.map(inv => ({
          amount: inv.amount,
          status: inv.status,
          vendor: inv.vendorName,
          category: inv.category?.name || 'Uncategorized',
          date: inv.createdAt?.toISOString().split('T')[0],
          createdAt: inv.createdAt?.toISOString(),
        })),
        paymentHistory: vendorAnalysis.paymentHistory.slice(0, 20),
      };

      // Create prompt for OpenAI with enhanced fraud detection, anomaly detection, and predictive analysis
      const prompt = `
      You are a financial insights AI assistant with expertise in fraud detection, anomaly detection, and predictive financial analysis. Based on the following financial data, generate 6-8 valuable insights.
      For each insight, include a type (trend, alert, tip, saving, anomaly, prediction, fraud), a short title, and a concise, specific description.

      Types of insights:
      - "trend": Observations about spending patterns or changes over time
      - "alert": Important warnings or issues that need attention
      - "saving": Opportunities to save money or optimize spending
      - "tip": Practical advice for financial management
      - "anomaly": Potential suspicious transactions or patterns that might indicate errors
      - "fraud": Potential fraudulent activities or manipulations in invoices
      - "prediction": Future projections and forecasts based on current data

      Make the insights specific to this user's data - mention actual numbers, vendors, categories and patterns!
      Each insight should be actionable and valuable, not generic.

      FRAUD DETECTION GUIDELINES:
      - Identify potential invoice manipulation (unusual amounts, suspicious patterns)
      - Detect potential duplicate payments or invoices
      - Flag suspicious vendor behavior (sudden changes in billing patterns)
      - Identify unusual invoice amounts compared to historical data
      - Detect potential price gouging or inflated charges
      - Look for unusual timing of invoices or payments that might indicate fraud
      - Identify potential shell vendors or suspicious new vendors

      ANOMALY DETECTION GUIDELINES:
      - Look for unusual spending patterns or outliers in the data
      - Identify potential duplicate invoices (similar amounts to the same vendor in a short timeframe)
      - Flag vendors with sudden large increases in invoice amounts
      - Detect unusual payment timing or frequency
      - Identify potential invoice errors (unusual amounts, round numbers, etc.)

      PREDICTIVE ANALYSIS GUIDELINES:
      - Provide 3-6 month cash flow projections based on current trends
      - Forecast expected expenses by category based on historical patterns
      - Predict potential cash flow issues before they occur
      - Estimate future vendor expenses based on historical data
      - Project payment timelines and potential late payments
      - Forecast seasonal spending patterns

      FINANCIAL ANALYSIS GUIDELINES:
      - Analyze cash flow trends and provide forecasting insights
      - Identify opportunities for better payment timing to optimize cash flow
      - Suggest vendor consolidation where appropriate
      - Provide category-specific spending optimization recommendations
      - Analyze payment patterns and suggest improvements

      FINANCIAL DATA:
      ${JSON.stringify(financialContext, null, 2)}

      RESPONSE FORMAT:
      Return a JSON array of insights like this:
      [
        {
          "type": "trend|alert|tip|saving|anomaly|fraud|prediction",
          "title": "Short, specific title",
          "description": "Detailed insight with specifics from the data",
          "severity": "info|warning|critical"
        }
      ]
      `;

      // Call Claude 4 API
      const response = await generateText({
        model: anthropic("claude-sonnet-4-20250514"),
        system: "You are a financial analysis AI focused on giving actionable insights. Always respond with valid JSON format.",
        prompt: prompt,
        temperature: 0.3, // Lower temperature for more consistent financial analysis
        maxTokens: 4000,
      });

      // Parse the response with type safety
      const aiResponse = response.text ?? '';
      let insights: Array<Record<string, unknown>> = [];

      try {
        // Clean the response to ensure it's valid JSON
        const cleanedResponse = aiResponse
          .trim()
          .replace(/```json|```/g, "")
          .trim();

        const parsedResponse = JSON.parse(cleanedResponse);
        insights = Array.isArray(parsedResponse.insights) ? parsedResponse.insights :
                  Array.isArray(parsedResponse) ? parsedResponse : [];
      } catch (error) {
        console.error("Error parsing Claude response:", error);
        return generateSampleInsights({
          totalInvoices: metrics.totalInvoices,
          totalAmount: metrics.totalRevenue + metrics.totalExpenses,
          pendingPayments: metrics.accountsReceivable,
          paidAmount: metrics.totalRevenue,
          overdueAmount: metrics.accountsReceivable,
          cashFlow: metrics.cashFlow
        });
      }

      // Validate and format insights with proper typing, including new types for fraud and prediction
      return insights.map((insight: Record<string, unknown>) => ({
        type: ["trend", "alert", "tip", "saving", "anomaly", "fraud", "prediction"].includes(String(insight.type || '')) ? insight.type : "tip",
        title: String(insight.title || "Financial Insight"),
        description: String(insight.description || "Review your financial data for more insights."),
        severity: ["info", "warning", "critical"].includes(String(insight.severity || '')) ? insight.severity : "info",
      }));

    } catch (claudeError) {
      console.error("Error calling Claude API:", claudeError);
      return generateSampleInsights({
        totalInvoices: metrics.totalInvoices,
        totalAmount: metrics.totalRevenue + metrics.totalExpenses,
        pendingPayments: metrics.accountsReceivable,
        paidAmount: metrics.totalRevenue,
        overdueAmount: metrics.accountsReceivable,
        cashFlow: metrics.cashFlow
      });
    }
  } catch (error) {
    console.error("Error generating AI insights:", error);
    throw error;
  }
}

// Helper function to generate sample insights as a fallback
function generateSampleInsights(metrics: {
  totalInvoices: number;
  totalAmount: number;
  pendingPayments: number;
  paidAmount?: number;
  overdueAmount?: number;
  cashFlow?: number;
}) {
  const currentDate = new Date();
  const threeMonthsLater = new Date();
  threeMonthsLater.setMonth(currentDate.getMonth() + 3);

  return [
    {
      type: "trend",
      title: "Spending Patterns",
      description: `You've processed ${metrics.totalInvoices} invoices totaling ${formatCurrency(metrics.totalAmount)} in this period.`,
      severity: "info",
    },
    {
      type: "alert",
      title: "Pending Payments",
      description: `You have ${formatCurrency(metrics.pendingPayments)} in pending payments that require attention.`,
      severity: "warning",
    },
    {
      type: "tip",
      title: "Invoice Management",
      description: "Categorizing your invoices can help you better understand spending patterns and optimize your budget.",
      severity: "info",
    },
    {
      type: "saving",
      title: "Cost Optimization",
      description: "Regular reviews of recurring expenses can help identify potential savings opportunities.",
      severity: "info",
    },
    {
      type: "anomaly",
      title: "Unusual Transactions",
      description: "Our system detected some unusual transaction patterns. Review your recent invoices for potential issues.",
      severity: "warning",
    },
    {
      type: "fraud",
      title: "Potential Invoice Manipulation",
      description: "We've detected potential duplicate invoices or unusual amounts that may indicate manipulation. Review your recent vendor transactions for irregularities.",
      severity: "critical",
    },
    {
      type: "prediction",
      title: "Cash Flow Forecast",
      description: `Based on current trends, your projected cash flow by ${threeMonthsLater.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })} is estimated to be ${formatCurrency((metrics.cashFlow || 0) * 1.15)}. Plan your expenses accordingly.`,
      severity: "info",
    },
    {
      type: "trend",
      title: "Cash Flow Analysis",
      description: `Your current cash flow is ${formatCurrency(metrics.cashFlow || 0)}. Consider optimizing payment schedules to improve liquidity.`,
      severity: "info",
    }
  ];
}

// Helper function to format currency
function formatCurrency(amount: number): string {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  }).format(amount);
}

export async function createReport(data: {
  name: string;
  description: string;
  type: string;
  dateRange: {
    from: Date;
    to: Date;
  };
  visualizationType: string;
}) {
  try {
    const { userId } = await auth();

    if (!userId) {
      throw new Error("Unauthorized");
    }

    // Get the database user ID from Clerk ID
    const user = await db.user.findUnique({
      where: { clerkId: userId },
      select: { id: true },
    });

    if (!user) {
      throw new Error("User not found");
    }

    await db.report.create({
      data: {
        title: data.name,
        description: data.description,
        reportType: data.type,
        startDate: data.dateRange.from,
        endDate: data.dateRange.to,
        format: "PDF", // Default format
        userId: user.id
      },
    });

    revalidatePath("/analytics");
  } catch (error) {
    console.error("Error creating report:", error);
    throw error;
  }
}

export async function getReports() {
  try {
    const { userId } = await auth();

    if (!userId) {
      throw new Error("Unauthorized");
    }

    // Get the database user ID from Clerk ID
    const user = await db.user.findUnique({
      where: { clerkId: userId },
      select: { id: true },
    });

    if (!user) {
      throw new Error("User not found");
    }

    // Fetch all reports for the current user
    const reports = await db.report.findMany({
      where: {
        userId: user.id,
      },
      orderBy: {
        createdAt: "desc",
      },
      // No limit - get all reports
    });

    return reports;
  } catch (error) {
    console.error("Error fetching reports:", error);
    return [];
  }
}

export async function exportReport(reportId: string, format: "pdf" | "excel") {
  try {
    const { userId } = await auth();

    if (!userId) {
      throw new Error("Unauthorized");
    }

    // Get the database user ID from Clerk ID
    const user = await db.user.findUnique({
      where: { clerkId: userId },
      select: { id: true },
    });

    if (!user) {
      throw new Error("User not found");
    }

    const report = await db.report.findUnique({
      where: {
        id: reportId,
        userId: user.id,
      },
    });

    if (!report) {
      throw new Error("Report not found");
    }

    if (format === "pdf") {
      try {
        // For now, just generate a dummy PDF until we have the real implementation
        const pdfBuffer = Buffer.from(`Report: ${report.title}`);
        const fileName = `${report.title.replace(/ /g, "_")}.pdf`;

        // Convert the buffer to a data URL
        const bufferBase64 = pdfBuffer.toString("base64");
        const downloadUrl = `data:application/pdf;base64,${bufferBase64}`;

        return {
          success: true,
          downloadUrl: downloadUrl,
          fileName: fileName,
        };
      } catch (pdfError) {
        console.error("Error generating PDF:", pdfError);
        return {
          success: false,
          error: "Failed to generate PDF",
        };
      }
    } else if (format === "excel") {
      // Placeholder for Excel export
      return {
        success: false,
        error: "Excel export not implemented yet",
      };
    } else {
      return {
        success: false,
        error: "Invalid format",
      };
    }
  } catch (error) {
    console.error("Error exporting report:", error);
    return {
      success: false,
      error: "Failed to export report",
    };
  }
}

export async function createScheduledReport(data: {
  reportId: string;
  frequency: string;
  emailRecipients: string[];
}) {
  try {
    const { userId } = await auth();

    if (!userId) {
      throw new Error("Unauthorized");
    }

    // Get the database user ID from Clerk ID
    const user = await db.user.findUnique({
      where: { clerkId: userId },
      select: { id: true },
    });

    if (!user) {
      throw new Error("User not found");
    }

    await db.scheduledReport.create({
      data: {
        reportId: data.reportId,
        frequency: data.frequency,
        emailAddresses: data.emailRecipients.join(','),
        nextRunTime: new Date(), // Required field
        userId: user.id
      },
    });

    revalidatePath("/analytics");
  } catch (error) {
    console.error("Error creating scheduled report:", error);
    throw error;
  }
}

// Revenue Analysis
export async function getRevenueAnalysis(
  startDate: Date,
  endDate: Date,
  filters: FilterValues
): Promise<RevenueAnalysis> {
  try {
    const { userId } = await auth();
    if (!userId) throw new Error("Unauthorized");

    const user = await db.user.findUnique({
      where: { clerkId: userId },
      select: { id: true },
    });
    if (!user) throw new Error("User not found");

    const filterConditions = buildFilterConditions(filters, { from: startDate, to: endDate });

    // Get sales invoices (revenue) - handle PAYMENT, SALE, and SALES types
    const salesInvoices = await db.invoice.findMany({
      where: {
        userId: user.id,
        invoiceType: { in: ['PAYMENT', 'SALE', 'SALES'] },
        ...filterConditions,
      },
      select: {
        id: true,
        amount: true,
        status: true,
        createdAt: true,
        vendorName: true,
        vendor: { select: { id: true, name: true } },
        category: { select: { id: true, name: true } },
      }
    });

    const validSalesInvoices = salesInvoices.filter(inv => inv.amount !== null);
    const totalRevenue = validSalesInvoices.reduce((sum, inv) => sum + (inv.amount || 0), 0);

    // Monthly revenue breakdown
    const monthlyRevenueMap = new Map<string, { value: number; count: number }>();
    validSalesInvoices.forEach(invoice => {
      if (invoice.createdAt) {
        const monthYear = getMonthYear(new Date(invoice.createdAt));
        const current = monthlyRevenueMap.get(monthYear) || { value: 0, count: 0 };
        monthlyRevenueMap.set(monthYear, {
          value: current.value + (invoice.amount || 0),
          count: current.count + 1
        });
      }
    });

    const monthlyRevenue: MonthlyData[] = Array.from(monthlyRevenueMap.entries()).map(([monthYear, data]) => {
      const [year, month] = monthYear.split('-');
      return {
        month: format(new Date(parseInt(year), parseInt(month) - 1), 'MMM'),
        year: parseInt(year),
        value: data.value,
        count: data.count
      };
    });

    // Top customers analysis
    const customerMap = new Map<string, { totalRevenue: number; invoiceCount: number; lastInvoiceDate: Date }>();
    validSalesInvoices.forEach(invoice => {
      const customerName = invoice.vendor?.name || invoice.vendorName || 'Unknown Customer';
      const current = customerMap.get(customerName) || { totalRevenue: 0, invoiceCount: 0, lastInvoiceDate: new Date(0) };
      customerMap.set(customerName, {
        totalRevenue: current.totalRevenue + (invoice.amount || 0),
        invoiceCount: current.invoiceCount + 1,
        lastInvoiceDate: invoice.createdAt && new Date(invoice.createdAt) > current.lastInvoiceDate
          ? new Date(invoice.createdAt)
          : current.lastInvoiceDate
      });
    });

    const topCustomers: CustomerAnalysis[] = Array.from(customerMap.entries())
      .map(([name, data]) => ({
        id: name,
        name,
        totalRevenue: data.totalRevenue,
        invoiceCount: data.invoiceCount,
        averageInvoiceValue: data.invoiceCount > 0 ? data.totalRevenue / data.invoiceCount : 0,
        lastInvoiceDate: data.lastInvoiceDate,
        paymentBehavior: 'GOOD' as const, // Simplified
        averagePaymentDays: 30 // Simplified
      }))
      .sort((a, b) => b.totalRevenue - a.totalRevenue)
      .slice(0, 10);

    // Calculate growth rate from monthly data
    let growthRate = 0;
    if (monthlyRevenue.length >= 2) {
      const lastMonth = monthlyRevenue[monthlyRevenue.length - 1];
      const previousMonth = monthlyRevenue[monthlyRevenue.length - 2];
      if (previousMonth.value > 0) {
        growthRate = ((lastMonth.value - previousMonth.value) / previousMonth.value) * 100;
      }
    }

    // Revenue by category (simplified - using vendor names as categories for now)
    const categoryMap = new Map<string, number>();
    validSalesInvoices.forEach(invoice => {
      const category = invoice.category?.name || 'Uncategorized';
      const current = categoryMap.get(category) || 0;
      categoryMap.set(category, current + (invoice.amount || 0));
    });

    const revenueByCategory = Array.from(categoryMap.entries()).map(([name, value]) => ({
      id: name,
      name,
      value,
      percentage: totalRevenue > 0 ? (value / totalRevenue) * 100 : 0,
      count: validSalesInvoices.filter(inv => (inv.category?.name || 'Uncategorized') === name).length,
      color: `hsl(${Math.floor(Math.random() * 360)}, 70%, 50%)`
    }));

    return {
      totalRevenue,
      monthlyRevenue,
      topCustomers,
      revenueByCategory,
      revenueForecast: [], // Will implement with AI in future
      growthRate,
      seasonalTrends: [] // Will implement in future
    };
  } catch (error) {
    console.error("Error fetching revenue analysis:", error);
    throw error;
  }
}

// Cash Flow Analysis
export async function getCashFlowAnalysis(
  startDate: Date,
  endDate: Date,
  filters: FilterValues
): Promise<CashFlowAnalysis> {
  try {
    const { userId } = await auth();
    if (!userId) throw new Error("Unauthorized");

    const user = await db.user.findUnique({
      where: { clerkId: userId },
      select: { id: true },
    });
    if (!user) throw new Error("User not found");

    const filterConditions = buildFilterConditions(filters, { from: startDate, to: endDate });

    // Get all invoices for cash flow analysis
    const invoices = await db.invoice.findMany({
      where: {
        userId: user.id,
        ...filterConditions,
      },
      select: {
        id: true,
        amount: true,
        status: true,
        invoiceType: true,
        createdAt: true,
        paidDate: true,
      }
    });

    const validInvoices = invoices.filter(inv => inv.amount !== null);

    // Separate inflows (sales) and outflows (purchases)
    const salesInvoices = validInvoices.filter(inv =>
      inv.invoiceType === 'PAYMENT' || inv.invoiceType === 'SALE' || inv.invoiceType === 'SALES'
    );
    const purchaseInvoices = validInvoices.filter(inv => inv.invoiceType === 'PURCHASE');

    // Calculate paid amounts for actual cash flow
    const paidSales = salesInvoices.filter(inv => inv.status === InvoiceStatus.PAID);
    const paidPurchases = purchaseInvoices.filter(inv => inv.status === InvoiceStatus.PAID);

    const totalInflows = paidSales.reduce((sum, inv) => sum + (inv.amount || 0), 0);
    const totalOutflows = paidPurchases.reduce((sum, inv) => sum + (inv.amount || 0), 0);
    const netCashFlow = totalInflows - totalOutflows;

    // Monthly cash flow breakdown
    const monthlyInflowsMap = new Map<string, number>();
    const monthlyOutflowsMap = new Map<string, number>();

    paidSales.forEach(invoice => {
      if (invoice.paidDate || invoice.createdAt) {
        const date = invoice.paidDate ? new Date(invoice.paidDate) : new Date(invoice.createdAt!);
        const monthYear = getMonthYear(date);
        const current = monthlyInflowsMap.get(monthYear) || 0;
        monthlyInflowsMap.set(monthYear, current + (invoice.amount || 0));
      }
    });

    paidPurchases.forEach(invoice => {
      if (invoice.paidDate || invoice.createdAt) {
        const date = invoice.paidDate ? new Date(invoice.paidDate) : new Date(invoice.createdAt!);
        const monthYear = getMonthYear(date);
        const current = monthlyOutflowsMap.get(monthYear) || 0;
        monthlyOutflowsMap.set(monthYear, current + (invoice.amount || 0));
      }
    });

    const monthlyInflows: MonthlyData[] = Array.from(monthlyInflowsMap.entries()).map(([monthYear, value]) => {
      const [year, month] = monthYear.split('-');
      return {
        month: format(new Date(parseInt(year), parseInt(month) - 1), 'MMM'),
        year: parseInt(year),
        value
      };
    });

    const monthlyOutflows: MonthlyData[] = Array.from(monthlyOutflowsMap.entries()).map(([monthYear, value]) => {
      const [year, month] = monthYear.split('-');
      return {
        month: format(new Date(parseInt(year), parseInt(month) - 1), 'MMM'),
        year: parseInt(year),
        value
      };
    });

    // Calculate liquidity ratio (simplified)
    const liquidityRatio = totalOutflows > 0 ? totalInflows / totalOutflows : 0;

    // Calculate burn rate (monthly average outflow)
    const monthsInPeriod = Math.max(1, Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24 * 30)));
    const burnRate = totalOutflows / monthsInPeriod;

    return {
      netCashFlow,
      monthlyInflows,
      monthlyOutflows,
      cashFlowForecast: [], // Will implement with AI
      paymentTimingAnalysis: [], // Will implement
      liquidityRatio,
      burnRate
    };
  } catch (error) {
    console.error("Error fetching cash flow analysis:", error);
    throw error;
  }
}

// Accounts Analysis (A/R and A/P)
export async function getAccountsAnalysis(
  startDate: Date,
  endDate: Date,
  filters: FilterValues
): Promise<AccountsAnalysis> {
  try {
    const { userId } = await auth();
    if (!userId) throw new Error("Unauthorized");

    const user = await db.user.findUnique({
      where: { clerkId: userId },
      select: { id: true },
    });
    if (!user) throw new Error("User not found");

    const filterConditions = buildFilterConditions(filters, { from: startDate, to: endDate });

    // Get unpaid invoices for A/R and A/P analysis
    const unpaidInvoices = await db.invoice.findMany({
      where: {
        userId: user.id,
        status: { not: InvoiceStatus.PAID },
        ...filterConditions,
      },
      select: {
        id: true,
        amount: true,
        invoiceType: true,
        dueDate: true,
        createdAt: true,
        vendorName: true,
        vendor: { select: { id: true, name: true } },
      }
    });

    const validUnpaidInvoices = unpaidInvoices.filter(inv => inv.amount !== null);

    // Accounts Receivable (unpaid sales invoices)
    const arInvoices = validUnpaidInvoices.filter(inv =>
      inv.invoiceType === 'PAYMENT' || inv.invoiceType === 'SALE' || inv.invoiceType === 'SALES'
    );
    const totalAR = arInvoices.reduce((sum, inv) => sum + (inv.amount || 0), 0);

    // Accounts Payable (unpaid purchase invoices)
    const apInvoices = validUnpaidInvoices.filter(inv => inv.invoiceType === 'PURCHASE');
    const totalAP = apInvoices.reduce((sum, inv) => sum + (inv.amount || 0), 0);

    // Calculate aging for A/R
    const arAging = new Map<string, { amount: number; count: number }>();
    arInvoices.forEach(invoice => {
      if (invoice.dueDate) {
        const agingBucket = calculateAging(new Date(invoice.dueDate));
        const current = arAging.get(agingBucket) || { amount: 0, count: 0 };
        arAging.set(agingBucket, {
          amount: current.amount + (invoice.amount || 0),
          count: current.count + 1
        });
      }
    });

    // Calculate aging for A/P
    const apAging = new Map<string, { amount: number; count: number }>();
    apInvoices.forEach(invoice => {
      if (invoice.dueDate) {
        const agingBucket = calculateAging(new Date(invoice.dueDate));
        const current = apAging.get(agingBucket) || { amount: 0, count: 0 };
        apAging.set(agingBucket, {
          amount: current.amount + (invoice.amount || 0),
          count: current.count + 1
        });
      }
    });

    const arAgingData: AgingData[] = Array.from(arAging.entries()).map(([range, data]) => ({
      range,
      amount: data.amount,
      count: data.count,
      percentage: totalAR > 0 ? (data.amount / totalAR) * 100 : 0
    }));

    const apAgingData: AgingData[] = Array.from(apAging.entries()).map(([range, data]) => ({
      range,
      amount: data.amount,
      count: data.count,
      percentage: totalAP > 0 ? (data.amount / totalAP) * 100 : 0
    }));

    return {
      accountsReceivable: {
        total: totalAR,
        aging: arAgingData,
        overdueAmount: arInvoices.filter(inv => inv.dueDate && new Date(inv.dueDate) < new Date()).reduce((sum, inv) => sum + (inv.amount || 0), 0),
        averageCollectionDays: 30, // Simplified
        topDebtors: [] // Will implement
      },
      accountsPayable: {
        total: totalAP,
        aging: apAgingData,
        overdueAmount: apInvoices.filter(inv => inv.dueDate && new Date(inv.dueDate) < new Date()).reduce((sum, inv) => sum + (inv.amount || 0), 0),
        averagePaymentDays: 30, // Simplified
        topCreditors: [] // Will implement
      }
    };
  } catch (error) {
    console.error("Error fetching accounts analysis:", error);
    throw error;
  }
}









