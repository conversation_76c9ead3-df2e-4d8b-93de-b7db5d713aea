import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  experimental: {
    serverActions:{
      bodySizeLimit: "10mb",
    }
  },
  transpilePackages: ["rimraf", "@lemonsqueezy/lemonsqueezy.js", "pdfkit", "jpeg-exif", "png-js"],
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "vq0p6ngmuf7yndmj.public.blob.vercel-storage.com",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "images.clerk.dev",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "img.clerk.com",
        port: "",
        pathname: "/**",
      },
    ],
  },  
};

export default nextConfig;
