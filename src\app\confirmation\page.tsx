'use client';

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Loader2, CheckCircle } from "lucide-react";

interface Subscription {
  id: number;
  status: string;
  plan: {
    name: string;
  };
  // Add other fields as needed
}

export default function ConfirmationPage() {
  const [loading, setLoading] = useState(true);
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  useEffect(() => {
    const fetchSubscription = async () => {
      try {
        setError(null);
        const res = await fetch("/api/subscriptions");
        if (!res.ok) {
          throw new Error("Failed to fetch subscription status");
        }
        const data: Subscription[] = await res.json();
        // Find an active subscription
        const active = data.find((sub) => sub.status === "active");
        if (active) {
          setSubscription(active);
          setLoading(false);
        } else {
          setSubscription(null);
          setLoading(true);
        }
      } catch (err: unknown) {
        setError(err instanceof Error ? err.message : "Unknown error");
        setLoading(false);
      }
    };

    // Initial fetch
    fetchSubscription();
    // Poll every 3 seconds
    const interval = setInterval(fetchSubscription, 3000);

    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh]">
        <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
        <h2 className="text-xl font-semibold mb-2">Processing your subscription...</h2>
        <p className="text-muted-foreground mb-4">
          Please wait while we confirm your payment and activate your subscription.
        </p>
        {error && (
          <div className="text-red-500 mb-2">{error}</div>
        )}
      </div>
    );
  }

  if (subscription) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh]">
        <CheckCircle className="h-12 w-12 text-green-500 mb-4" />
        <h2 className="text-2xl font-bold mb-2">Subscription Activated!</h2>
        <p className="text-muted-foreground mb-4">
          Your subscription to the <span className="font-semibold">{subscription.plan.name}</span> plan is now active.
        </p>
        <Button onClick={() => router.push("/dashboard/subscription")}>
          Go to Subscription Dashboard
        </Button>
      </div>
    );
  }

  // If not loading and no subscription found
  return (
    <div className="flex flex-col items-center justify-center min-h-[60vh]">
      <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
      <h2 className="text-xl font-semibold mb-2">Still processing...</h2>
      <p className="text-muted-foreground mb-4">
        We haven&apos;t detected an active subscription yet. Please wait a moment or contact support if this takes too long.
      </p>
      {error && (
        <div className="text-red-500 mb-2">{error}</div>
      )}
    </div>
  );
}
