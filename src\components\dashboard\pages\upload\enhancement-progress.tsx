'use client';

import { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  CheckCircle,
  Clock,
  AlertCircle,
  RefreshCw,
  Shield,
  FileSearch,
  TrendingUp,
  Users,
} from 'lucide-react';

interface EnhancementProgressProps {
  invoiceId: string;
  onComplete?: (results: any) => void;
}

interface EnhancementJob {
  jobId: string;
  invoiceId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  enhancementTypes: string[];
  startedAt: string;
  completedAt?: string;
  error?: string;
  results?: Record<string, any>;
}

const enhancementSteps = [
  {
    key: 'fraud',
    label: 'Fraud Detection',
    description: 'Analyzing for suspicious patterns',
    icon: Shield,
  },
  {
    key: 'related',
    label: 'Document Discovery',
    description: 'Finding related documents',
    icon: FileSearch,
  },
  {
    key: 'payment',
    label: 'Payment Prediction',
    description: 'Predicting payment timing',
    icon: TrendingUp,
  },
  {
    key: 'vendor',
    label: 'Vendor Learning',
    description: 'Updating vendor profile',
    icon: Users,
  },
];

export function EnhancementProgress({
  invoiceId,
  onComplete,
}: EnhancementProgressProps) {
  const [job, setJob] = useState<EnhancementJob | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    let intervalId: NodeJS.Timeout;

    const checkJobStatus = async () => {
      try {
        const response = await fetch(
          `/api/jobs/invoice-enhancement?invoiceId=${invoiceId}`
        );

        if (!response.ok) {
          if (response.status === 404) {
            // No enhancement job found, this is normal for fast uploads
            setLoading(false);
            return;
          }
          throw new Error('Failed to fetch enhancement status');
        }

        const jobData = await response.json();
        setJob(jobData);
        setLoading(false);

        // If job is completed or failed, stop polling
        if (
          jobData.status === 'completed' ||
          jobData.status === 'failed'
        ) {
          if (intervalId) {
            clearInterval(intervalId);
          }

          if (jobData.status === 'completed' && onComplete) {
            onComplete(jobData.results);
          }
        }
      } catch (err) {
        console.error('Error checking enhancement status:', err);
        setError(
          err instanceof Error ? err.message : 'Unknown error'
        );
        setLoading(false);

        if (intervalId) {
          clearInterval(intervalId);
        }
      }
    };

    // Initial check
    checkJobStatus();

    // Poll every 2 seconds for updates
    intervalId = setInterval(checkJobStatus, 2000);

    // Cleanup
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [invoiceId, onComplete]);

  if (loading) {
    return (
      <Card className="w-full">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-sm">
            <RefreshCw className="h-4 w-4 animate-spin" />
            Checking Enhancement Status...
          </CardTitle>
        </CardHeader>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="w-full border-red-200 bg-red-50">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-sm text-red-700">
            <AlertCircle className="h-4 w-4" />
            Enhancement Status Error
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-red-600">{error}</p>
        </CardContent>
      </Card>
    );
  }

  if (!job) {
    return (
      <Card className="w-full border-green-200 bg-green-50">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-sm text-green-700">
            <CheckCircle className="h-4 w-4" />
            Basic Processing Complete
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-green-600">
            Invoice uploaded successfully. Enhanced features will be
            available shortly.
          </p>
        </CardContent>
      </Card>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-500';
      case 'processing':
        return 'bg-blue-500';
      case 'failed':
        return 'bg-red-500';
      default:
        return 'bg-gray-300';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'processing':
        return (
          <RefreshCw className="h-4 w-4 animate-spin text-blue-500" />
        );
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const getProgress = () => {
    if (job.status === 'completed') return 100;
    if (job.status === 'failed') return 0;
    if (job.status === 'processing') return 50;
    return 0;
  };

  const getCompletedSteps = () => {
    if (!job.results) return [];
    return Object.keys(job.results).filter(
      (key) => job.results![key] && !job.results![key].error
    );
  };

  const completedSteps = getCompletedSteps();

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-sm">
            {getStatusIcon(job.status)}
            Enhanced Processing
          </CardTitle>
          <Badge
            variant={
              job.status === 'completed' ? 'default' : 'secondary'
            }
            className={
              job.status === 'completed' ? 'bg-green-500' : ''
            }
          >
            {job.status.charAt(0).toUpperCase() + job.status.slice(1)}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Overall Progress */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Progress</span>
            <span>{getProgress()}%</span>
          </div>
          <Progress value={getProgress()} className="h-2" />
        </div>

        {/* Enhancement Steps */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium">
            Enhancement Features
          </h4>
          {enhancementSteps.map((step) => {
            const isCompleted = completedSteps.includes(step.key);
            const hasError = job.results?.[step.key]?.error;
            const isProcessing =
              job.status === 'processing' &&
              !isCompleted &&
              !hasError;

            return (
              <div
                key={step.key}
                className="flex items-center gap-3 p-2 rounded-lg bg-gray-50"
              >
                <div
                  className={`p-1.5 rounded-full ${
                    isCompleted
                      ? 'bg-green-100'
                      : hasError
                        ? 'bg-red-100'
                        : isProcessing
                          ? 'bg-blue-100'
                          : 'bg-gray-100'
                  }`}
                >
                  {isCompleted ? (
                    <CheckCircle className="h-3 w-3 text-green-600" />
                  ) : hasError ? (
                    <AlertCircle className="h-3 w-3 text-red-600" />
                  ) : isProcessing ? (
                    <RefreshCw className="h-3 w-3 animate-spin text-blue-600" />
                  ) : (
                    <step.icon className="h-3 w-3 text-gray-400" />
                  )}
                </div>

                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900">
                    {step.label}
                  </p>
                  <p className="text-xs text-gray-500 truncate">
                    {hasError
                      ? 'Processing failed'
                      : step.description}
                  </p>
                </div>

                {isCompleted && (
                  <Badge
                    variant="outline"
                    className="text-xs bg-green-50 text-green-700 border-green-200"
                  >
                    Done
                  </Badge>
                )}
                {hasError && (
                  <Badge
                    variant="outline"
                    className="text-xs bg-red-50 text-red-700 border-red-200"
                  >
                    Error
                  </Badge>
                )}
              </div>
            );
          })}
        </div>

        {/* Error Message */}
        {job.status === 'failed' && job.error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-700">{job.error}</p>
          </div>
        )}

        {/* Completion Message */}
        {job.status === 'completed' && (
          <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
            <p className="text-sm text-green-700">
              Enhanced processing completed! Advanced features are now
              available.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
