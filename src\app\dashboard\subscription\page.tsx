import Subscription from '@/components/dashboard/pages/Subscription';
import {
  getUserSubscriptions,
  getAvailablePlans,
} from '@/actions/paddle-subscription-actions';
import { auth } from '@clerk/nextjs/server';
import { redirect } from 'next/navigation';
import { syncUserWithDatabase } from '@/lib/actions/user';
import DashboardLayout from '@/components/layout/DashboardLayout';

interface SubscriptionPageProps {
  searchParams?: Promise<{
    success?: string;
    session_id?: string;
    return_url?: string;
  }>;
}

export default async function SubscriptionPage({
  searchParams,
}: SubscriptionPageProps) {
  // Check authentication
  const session = await auth();
  if (!session.userId) {
    redirect('/login');
  }

  // Ensure user exists in database
  try {
    await syncUserWithDatabase();
  } catch (error) {
    console.error('Error syncing user with database:', error);
  }

  const params = await searchParams;

  try {
    // Fetch user subscriptions server-side (Paddle-only)
    const userSubscriptions = await getUserSubscriptions();

    // Fetch available plans from database
    const plans = await getAvailablePlans();

    // Pass the subscriptions to the client component with the correct prop name
    return (
      <DashboardLayout>
        <Subscription
          userSubscriptions={userSubscriptions}
          plans={plans}
          successParams={
            params?.success === 'true'
              ? {
                  sessionId: params.session_id,
                  returnUrl: params.return_url,
                }
              : undefined
          }
        />
      </DashboardLayout>
    );
  } catch (error) {
    console.error('Error in subscription page:', error);
    // Return with empty arrays so the UI can handle it gracefully
    return (
      <DashboardLayout>
        <Subscription
          userSubscriptions={[]}
          plans={[]}
          successParams={undefined}
        />
      </DashboardLayout>
    );
  }
}
