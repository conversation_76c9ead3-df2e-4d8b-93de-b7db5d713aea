import { tool } from 'ai';
import { z } from 'zod';
import db from '@/db/db';
import { getAuthenticatedUserId } from '@/lib/auth-helpers';

export const computeCashFlow = tool({
  description: 'Analyzes cash inflows (paid invoices) versus outflows (costs, if modeled) over a timeframe, producing a cash-flow statement.',
  parameters: z.object({
    startDate: z.string().describe('Start date for the period to analyze (YYYY-MM-DD)'),
    endDate: z.string().describe('End date for the period to analyze (YYYY-MM-DD)'),
    currency: z.string().optional().describe('Optional currency to filter by (e.g., USD, EUR)'),
    includeEstimatedCosts: z.boolean().default(false).optional().describe('Whether to include estimated costs based on categories'),
    costEstimatePercentage: z.number().min(0).max(100).default(70).optional().describe('Estimated cost percentage of revenue (0-100)'),
  }),
  execute: async ({ startDate, endDate, currency, includeEstimatedCosts = false, costEstimatePercentage = 70 }) => {
    try {
      // Get the authenticated user ID
      const userId = await getAuthenticatedUserId();

      // Build the where clause for the query
      const where: Record<string, unknown> = {
        userId,
        createdAt: {
          gte: new Date(startDate),
          lte: new Date(endDate),
        },
      };

      // Add currency filter if provided
      if (currency) {
        where.currency = currency;
      }

      // Get all invoices for the user with the applied filters
      const invoices = await db.invoice.findMany({
        where,
        select: {
          id: true,
          amount: true,
          currency: true,
          status: true,
          issueDate: true,
          paidDate: true,
          createdAt: true,
          updatedAt: true,
          category: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: {
          createdAt: 'asc',
        },
      });

      // Group invoices by month and status
      const monthlyCashFlow: Record<string, {
        month: string;
        inflow: number;
        outflow: number;
        netCashFlow: number;
        paidInvoices: number;
        pendingInvoices: number;
        overdueInvoices: number;
        currencies: Record<string, { inflow: number; outflow: number }>;
      }> = {};

      // Calculate monthly cash flow
      for (const invoice of invoices) {
        // Use createdAt date instead of issueDate
        if (!invoice.createdAt) continue;

        // Use createdAt for all invoices, regardless of status
        const dateToUse = invoice.createdAt;
        const monthYear = `${dateToUse.getFullYear()}-${String(dateToUse.getMonth() + 1).padStart(2, '0')}`;
        const amount = invoice.amount || 0;
        const currencyKey = invoice.currency || 'UNKNOWN';

        // Create month entry if it doesn't exist
        if (!monthlyCashFlow[monthYear]) {
          monthlyCashFlow[monthYear] = {
            month: monthYear,
            inflow: 0,
            outflow: 0,
            netCashFlow: 0,
            paidInvoices: 0,
            pendingInvoices: 0,
            overdueInvoices: 0,
            currencies: {},
          };
        }

        // Initialize currency entry if it doesn't exist
        if (!monthlyCashFlow[monthYear].currencies[currencyKey]) {
          monthlyCashFlow[monthYear].currencies[currencyKey] = {
            inflow: 0,
            outflow: 0,
          };
        }

        // Update cash flow based on invoice status
        if (invoice.status === 'PAID') {
          monthlyCashFlow[monthYear].inflow += amount;
          monthlyCashFlow[monthYear].currencies[currencyKey].inflow += amount;
          monthlyCashFlow[monthYear].paidInvoices++;
        } else if (invoice.status === 'PENDING') {
          monthlyCashFlow[monthYear].pendingInvoices++;
        } else if (invoice.status === 'OVERDUE') {
          monthlyCashFlow[monthYear].overdueInvoices++;
        }

        // Calculate estimated costs if requested
        if (includeEstimatedCosts && invoice.status === 'PAID') {
          const estimatedCost = amount * (costEstimatePercentage / 100);
          monthlyCashFlow[monthYear].outflow += estimatedCost;
          monthlyCashFlow[monthYear].currencies[currencyKey].outflow += estimatedCost;
        }
      }

      // Calculate net cash flow for each month
      for (const month in monthlyCashFlow) {
        monthlyCashFlow[month].netCashFlow = monthlyCashFlow[month].inflow - monthlyCashFlow[month].outflow;
      }

      // Convert to array and sort by month
      const monthlyData = Object.values(monthlyCashFlow)
        .sort((a, b) => a.month.localeCompare(b.month));

      // Calculate totals
      const totalInflow = monthlyData.reduce((sum, month) => sum + month.inflow, 0);
      const totalOutflow = monthlyData.reduce((sum, month) => sum + month.outflow, 0);
      const netCashFlow = totalInflow - totalOutflow;
      const totalPaidInvoices = monthlyData.reduce((sum, month) => sum + month.paidInvoices, 0);
      const totalPendingInvoices = monthlyData.reduce((sum, month) => sum + month.pendingInvoices, 0);
      const totalOverdueInvoices = monthlyData.reduce((sum, month) => sum + month.overdueInvoices, 0);

      // Prepare the result
      return {
        cashFlowStatement: {
          monthlyData,
          totalInflow,
          totalOutflow,
          netCashFlow,
          totalPaidInvoices,
          totalPendingInvoices,
          totalOverdueInvoices,
          costEstimationMethod: includeEstimatedCosts
            ? `${costEstimatePercentage}% of revenue`
            : 'No cost estimation',
        },
        timeframe: {
          startDate: new Date(startDate).toISOString(),
          endDate: new Date(endDate).toISOString(),
        },
        systemDate: {
          current: new Date().toISOString(),
          currentFormatted: new Date().toISOString().split('T')[0],
          year: new Date().getFullYear(),
          month: new Date().getMonth() + 1, // JavaScript months are 0-indexed
          day: new Date().getDate()
        },
        message: "Note: This cash flow analysis is based on invoice creation dates (when they were added to the system), not the issue dates shown on the invoices."
      };
    } catch (error) {
      console.error('Error computing cash flow:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  },
});
