'use client';

import { motion } from 'motion/react';
import { Button } from '@/components/ui/button';
import { ChatRequestOptions, CreateMessage, Message } from 'ai';
import { memo } from 'react';

interface SuggestedActionsProps {
  chatId: string;
  append: (
    message: Message | CreateMessage,
    chatRequestOptions?: ChatRequestOptions,
  ) => Promise<string | null | undefined>;
}

function PureSuggestedActions({ chatId, append }: SuggestedActionsProps) {
  const suggestedActions = [
    {
      title: 'Generate a report',
      label: 'for this month\'s cash flow',
      action: 'Generate a cash flow report for this month',
    },
    {
      title: 'Analyze my invoices',
      label: 'by category for the last quarter',
      action: 'Analyze my invoice categories for the last quarter and show me the top spending areas',
    },
    {
      title: 'Create an invoice',
      label: 'for my latest client project',
      action: 'Help me create a new invoice for client services',
    },
    {
      title: 'Show me documentation',
      label: 'about Billix AI capabilities',
      action: 'Create document explaining all your capabilities as the Billix AI Agent',
    },
  ];

  return (
    <div className="grid sm:grid-cols-2 gap-2 w-full mt-0 mb-2">
      {suggestedActions.map((suggestedAction, index) => (
        <motion.div
          initial={{ opacity: 0, y: 5 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 5 }}
          transition={{ delay: 0.05 * index }}
          key={`suggested-action-${suggestedAction.title}-${index}`}
          className={index > 1 ? 'hidden sm:block' : 'block'}
        >
          <Button
            variant="ghost"
            onClick={async () => {
              window.history.replaceState({}, '', `/chat/${chatId}`);

              append({
                role: 'user',
                content: suggestedAction.action,
              });
            }}
            className="text-left border border-primary/30 rounded-xl px-3 py-2 text-sm flex-1 gap-1 sm:flex-col w-full h-auto justify-start items-start hover:bg-primary/10 hover:border-primary/50 transition-colors shadow-sm bg-background/90 backdrop-blur-sm"
          >
            <span className="font-medium text-primary">{suggestedAction.title}</span>
            <span className="text-muted-foreground text-xs">
              {suggestedAction.label}
            </span>
          </Button>
        </motion.div>
      ))}
    </div>
  );
}

export const SuggestedActions = memo(PureSuggestedActions, () => true);
