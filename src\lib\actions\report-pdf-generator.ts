import { jsPDF } from "jspdf";
import autoTable from "jspdf-autotable";
import type { Report, ReportData } from "@prisma/client";
// Import Arabic font support
import "jspdf/dist/polyfills.es.js";

interface JsPDFWithAutoTable extends jsPDF {
  lastAutoTable: {
    finalY: number;
  };
}

// Custom interface for report data items
interface ReportDataItem extends Omit<ReportData, 'id' | 'createdAt' | 'reportId' | 'invoiceId'> {
  id?: string;
  reportId?: string;
  invoiceId?: string | null;
  language?: string; // Add language field for RTL support
}

interface ReportWithData extends Report {
  data: ReportDataItem[];
  language?: string; // Add language field for RTL support
}

// Add Arabic font to jsPDF
function addArabicFontSupport(doc: jsPDF): void {
  try {
    // Use the Amiri font from public directory
    const regularFontPath = '/fonts/Amiri-1.001/Amiri-Regular.ttf';
    const boldFontPath = '/fonts/Amiri-1.001/Amiri-Bold.ttf';

    // Add both regular and bold font variants
    doc.addFont(regularFontPath, 'Amiri', 'normal');
    doc.addFont(boldFontPath, 'Amiri', 'bold');

    // Set default font and RTL direction
    doc.setFont('Amiri');
    doc.setR2L(true);
  } catch (error) {
    console.error("Error loading Amiri font:", error);
    // Fallback to courier font if loading fails
    doc.setFont('courier', 'normal');
    doc.setR2L(true);
  }
}

// Helper function to format dates in a clean format (keeping numbers in English)
function formatDate(date: Date | string): string {
  const dateObj = date instanceof Date ? date : new Date(date);

  // Use en-US for number formatting while respecting RTL direction
  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  };

  // Always use en-US for dates to keep numbers in English format
  return dateObj.toLocaleDateString('en-US', options);
}

/**
 * Generates a PDF report based on report data
 * @param report The report with data items
 * @param includeCompanyBranding Whether to include company branding
 * @returns Buffer containing the PDF data
 */
export async function generateReportPdf(
  report: ReportWithData,
  includeCompanyBranding = true
): Promise<Buffer> {
  const doc = new jsPDF();

  // Detect language from report data - if any data item has Arabic language, use Arabic
  const isArabic = report.language === "ar" || report.data.some(item => item.language === "ar");

  // Configure document for the appropriate language
  if (isArabic) {
    addArabicFontSupport(doc);
  } else {
    doc.setFont("helvetica", "normal");
    doc.setR2L(false);
  }

  if (includeCompanyBranding) {
    addBranding(doc);
  }

  doc.setFontSize(20);
  // Use report.title instead of report.name
  doc.text(report.title, doc.internal.pageSize.width / 2, 30, {
    align: "center",
  });

  doc.setFontSize(10);
  doc.text(`Generated on: ${formatDate(new Date())}`, 14, 40);
  doc.text(`Report Type: ${formatReportType(report.reportType)}`, 14, 45);

  // Add date range if available
  if (report.startDate || report.endDate) {
    const startDateStr = report.startDate ? formatDate(report.startDate) : 'All time';
    const endDateStr = report.endDate ? formatDate(report.endDate) : 'Present';
    doc.text(`Date Range: ${startDateStr} to ${endDateStr}`, 14, 50);
  } else {
    doc.text(`Date Range: Based on when invoices were added to the system`, 14, 50);
  }

  let yPos = 55;

  if (report.description) {
    doc.setFontSize(12);
    doc.text("Description:", 14, yPos);
    yPos += 5;

    const splitDescription = doc.splitTextToSize(report.description, 180);
    doc.setFontSize(10);
    doc.text(splitDescription, 14, yPos);
    yPos += splitDescription.length * 5 + 10;
  }

  // Check if report has data
  if (!report.data || report.data.length === 0) {
    // No data available, show a message
    doc.setFontSize(14);
    doc.text("No data available for this report.", 14, yPos);
    yPos += 10;

    doc.setFontSize(12);
    doc.text("Please try again with different parameters or check your data source.", 14, yPos);

    // Add page numbers
    doc.setPage(1);
    doc.setFontSize(10);
    doc.text(
      `Page 1 of 1`,
      doc.internal.pageSize.width / 2,
      doc.internal.pageSize.height - 10,
      { align: "center" }
    );

    // Return buffer instead of doc object
    const pdfBuffer = Buffer.from(doc.output("arraybuffer"));
    return pdfBuffer;
  }

  switch (report.reportType) {
    case "EXPENSES":
      yPos = addExpensesReport(doc, report, yPos, isArabic);
      break;
    case "VENDOR_ANALYSIS":
      yPos = addVendorAnalysisReport(doc, report, yPos, isArabic);
      break;
    case "CATEGORY_ANALYSIS":
    case "CATEGORY_BREAKDOWN":
      yPos = addCategoryAnalysisReport(doc, report, yPos, isArabic);
      break;
    case "CASH_FLOW":
      yPos = addCashFlowReport(doc, report, yPos, isArabic);
      break;
    case "PROFIT_LOSS":
      // Use dedicated profit & loss report
      yPos = addProfitLossReport(doc, report, yPos, isArabic);
      break;
    case "INVOICE_SUMMARY":
      // Use expenses report for invoice summary as they're similar
      yPos = addExpensesReport(doc, report, yPos, isArabic);
      break;
    case "BALANCE_SHEET":
      // Use dedicated balance sheet report
      yPos = addBalanceSheetReport(doc, report, yPos, isArabic);
      break;
    case "TAX":
      // Use dedicated tax report
      yPos = addTaxReport(doc, report, yPos, isArabic);
      break;
    case "SALES":
      // Use dedicated sales report
      yPos = addSalesReport(doc, report, yPos, isArabic);
      break;
    default:
      yPos = addDefaultReport(doc, report, yPos, isArabic);
  }

  const pageCount = doc.internal.pages.length - 1;
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    doc.setFontSize(10);
    doc.text(
      `Page ${i} of ${pageCount}`,
      doc.internal.pageSize.width / 2,
      doc.internal.pageSize.height - 10,
      { align: "center" }
    );
  }

  const pdfBuffer = Buffer.from(doc.output("arraybuffer"));
  return pdfBuffer;
}

/**
 * Adds company branding to the PDF
 */
function addBranding(doc: jsPDF): void {
  doc.setFillColor(60, 90, 153); // Brand color
  doc.rect(14, 10, 30, 10, "F");

  doc.setTextColor(255, 255, 255);
  doc.setFontSize(12);
  doc.text("Billix", 18, 17);

  doc.setTextColor(0, 0, 0);

  doc.setDrawColor(220, 220, 220);
  doc.setLineWidth(0.5);
  doc.line(14, 25, 196, 25);
}

/**
 * Formats the report type for display
 */
function formatReportType(type: string): string {
  switch (type) {
    case "INVOICE_SUMMARY":
      return "Invoice Summary";
    case "VENDOR_ANALYSIS":
      return "Vendor Analysis";
    case "CATEGORY_BREAKDOWN":
      return "Category Breakdown";
    case "CASH_FLOW":
      return "Cash Flow Analysis";
    case "PROFIT_LOSS":
      return "Profit & Loss";
    case "BALANCE_SHEET":
      return "Balance Sheet";
    case "EXPENSES":
      return "Expenses Report";
    case "CATEGORY_ANALYSIS":
      return "Category Analysis";
    default:
      return "Custom Report";
  }
}

/**
 * Adds expenses report data to the PDF
 */
function addExpensesReport(doc: jsPDF, report: ReportWithData, startY: number, isArabic: boolean): number {
  let yPos = startY;

  const expenseData = report.data.filter(item => item.dataPoint === "expense");
  const dailyExpenseData = report.data.filter(item => item.dataPoint === "daily_expense");
  const averageExpense = report.data.find(item => item.dataPoint === "average_expense");

  doc.setFontSize(16);
  doc.text("Expense Summary", 14, yPos);
  doc.setLineWidth(0.3);
  doc.line(14, yPos + 2, 100, yPos + 2);
  yPos += 10;

  doc.setFontSize(12);

  const totalExpenses = expenseData.reduce((sum, item) => sum + item.value, 0);
  doc.text(`Total Expenses: ${formatCurrency(totalExpenses)}`, 14, yPos);
  yPos += 6;

  if (averageExpense) {
    doc.text(`Average Expense: ${formatCurrency(averageExpense.value)}`, 14, yPos);
    yPos += 6;
  }

  doc.text(`Number of Expenses: ${expenseData.length}`, 14, yPos);
  yPos += 15;

  if (expenseData.length > 0) {
    doc.setFontSize(14);
    doc.text("Expense Details", 14, yPos);
    doc.setLineWidth(0.3);
    doc.line(14, yPos + 2, 100, yPos + 2);
    yPos += 10;

    if (yPos > 220) {
      doc.addPage();
      yPos = 20;
    }

    const tableHeaders = ["Date", "Category", "Amount"];
    const tableData = expenseData.map(item => [
      // Use the label which contains the creation date
      item.label,
      item.category || "Uncategorized",
      formatCurrency(item.value)
    ]);

    autoTable(doc, {
      startY: yPos,
      head: [tableHeaders],
      body: tableData,
      theme: "striped",
      headStyles: {
        fillColor: [60, 90, 153],
        textColor: [255, 255, 255],
        font: isArabic ? 'Amiri' : 'helvetica',
        fontStyle: isArabic ? 'bold' : 'normal'
      },
      margin: { top: 10, left: 14, right: 14 },
      styles: {
        fontSize: 10,
        halign: isArabic ? 'right' : 'left',
        font: isArabic ? 'Amiri' : 'helvetica',
        cellPadding: 3
      },
      didParseCell: function(data) {
        // For right-to-left tables in Arabic
        if (isArabic) {
          data.cell.styles.halign = 'right';

          // Apply bold font to header cells
          if (data.row.index === 0) {
            data.cell.styles.fontStyle = 'bold';
          }
        }
      }
    });

    yPos = (doc as unknown as JsPDFWithAutoTable).lastAutoTable.finalY + 10;
  }

  if (dailyExpenseData.length > 0) {
    if (yPos > 220) {
      doc.addPage();
      yPos = 20;
    }

    doc.setFontSize(14);
    doc.text("Daily Expenses Trend", 14, yPos);
    doc.setLineWidth(0.3);
    doc.line(14, yPos + 2, 120, yPos + 2);
    yPos += 10;

    const tableHeaders = ["Date", "Total Amount", "Number of Invoices"];
    const tableData = dailyExpenseData.map(item => {
      const dayCount = expenseData.filter(exp => exp.label === item.label).length;
      return [
        item.label,
        formatCurrency(item.value),
        dayCount.toString()
      ];
    });

    autoTable(doc, {
      startY: yPos,
      head: [tableHeaders],
      body: tableData,
      theme: "striped",
      headStyles: {
        fillColor: [60, 90, 153],
        textColor: [255, 255, 255],
        font: isArabic ? 'Amiri' : 'helvetica',
        fontStyle: isArabic ? 'bold' : 'normal'
      },
      margin: { top: 10, left: 14, right: 14 },
      styles: {
        fontSize: 10,
        halign: isArabic ? 'right' : 'left',
        font: isArabic ? 'Amiri' : 'helvetica',
        cellPadding: 3
      },
      didParseCell: function(data) {
        // For right-to-left tables in Arabic
        if (isArabic) {
          data.cell.styles.halign = 'right';

          // Apply bold font to header cells
          if (data.row.index === 0) {
            data.cell.styles.fontStyle = 'bold';
          }
        }
      }
    });

    yPos = (doc as unknown as JsPDFWithAutoTable).lastAutoTable.finalY + 10;
  }

  return yPos;
}

/**
 * Adds vendor analysis report data to the PDF
 */
function addVendorAnalysisReport(doc: jsPDF, report: ReportWithData, startY: number, isArabic: boolean): number {
  let yPos = startY;

  const vendorTotalData = report.data.filter(item => item.dataPoint === "vendor_total");
  const vendorCountData = report.data.filter(item => item.dataPoint === "vendor_count");
  const vendorAverageData = report.data.filter(item => item.dataPoint === "vendor_average");
  const topVendorData = report.data.filter(item => item.dataPoint === "top_vendor");

  doc.setFontSize(16);
  doc.text("Vendor Analysis Summary", 14, yPos);
  doc.setLineWidth(0.3);
  doc.line(14, yPos + 2, 130, yPos + 2);
  yPos += 10;

  doc.setFontSize(12);

  const uniqueVendors = new Set(vendorTotalData.map(item => item.label));
  doc.text(`Total Vendors: ${uniqueVendors.size}`, 14, yPos);
  yPos += 6;

  const totalSpend = vendorTotalData.reduce((sum, item) => sum + item.value, 0);
  doc.text(`Total Spend: ${formatCurrency(totalSpend)}`, 14, yPos);
  yPos += 15;

  if (topVendorData.length > 0) {
    doc.setFontSize(14);
    doc.text("Top Vendors by Spend", 14, yPos);
    doc.setLineWidth(0.3);
    doc.line(14, yPos + 2, 120, yPos + 2);
    yPos += 10;

    const tableHeaders = ["Rank", "Vendor", "Total Spend", "% of Total"];
    const tableData = topVendorData.map(item => {
      const percentage = (item.value / totalSpend) * 100;
      const labelParts = item.label ? item.label.split(":") : ["", "Unknown"];
      return [
        labelParts[0],
        labelParts[1] ? labelParts[1].trim() : "Unknown",
        formatCurrency(item.value),
        `${percentage.toFixed(2)}%`
      ];
    });

    autoTable(doc, {
      startY: yPos,
      head: [tableHeaders],
      body: tableData,
      theme: "striped",
      headStyles: {
        fillColor: [60, 90, 153],
        textColor: [255, 255, 255],
        font: isArabic ? 'Amiri' : 'helvetica',
        fontStyle: isArabic ? 'bold' : 'normal'
      },
      margin: { top: 10, left: 14, right: 14 },
      styles: {
        fontSize: 10,
        halign: isArabic ? 'right' : 'left',
        font: isArabic ? 'Amiri' : 'helvetica',
        cellPadding: 3
      },
      didParseCell: function(data) {
        // For right-to-left tables in Arabic
        if (isArabic) {
          data.cell.styles.halign = 'right';

          // Apply bold font to header cells
          if (data.row.index === 0) {
            data.cell.styles.fontStyle = 'bold';
          }
        }
      }
    });

    yPos = (doc as unknown as JsPDFWithAutoTable).lastAutoTable.finalY + 10;
  }

  if (vendorTotalData.length > 0) {
    if (yPos > 220) {
      doc.addPage();
      yPos = 20;
    }

    doc.setFontSize(14);
    doc.text("Vendor Details", 14, yPos);
    doc.setLineWidth(0.3);
    doc.line(14, yPos + 2, 100, yPos + 2);
    yPos += 10;

    const tableHeaders = ["Vendor", "Total Spend", "Invoice Count", "Average Invoice"];
    const tableData = vendorTotalData.map((item, index) => {
      const count = vendorCountData[index]?.value || 0;
      const average = vendorAverageData[index]?.value || 0;

      return [
        item.label,
        formatCurrency(item.value),
        count.toString(),
        formatCurrency(average)
      ];
    });

    autoTable(doc, {
      startY: yPos,
      head: [tableHeaders],
      body: tableData,
      theme: "striped",
      headStyles: {
        fillColor: [60, 90, 153],
        textColor: [255, 255, 255],
        font: isArabic ? 'Amiri' : 'helvetica',
        fontStyle: isArabic ? 'bold' : 'normal'
      },
      margin: { top: 10, left: 14, right: 14 },
      styles: {
        fontSize: 10,
        halign: isArabic ? 'right' : 'left',
        font: isArabic ? 'Amiri' : 'helvetica',
        cellPadding: 3
      },
      didParseCell: function(data) {
        // For right-to-left tables in Arabic
        if (isArabic) {
          data.cell.styles.halign = 'right';

          // Apply bold font to header cells
          if (data.row.index === 0) {
            data.cell.styles.fontStyle = 'bold';
          }
        }
      }
    });

    yPos = (doc as unknown as JsPDFWithAutoTable).lastAutoTable.finalY + 10;
  }

  return yPos;
}

/**
 * Adds category analysis report data to the PDF
 */
function addCategoryAnalysisReport(doc: jsPDF, report: ReportWithData, startY: number, isArabic: boolean): number {
  let yPos = startY;

  const categoryTotalData = report.data.filter(item => item.dataPoint === "category_total");
  const categoryPercentageData = report.data.filter(item => item.dataPoint === "category_percentage");
  const categoryCountData = report.data.filter(item => item.dataPoint === "category_count");
  const categoryAverageData = report.data.filter(item => item.dataPoint === "category_average");
  const topCategoryData = report.data.filter(item => item.dataPoint === "top_category");

  doc.setFontSize(16);
  doc.text("Category Analysis Summary", 14, yPos);
  doc.setLineWidth(0.3);
  doc.line(14, yPos + 2, 130, yPos + 2);
  yPos += 10;

  doc.setFontSize(12);

  const uniqueCategories = new Set(categoryTotalData.map(item => item.label));
  doc.text(`Total Categories: ${uniqueCategories.size}`, 14, yPos);
  yPos += 6;

  const totalSpend = categoryTotalData.reduce((sum, item) => sum + item.value, 0);
  doc.text(`Total Spend: ${formatCurrency(totalSpend)}`, 14, yPos);
  yPos += 15;

  if (topCategoryData.length > 0) {
    doc.setFontSize(14);
    doc.text("Top Categories by Spend", 14, yPos);
    doc.setLineWidth(0.3);
    doc.line(14, yPos + 2, 130, yPos + 2);
    yPos += 10;

    const tableHeaders = ["Rank", "Category", "Total Spend", "% of Total"];
    const tableData = topCategoryData.map(item => {
      const percentage = (item.value / totalSpend) * 100;
      const labelParts = item.label ? item.label.split(":") : ["", "Unknown"];
      return [
        labelParts[0],
        labelParts[1] ? labelParts[1].trim() : "Unknown",
        formatCurrency(item.value),
        `${percentage.toFixed(2)}%`
      ];
    });

    autoTable(doc, {
      startY: yPos,
      head: [tableHeaders],
      body: tableData,
      theme: "striped",
      headStyles: {
        fillColor: [60, 90, 153],
        textColor: [255, 255, 255],
        font: isArabic ? 'Amiri' : 'helvetica',
        fontStyle: isArabic ? 'bold' : 'normal'
      },
      margin: { top: 10, left: 14, right: 14 },
      styles: {
        fontSize: 10,
        halign: isArabic ? 'right' : 'left',
        font: isArabic ? 'Amiri' : 'helvetica',
        cellPadding: 3
      },
      didParseCell: function(data) {
        // For right-to-left tables in Arabic
        if (isArabic) {
          data.cell.styles.halign = 'right';

          // Apply bold font to header cells
          if (data.row.index === 0) {
            data.cell.styles.fontStyle = 'bold';
          }
        }
      }
    });

    yPos = (doc as unknown as JsPDFWithAutoTable).lastAutoTable.finalY + 10;
  }

  if (categoryTotalData.length > 0) {
    if (yPos > 220) {
      doc.addPage();
      yPos = 20;
    }

    doc.setFontSize(14);
    doc.text("Category Details", 14, yPos);
    doc.setLineWidth(0.3);
    doc.line(14, yPos + 2, 100, yPos + 2);
    yPos += 10;

    const tableHeaders = ["Category", "Total Spend", "% of Total", "Invoice Count", "Average Invoice"];
    const tableData = categoryTotalData.map((item, index) => {
      const percentage = categoryPercentageData[index]?.value || 0;
      const count = categoryCountData[index]?.value || 0;
      const average = categoryAverageData[index]?.value || 0;

      return [
        item.label,
        formatCurrency(item.value),
        `${percentage.toFixed(2)}%`,
        count.toString(),
        formatCurrency(average)
      ];
    });

    autoTable(doc, {
      startY: yPos,
      head: [tableHeaders],
      body: tableData,
      theme: "striped",
      headStyles: {
        fillColor: [60, 90, 153],
        textColor: [255, 255, 255],
        font: isArabic ? 'Amiri' : 'helvetica',
        fontStyle: isArabic ? 'bold' : 'normal'
      },
      margin: { top: 10, left: 14, right: 14 },
      styles: {
        fontSize: 10,
        halign: isArabic ? 'right' : 'left',
        font: isArabic ? 'Amiri' : 'helvetica',
        cellPadding: 3
      },
      didParseCell: function(data) {
        // For right-to-left tables in Arabic
        if (isArabic) {
          data.cell.styles.halign = 'right';

          // Apply bold font to header cells
          if (data.row.index === 0) {
            data.cell.styles.fontStyle = 'bold';
          }
        }
      }
    });

    yPos = (doc as unknown as JsPDFWithAutoTable).lastAutoTable.finalY + 10;
  }

  return yPos;
}

/**
 * Adds cash flow report data to the PDF
 */
function addCashFlowReport(doc: jsPDF, report: ReportWithData, startY: number, isArabic: boolean): number {
  let yPos = startY;

  const monthlyCashFlowData = report.data.filter(item => item.dataPoint === "monthly_cashflow");
  const cumulativeCashFlowData = report.data.filter(item => item.dataPoint === "cumulative_cashflow");
  const monthlyChangeData = report.data.filter(item => item.dataPoint === "monthly_change");
  const monthlyChangePercentData = report.data.filter(item => item.dataPoint === "monthly_change_percent");
  const quarterlyCashFlowData = report.data.filter(item => item.dataPoint === "quarterly_cashflow");

  doc.setFontSize(16);
  doc.text("Cash Flow Summary", 14, yPos);
  doc.setLineWidth(0.3);
  doc.line(14, yPos + 2, 110, yPos + 2);
  yPos += 10;

  doc.setFontSize(12);

  const totalCashFlow = monthlyCashFlowData.reduce((sum, item) => sum + item.value, 0);
  doc.text(`Total Cash Flow: ${formatCurrency(totalCashFlow)}`, 14, yPos);
  yPos += 6;

  const avgMonthlyCashFlow = monthlyCashFlowData.length > 0
    ? totalCashFlow / monthlyCashFlowData.length
    : 0;
  doc.text(`Average Monthly Cash Flow: ${formatCurrency(avgMonthlyCashFlow)}`, 14, yPos);
  yPos += 15;

  if (monthlyCashFlowData.length > 0) {
    doc.setFontSize(14);
    doc.text("Monthly Cash Flow", 14, yPos);
    doc.setLineWidth(0.3);
    doc.line(14, yPos + 2, 110, yPos + 2);
    yPos += 10;

    const tableHeaders = ["Month", "Cash Flow", "Cumulative", "Change", "Change %"];
    const tableData = monthlyCashFlowData.map((item, index) => {
      const cumulative = cumulativeCashFlowData[index]?.value || 0;
      const change = monthlyChangeData[index]?.value || 0;
      const changePercent = monthlyChangePercentData[index]?.value || 0;

      return [
        item.label,
        formatCurrency(item.value),
        formatCurrency(cumulative),
        formatCurrency(change),
        `${changePercent.toFixed(2)}%`
      ];
    });

    autoTable(doc, {
      startY: yPos,
      head: [tableHeaders],
      body: tableData,
      theme: "striped",
      headStyles: {
        fillColor: [60, 90, 153],
        textColor: [255, 255, 255],
        font: isArabic ? 'Amiri' : 'helvetica',
        fontStyle: isArabic ? 'bold' : 'normal'
      },
      margin: { top: 10, left: 14, right: 14 },
      styles: {
        fontSize: 10,
        halign: isArabic ? 'right' : 'left',
        font: isArabic ? 'Amiri' : 'helvetica',
        cellPadding: 3
      },
      didParseCell: function(data) {
        // For right-to-left tables in Arabic
        if (isArabic) {
          data.cell.styles.halign = 'right';

          // Apply bold font to header cells
          if (data.row.index === 0) {
            data.cell.styles.fontStyle = 'bold';
          }
        }
      }
    });

    yPos = (doc as unknown as JsPDFWithAutoTable).lastAutoTable.finalY + 10;
  }

  if (quarterlyCashFlowData.length > 0) {
    if (yPos > 220) {
      doc.addPage();
      yPos = 20;
    }

    doc.setFontSize(14);
    doc.text("Quarterly Cash Flow", 14, yPos);
    doc.setLineWidth(0.3);
    doc.line(14, yPos + 2, 110, yPos + 2);
    yPos += 10;

    const tableHeaders = ["Quarter", "Cash Flow", "% of Total"];
    const tableData = quarterlyCashFlowData.map(item => {
      const percentage = (item.value / totalCashFlow) * 100;
      return [
        item.label,
        formatCurrency(item.value),
        `${percentage.toFixed(2)}%`
      ];
    });

    autoTable(doc, {
      startY: yPos,
      head: [tableHeaders],
      body: tableData,
      theme: "striped",
      headStyles: {
        fillColor: [60, 90, 153],
        textColor: [255, 255, 255],
        font: isArabic ? 'Amiri' : 'helvetica',
        fontStyle: isArabic ? 'bold' : 'normal'
      },
      margin: { top: 10, left: 14, right: 14 },
      styles: {
        fontSize: 10,
        halign: isArabic ? 'right' : 'left',
        font: isArabic ? 'Amiri' : 'helvetica',
        cellPadding: 3
      },
      didParseCell: function(data) {
        // For right-to-left tables in Arabic
        if (isArabic) {
          data.cell.styles.halign = 'right';

          // Apply bold font to header cells
          if (data.row.index === 0) {
            data.cell.styles.fontStyle = 'bold';
          }
        }
      }
    });

    yPos = (doc as unknown as JsPDFWithAutoTable).lastAutoTable.finalY + 10;
  }

  return yPos;
}

/**
 * Adds default report data to the PDF
 */
function addDefaultReport(doc: jsPDF, report: ReportWithData, startY: number, isArabic: boolean): number {
  let yPos = startY;

  const basicData = report.data.filter(item => item.dataPoint === "amount");
  const summaryData = report.data.filter(item =>
    ["total_amount", "average_amount", "invoice_count", "max_amount"].includes(item.dataPoint)
  );

  doc.setFontSize(16);
  doc.text("Report Summary", 14, yPos);
  doc.setLineWidth(0.3);
  doc.line(14, yPos + 2, 100, yPos + 2);
  yPos += 10;

  doc.setFontSize(12);

  summaryData.forEach(item => {
    let valueText = item.value.toString();

    if (item.dataPoint === "total_amount" || item.dataPoint === "average_amount" || item.dataPoint === "max_amount") {
      valueText = formatCurrency(item.value);
    }

    doc.text(`${item.label}: ${valueText}`, 14, yPos);
    yPos += 6;
  });

  yPos += 10;

  if (basicData.length > 0) {
    if (yPos > 220) {
      doc.addPage();
      yPos = 20;
    }

    doc.setFontSize(14);
    doc.text("Data Details", 14, yPos);
    doc.setLineWidth(0.3);
    doc.line(14, yPos + 2, 100, yPos + 2);
    yPos += 10;

    const tableHeaders = ["Invoice", "Category", "Amount"];
    const tableData = basicData.map(item => [
      item.label,
      item.category,
      formatCurrency(item.value)
    ]);

    autoTable(doc, {
      startY: yPos,
      head: [tableHeaders],
      body: tableData,
      theme: "striped",
      headStyles: {
        fillColor: [60, 90, 153],
        textColor: [255, 255, 255],
        font: isArabic ? 'Amiri' : 'helvetica',
        fontStyle: isArabic ? 'bold' : 'normal'
      },
      margin: { top: 10, left: 14, right: 14 },
      styles: {
        fontSize: 10,
        halign: isArabic ? 'right' : 'left',
        font: isArabic ? 'Amiri' : 'helvetica',
        cellPadding: 3
      },
      didParseCell: function(data) {
        // For right-to-left tables in Arabic
        if (isArabic) {
          data.cell.styles.halign = 'right';

          // Apply bold font to header cells
          if (data.row.index === 0) {
            data.cell.styles.fontStyle = 'bold';
          }
        }
      }
    });

    yPos = (doc as unknown as JsPDFWithAutoTable).lastAutoTable.finalY + 10;
  }

  return yPos;
}

/**
 * Adds balance sheet report data to the PDF
 */
function addBalanceSheetReport(doc: jsPDF, report: ReportWithData, startY: number, isArabic: boolean): number {
  let yPos = startY;

  // Get relevant data points
  const assetsData = report.data.find(item => item.dataPoint === "assets");
  const liabilitiesData = report.data.find(item => item.dataPoint === "liabilities");
  const equityData = report.data.find(item => item.dataPoint === "equity");
  const assetRatioData = report.data.find(item => item.dataPoint === "asset_ratio");
  const assetCategoryData = report.data.filter(item => item.dataPoint === "asset_category");
  const liabilityCategoryData = report.data.filter(item => item.dataPoint === "liability_category");

  doc.setFontSize(16);
  doc.text("Balance Sheet Summary", 14, yPos);
  doc.setLineWidth(0.3);
  doc.line(14, yPos + 2, 130, yPos + 2);
  yPos += 10;

  doc.setFontSize(12);

  // Display main balance sheet components
  if (assetsData) {
    doc.text(`Total Assets: ${formatCurrency(assetsData.value)}`, 14, yPos);
    yPos += 6;
  }

  if (liabilitiesData) {
    doc.text(`Total Liabilities: ${formatCurrency(liabilitiesData.value)}`, 14, yPos);
    yPos += 6;
  }

  if (equityData) {
    doc.text(`Total Equity: ${formatCurrency(equityData.value)}`, 14, yPos);
    yPos += 6;
  }

  if (assetRatioData) {
    doc.text(`Asset to Liability Ratio: ${assetRatioData.value.toFixed(2)}`, 14, yPos);
    yPos += 15;
  }

  // Display asset breakdown by category
  if (assetCategoryData.length > 0) {
    if (yPos > 220) {
      doc.addPage();
      yPos = 20;
    }

    doc.setFontSize(14);
    doc.text("Assets by Category", 14, yPos);
    doc.setLineWidth(0.3);
    doc.line(14, yPos + 2, 110, yPos + 2);
    yPos += 10;

    const totalAssets = assetCategoryData.reduce((sum, item) => sum + item.value, 0);
    const tableHeaders = ["Category", "Amount", "% of Total"];
    const tableData = assetCategoryData.map(item => {
      const percentage = (item.value / totalAssets) * 100;
      return [
        item.category || "Uncategorized",
        formatCurrency(item.value),
        `${percentage.toFixed(2)}%`
      ];
    });

    autoTable(doc, {
      startY: yPos,
      head: [tableHeaders],
      body: tableData,
      theme: "striped",
      headStyles: {
        fillColor: [60, 90, 153],
        textColor: [255, 255, 255],
        font: isArabic ? 'Amiri' : 'helvetica',
        fontStyle: isArabic ? 'bold' : 'normal'
      },
      margin: { top: 10, left: 14, right: 14 },
      styles: {
        fontSize: 10,
        halign: isArabic ? 'right' : 'left',
        font: isArabic ? 'Amiri' : 'helvetica',
        cellPadding: 3
      },
      didParseCell: function(data) {
        if (isArabic) {
          data.cell.styles.halign = 'right';
          if (data.row.index === 0) {
            data.cell.styles.fontStyle = 'bold';
          }
        }
      }
    });

    yPos = (doc as unknown as JsPDFWithAutoTable).lastAutoTable.finalY + 10;
  }

  // Display liability breakdown by category
  if (liabilityCategoryData.length > 0) {
    if (yPos > 220) {
      doc.addPage();
      yPos = 20;
    }

    doc.setFontSize(14);
    doc.text("Liabilities by Category", 14, yPos);
    doc.setLineWidth(0.3);
    doc.line(14, yPos + 2, 130, yPos + 2);
    yPos += 10;

    const totalLiabilities = liabilityCategoryData.reduce((sum, item) => sum + item.value, 0);
    const tableHeaders = ["Category", "Amount", "% of Total"];
    const tableData = liabilityCategoryData.map(item => {
      const percentage = (item.value / totalLiabilities) * 100;
      return [
        item.category || "Uncategorized",
        formatCurrency(item.value),
        `${percentage.toFixed(2)}%`
      ];
    });

    autoTable(doc, {
      startY: yPos,
      head: [tableHeaders],
      body: tableData,
      theme: "striped",
      headStyles: {
        fillColor: [60, 90, 153],
        textColor: [255, 255, 255],
        font: isArabic ? 'Amiri' : 'helvetica',
        fontStyle: isArabic ? 'bold' : 'normal'
      },
      margin: { top: 10, left: 14, right: 14 },
      styles: {
        fontSize: 10,
        halign: isArabic ? 'right' : 'left',
        font: isArabic ? 'Amiri' : 'helvetica',
        cellPadding: 3
      },
      didParseCell: function(data) {
        if (isArabic) {
          data.cell.styles.halign = 'right';
          if (data.row.index === 0) {
            data.cell.styles.fontStyle = 'bold';
          }
        }
      }
    });

    yPos = (doc as unknown as JsPDFWithAutoTable).lastAutoTable.finalY + 10;
  }

  return yPos;
}

/**
 * Adds profit and loss report data to the PDF
 */
function addProfitLossReport(doc: jsPDF, report: ReportWithData, startY: number, isArabic: boolean): number {
  let yPos = startY;

  // Get relevant data points
  const totalIncomeData = report.data.find(item => item.dataPoint === "total_income");
  const totalExpenseData = report.data.find(item => item.dataPoint === "total_expense");
  const totalProfitData = report.data.find(item => item.dataPoint === "total_profit");
  const totalProfitMarginData = report.data.find(item => item.dataPoint === "total_profit_margin");
  const monthlyIncomeData = report.data.filter(item => item.dataPoint === "monthly_income");
  const monthlyExpenseData = report.data.filter(item => item.dataPoint === "monthly_expense");
  const monthlyProfitData = report.data.filter(item => item.dataPoint === "monthly_profit");
  const monthlyProfitMarginData = report.data.filter(item => item.dataPoint === "monthly_profit_margin");
  const categoryProfitData = report.data.filter(item => item.dataPoint === "total_category_profit");

  doc.setFontSize(16);
  doc.text("Profit & Loss Summary", 14, yPos);
  doc.setLineWidth(0.3);
  doc.line(14, yPos + 2, 130, yPos + 2);
  yPos += 10;

  doc.setFontSize(12);

  // Display main P&L components
  if (totalIncomeData) {
    doc.text(`Total Income: ${formatCurrency(totalIncomeData.value)}`, 14, yPos);
    yPos += 6;
  }

  if (totalExpenseData) {
    doc.text(`Total Expenses: ${formatCurrency(totalExpenseData.value)}`, 14, yPos);
    yPos += 6;
  }

  if (totalProfitData) {
    doc.text(`Total Profit/Loss: ${formatCurrency(totalProfitData.value)}`, 14, yPos);
    yPos += 6;
  }

  if (totalProfitMarginData) {
    doc.text(`Profit Margin: ${totalProfitMarginData.value.toFixed(2)}%`, 14, yPos);
    yPos += 15;
  }

  // Display monthly P&L data
  if (monthlyProfitData.length > 0) {
    if (yPos > 220) {
      doc.addPage();
      yPos = 20;
    }

    doc.setFontSize(14);
    doc.text("Monthly Profit & Loss", 14, yPos);
    doc.setLineWidth(0.3);
    doc.line(14, yPos + 2, 130, yPos + 2);
    yPos += 10;

    const tableHeaders = ["Month", "Income", "Expenses", "Profit/Loss", "Margin %"];
    const tableData = monthlyProfitData.map((item, index) => {
      const income = monthlyIncomeData[index]?.value || 0;
      const expense = monthlyExpenseData[index]?.value || 0;
      const margin = monthlyProfitMarginData[index]?.value || 0;

      return [
        item.label,
        formatCurrency(income),
        formatCurrency(expense),
        formatCurrency(item.value),
        `${margin.toFixed(2)}%`
      ];
    });

    autoTable(doc, {
      startY: yPos,
      head: [tableHeaders],
      body: tableData,
      theme: "striped",
      headStyles: {
        fillColor: [60, 90, 153],
        textColor: [255, 255, 255],
        font: isArabic ? 'Amiri' : 'helvetica',
        fontStyle: isArabic ? 'bold' : 'normal'
      },
      margin: { top: 10, left: 14, right: 14 },
      styles: {
        fontSize: 10,
        halign: isArabic ? 'right' : 'left',
        font: isArabic ? 'Amiri' : 'helvetica',
        cellPadding: 3
      },
      didParseCell: function(data) {
        if (isArabic) {
          data.cell.styles.halign = 'right';
          if (data.row.index === 0) {
            data.cell.styles.fontStyle = 'bold';
          }
        }
      }
    });

    yPos = (doc as unknown as JsPDFWithAutoTable).lastAutoTable.finalY + 10;
  }

  // Display category profit data
  if (categoryProfitData.length > 0) {
    if (yPos > 220) {
      doc.addPage();
      yPos = 20;
    }

    doc.setFontSize(14);
    doc.text("Profit by Category", 14, yPos);
    doc.setLineWidth(0.3);
    doc.line(14, yPos + 2, 110, yPos + 2);
    yPos += 10;

    const tableHeaders = ["Category", "Profit/Loss", "% of Total"];
    const totalProfit = totalProfitData?.value || 0;
    const tableData = categoryProfitData.map(item => {
      const percentage = totalProfit !== 0 ? (item.value / Math.abs(totalProfit)) * 100 : 0;
      return [
        item.category || "Uncategorized",
        formatCurrency(item.value),
        `${percentage.toFixed(2)}%`
      ];
    });

    autoTable(doc, {
      startY: yPos,
      head: [tableHeaders],
      body: tableData,
      theme: "striped",
      headStyles: {
        fillColor: [60, 90, 153],
        textColor: [255, 255, 255],
        font: isArabic ? 'Amiri' : 'helvetica',
        fontStyle: isArabic ? 'bold' : 'normal'
      },
      margin: { top: 10, left: 14, right: 14 },
      styles: {
        fontSize: 10,
        halign: isArabic ? 'right' : 'left',
        font: isArabic ? 'Amiri' : 'helvetica',
        cellPadding: 3
      },
      didParseCell: function(data) {
        if (isArabic) {
          data.cell.styles.halign = 'right';
          if (data.row.index === 0) {
            data.cell.styles.fontStyle = 'bold';
          }
        }
      }
    });

    yPos = (doc as unknown as JsPDFWithAutoTable).lastAutoTable.finalY + 10;
  }

  return yPos;
}

/**
 * Adds tax report data to the PDF
 */
function addTaxReport(doc: jsPDF, report: ReportWithData, startY: number, isArabic: boolean): number {
  let yPos = startY;

  // Get relevant data points
  const totalTaxableAmountData = report.data.find(item => item.dataPoint === "total_taxable_amount");
  const totalTaxAmountData = report.data.find(item => item.dataPoint === "total_tax_amount");
  const effectiveTaxRateData = report.data.find(item => item.dataPoint === "effective_tax_rate");
  const monthlyTaxableAmountData = report.data.filter(item => item.dataPoint === "monthly_taxable_amount");
  const monthlyTaxAmountData = report.data.filter(item => item.dataPoint === "monthly_tax_amount");
  const monthlyEffectiveTaxRateData = report.data.filter(item => item.dataPoint === "monthly_effective_tax_rate");
  const categoryTaxData = report.data.filter(item => item.dataPoint === "total_category_tax");
  const categoryTaxRateData = report.data.filter(item => item.dataPoint === "category_tax_rate");

  doc.setFontSize(16);
  doc.text("Tax Report Summary", 14, yPos);
  doc.setLineWidth(0.3);
  doc.line(14, yPos + 2, 120, yPos + 2);
  yPos += 10;

  doc.setFontSize(12);

  // Display main tax components
  if (totalTaxableAmountData) {
    doc.text(`Total Taxable Amount: ${formatCurrency(totalTaxableAmountData.value)}`, 14, yPos);
    yPos += 6;
  }

  if (totalTaxAmountData) {
    doc.text(`Total Tax Amount: ${formatCurrency(totalTaxAmountData.value)}`, 14, yPos);
    yPos += 6;
  }

  if (effectiveTaxRateData) {
    doc.text(`Effective Tax Rate: ${effectiveTaxRateData.value.toFixed(2)}%`, 14, yPos);
    yPos += 15;
  }

  // Display monthly tax data
  if (monthlyTaxAmountData.length > 0) {
    if (yPos > 220) {
      doc.addPage();
      yPos = 20;
    }

    doc.setFontSize(14);
    doc.text("Monthly Tax Summary", 14, yPos);
    doc.setLineWidth(0.3);
    doc.line(14, yPos + 2, 130, yPos + 2);
    yPos += 10;

    const tableHeaders = ["Month", "Taxable Amount", "Tax Amount", "Effective Rate"];
    const tableData = monthlyTaxAmountData.map((item, index) => {
      const taxableAmount = monthlyTaxableAmountData[index]?.value || 0;
      const effectiveRate = monthlyEffectiveTaxRateData[index]?.value || 0;

      return [
        item.label,
        formatCurrency(taxableAmount),
        formatCurrency(item.value),
        `${effectiveRate.toFixed(2)}%`
      ];
    });

    autoTable(doc, {
      startY: yPos,
      head: [tableHeaders],
      body: tableData,
      theme: "striped",
      headStyles: {
        fillColor: [60, 90, 153],
        textColor: [255, 255, 255],
        font: isArabic ? 'Amiri' : 'helvetica',
        fontStyle: isArabic ? 'bold' : 'normal'
      },
      margin: { top: 10, left: 14, right: 14 },
      styles: {
        fontSize: 10,
        halign: isArabic ? 'right' : 'left',
        font: isArabic ? 'Amiri' : 'helvetica',
        cellPadding: 3
      },
      didParseCell: function(data) {
        if (isArabic) {
          data.cell.styles.halign = 'right';
          if (data.row.index === 0) {
            data.cell.styles.fontStyle = 'bold';
          }
        }
      }
    });

    yPos = (doc as unknown as JsPDFWithAutoTable).lastAutoTable.finalY + 10;
  }

  // Display category tax data
  if (categoryTaxData.length > 0) {
    if (yPos > 220) {
      doc.addPage();
      yPos = 20;
    }

    doc.setFontSize(14);
    doc.text("Tax by Category", 14, yPos);
    doc.setLineWidth(0.3);
    doc.line(14, yPos + 2, 100, yPos + 2);
    yPos += 10;

    const tableHeaders = ["Category", "Tax Amount", "Tax Rate", "% of Total"];
    const totalTax = totalTaxAmountData?.value || 0;
    const tableData = categoryTaxData.map((item, index) => {
      const taxRate = categoryTaxRateData[index]?.value || 0;
      const percentage = totalTax !== 0 ? (item.value / totalTax) * 100 : 0;
      return [
        item.category || "Uncategorized",
        formatCurrency(item.value),
        `${taxRate.toFixed(2)}%`,
        `${percentage.toFixed(2)}%`
      ];
    });

    autoTable(doc, {
      startY: yPos,
      head: [tableHeaders],
      body: tableData,
      theme: "striped",
      headStyles: {
        fillColor: [60, 90, 153],
        textColor: [255, 255, 255],
        font: isArabic ? 'Amiri' : 'helvetica',
        fontStyle: isArabic ? 'bold' : 'normal'
      },
      margin: { top: 10, left: 14, right: 14 },
      styles: {
        fontSize: 10,
        halign: isArabic ? 'right' : 'left',
        font: isArabic ? 'Amiri' : 'helvetica',
        cellPadding: 3
      },
      didParseCell: function(data) {
        if (isArabic) {
          data.cell.styles.halign = 'right';
          if (data.row.index === 0) {
            data.cell.styles.fontStyle = 'bold';
          }
        }
      }
    });

    yPos = (doc as unknown as JsPDFWithAutoTable).lastAutoTable.finalY + 10;
  }

  return yPos;
}

/**
 * Adds sales report data to the PDF
 */
function addSalesReport(doc: jsPDF, report: ReportWithData, startY: number, isArabic: boolean): number {
  let yPos = startY;

  // Get relevant data points
  const totalSalesData = report.data.find(item => item.dataPoint === "total_sales");
  const averageSaleData = report.data.find(item => item.dataPoint === "average_sale");
  const averageMonthlySalesData = report.data.find(item => item.dataPoint === "average_monthly_sales");
  const monthlySalesData = report.data.filter(item => item.dataPoint === "monthly_sales");
  const monthlyGrowthData = report.data.filter(item => item.dataPoint === "monthly_growth");
  const categorySalesData = report.data.filter(item => item.dataPoint === "category_sales");
  const vendorSalesData = report.data.filter(item => item.dataPoint === "vendor_sales");

  doc.setFontSize(16);
  doc.text("Sales Report Summary", 14, yPos);
  doc.setLineWidth(0.3);
  doc.line(14, yPos + 2, 130, yPos + 2);
  yPos += 10;

  doc.setFontSize(12);

  // Display main sales components
  if (totalSalesData) {
    doc.text(`Total Sales: ${formatCurrency(totalSalesData.value)}`, 14, yPos);
    yPos += 6;
  }

  if (averageSaleData) {
    doc.text(`Average Sale: ${formatCurrency(averageSaleData.value)}`, 14, yPos);
    yPos += 6;
  }

  if (averageMonthlySalesData) {
    doc.text(`Average Monthly Sales: ${formatCurrency(averageMonthlySalesData.value)}`, 14, yPos);
    yPos += 15;
  }

  // Display monthly sales data
  if (monthlySalesData.length > 0) {
    if (yPos > 220) {
      doc.addPage();
      yPos = 20;
    }

    doc.setFontSize(14);
    doc.text("Monthly Sales", 14, yPos);
    doc.setLineWidth(0.3);
    doc.line(14, yPos + 2, 100, yPos + 2);
    yPos += 10;

    const tableHeaders = ["Month", "Sales Amount", "Growth %"];
    const tableData = monthlySalesData.map((item, index) => {
      const growth = monthlyGrowthData[index]?.value || 0;
      return [
        item.label,
        formatCurrency(item.value),
        `${growth.toFixed(2)}%`
      ];
    });

    autoTable(doc, {
      startY: yPos,
      head: [tableHeaders],
      body: tableData,
      theme: "striped",
      headStyles: {
        fillColor: [60, 90, 153],
        textColor: [255, 255, 255],
        font: isArabic ? 'Amiri' : 'helvetica',
        fontStyle: isArabic ? 'bold' : 'normal'
      },
      margin: { top: 10, left: 14, right: 14 },
      styles: {
        fontSize: 10,
        halign: isArabic ? 'right' : 'left',
        font: isArabic ? 'Amiri' : 'helvetica',
        cellPadding: 3
      },
      didParseCell: function(data) {
        if (isArabic) {
          data.cell.styles.halign = 'right';
          if (data.row.index === 0) {
            data.cell.styles.fontStyle = 'bold';
          }
        }
      }
    });

    yPos = (doc as unknown as JsPDFWithAutoTable).lastAutoTable.finalY + 10;
  }

  // Display category sales data
  if (categorySalesData.length > 0) {
    if (yPos > 220) {
      doc.addPage();
      yPos = 20;
    }

    doc.setFontSize(14);
    doc.text("Sales by Category", 14, yPos);
    doc.setLineWidth(0.3);
    doc.line(14, yPos + 2, 110, yPos + 2);
    yPos += 10;

    const tableHeaders = ["Category", "Sales Amount", "% of Total"];
    const totalSales = totalSalesData?.value || 0;
    const tableData = categorySalesData.map(item => {
      const percentage = totalSales !== 0 ? (item.value / totalSales) * 100 : 0;
      return [
        item.category || "Uncategorized",
        formatCurrency(item.value),
        `${percentage.toFixed(2)}%`
      ];
    });

    autoTable(doc, {
      startY: yPos,
      head: [tableHeaders],
      body: tableData,
      theme: "striped",
      headStyles: {
        fillColor: [60, 90, 153],
        textColor: [255, 255, 255],
        font: isArabic ? 'Amiri' : 'helvetica',
        fontStyle: isArabic ? 'bold' : 'normal'
      },
      margin: { top: 10, left: 14, right: 14 },
      styles: {
        fontSize: 10,
        halign: isArabic ? 'right' : 'left',
        font: isArabic ? 'Amiri' : 'helvetica',
        cellPadding: 3
      },
      didParseCell: function(data) {
        if (isArabic) {
          data.cell.styles.halign = 'right';
          if (data.row.index === 0) {
            data.cell.styles.fontStyle = 'bold';
          }
        }
      }
    });

    yPos = (doc as unknown as JsPDFWithAutoTable).lastAutoTable.finalY + 10;
  }

  // Display vendor sales data
  if (vendorSalesData.length > 0) {
    if (yPos > 220) {
      doc.addPage();
      yPos = 20;
    }

    doc.setFontSize(14);
    doc.text("Sales by Vendor", 14, yPos);
    doc.setLineWidth(0.3);
    doc.line(14, yPos + 2, 100, yPos + 2);
    yPos += 10;

    const tableHeaders = ["Vendor", "Sales Amount", "% of Total"];
    const totalSales = totalSalesData?.value || 0;
    const tableData = vendorSalesData.map(item => {
      const percentage = totalSales !== 0 ? (item.value / totalSales) * 100 : 0;
      return [
        item.label || "Unknown",
        formatCurrency(item.value),
        `${percentage.toFixed(2)}%`
      ];
    });

    autoTable(doc, {
      startY: yPos,
      head: [tableHeaders],
      body: tableData,
      theme: "striped",
      headStyles: {
        fillColor: [60, 90, 153],
        textColor: [255, 255, 255],
        font: isArabic ? 'Amiri' : 'helvetica',
        fontStyle: isArabic ? 'bold' : 'normal'
      },
      margin: { top: 10, left: 14, right: 14 },
      styles: {
        fontSize: 10,
        halign: isArabic ? 'right' : 'left',
        font: isArabic ? 'Amiri' : 'helvetica',
        cellPadding: 3
      },
      didParseCell: function(data) {
        if (isArabic) {
          data.cell.styles.halign = 'right';
          if (data.row.index === 0) {
            data.cell.styles.fontStyle = 'bold';
          }
        }
      }
    });

    yPos = (doc as unknown as JsPDFWithAutoTable).lastAutoTable.finalY + 10;
  }

  return yPos;
}

/**
 * Helper function to format currency
 */
function formatCurrency(amount: number, currency = "USD"): string {
  // Validate currency code - must be a 3-letter code according to ISO 4217
  // Common mistake is passing the currency symbol (like $) instead of the code
  if (!currency || currency.length !== 3 || /[$€£¥]/.test(currency)) {
    // Default to USD if invalid currency code is provided
    currency = "USD";
  }

  try {
    // Always use en-US locale to keep numbers in English format
    return new Intl.NumberFormat('en-US', {
      style: "currency",
      currency,
    }).format(amount);
  } catch (error) {
    // Fallback to basic formatting if Intl.NumberFormat fails
    console.warn(`Error formatting currency ${currency}:`, error);
    return `${currency} ${amount.toFixed(2)}`;
  }
}
