import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import db from '@/db/db';
import { error } from 'console';

/**
 * Get enhancement job status for an invoice
 */
export async function GET(request: NextRequest) {
  try {
    const { userId: clerkUserId } = await auth();
    if (!clerkUserId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const invoiceId = searchParams.get('invoiceId');

    if (!invoiceId) {
      return NextResponse.json(
        { error: 'Invoice ID is required' },
        { status: 400 }
      );
    }

    // Get the database user ID from Clerk ID
    const user = await db.user.findUnique({
      where: { clerkId: clerkUserId },
      select: { id: true },
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Find the enhancement job for this invoice
    const job = await db.invoiceEnhancementJob.findFirst({
      where: {
        invoiceId,
        userId: user.id,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    if (!job) {
      return NextResponse.json(
        { error: 'Enhancement job not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(job);
  } catch (error) {
    console.error('Error fetching enhancement job status:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Background job for processing invoice enhancements
 * This handles the non-critical functions that can be processed asynchronously
 */
export async function POST(request: NextRequest) {
  try {
    // Get authenticated user
    const { userId: clerkUserId } = await auth();
    if (!clerkUserId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { invoiceId, enhancementTypes = ['all'] } = body;

    if (!invoiceId) {
      return NextResponse.json(
        { error: 'Invoice ID is required' },
        { status: 400 }
      );
    }

    // Get the invoice from database
    const invoice = await db.invoice.findUnique({
      where: { id: invoiceId },
      include: {
        lineItems: true,
        vendor: true,
        user: true,
      },
    });

    if (!invoice) {
      return NextResponse.json(
        { error: 'Invoice not found' },
        { status: 404 }
      );
    }

    // Check if user owns this invoice
    if (invoice.user.clerkId !== clerkUserId) {
      return NextResponse.json(
        { error: 'Unauthorized access to invoice' },
        { status: 403 }
      );
    }

    // Create enhancement job record
    const enhancementJob = await db.invoiceEnhancementJob.create({
      data: {
        id: `enh_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        invoiceId,
        userId: invoice.userId,
        status: 'processing',
        enhancementTypes: enhancementTypes,
        startedAt: new Date(),
      },
    });

    // Process enhancements in background
    processInvoiceEnhancements(
      enhancementJob.id,
      invoice,
      enhancementTypes
    ).catch((error) => {
      console.error(
        'Error in background enhancement processing:',
        error
      );
      // Update job status to failed
      db.invoiceEnhancementJob
        .update({
          where: { id: enhancementJob.id },
          data: {
            status: 'failed',
            error:
              error instanceof Error
                ? error.message
                : 'Unknown error',
            completedAt: new Date(),
          },
        })
        .catch(console.error);
    });

    return NextResponse.json({
      success: true,
      jobId: enhancementJob.id,
      message: 'Enhancement job started',
    });
  } catch (error) {
    console.error('Error starting invoice enhancement job:', error);
    return NextResponse.json(
      { error: 'Failed to start enhancement job' },
      { status: 500 }
    );
  }
}

/**
 * Background function to process invoice enhancements
 */
async function processInvoiceEnhancements(
  jobId: string,
  invoice: any,
  enhancementTypes: string[]
) {
  try {
    const results: Record<string, any> = {};

    // Simulate enhanced processing functions
    // In a real implementation, these would call the actual enhancement functions

    if (
      enhancementTypes.includes('all') ||
      enhancementTypes.includes('fraud')
    ) {
      try {
        // Simulate fraud detection
        await new Promise((resolve) => setTimeout(resolve, 1000));
        results.fraud = {
          status: 'completed',
          fraudScore: Math.random() * 100,
          riskLevel: 'low',
        };
      } catch (error) {
        results.fraud = {
          error:
            error instanceof Error
              ? error.message
              : 'Fraud detection failed',
        };
      }
    }

    if (
      enhancementTypes.includes('all') ||
      enhancementTypes.includes('related')
    ) {
      try {
        // Simulate related document discovery
        await new Promise((resolve) => setTimeout(resolve, 800));
        results.related = {
          status: 'completed',
          documentsFound: Math.floor(Math.random() * 5),
        };
      } catch (error) {
        results.related = {
          error:
            error instanceof Error
              ? error.message
              : 'Document discovery failed',
        };
      }
    }

    if (
      enhancementTypes.includes('all') ||
      enhancementTypes.includes('payment')
    ) {
      try {
        // Simulate payment prediction
        await new Promise((resolve) => setTimeout(resolve, 1200));
        results.payment = {
          status: 'completed',
          predictedDays: Math.floor(Math.random() * 30) + 1,
          confidence: Math.random() * 100,
        };
      } catch (error) {
        results.payment = {
          error:
            error instanceof Error
              ? error.message
              : 'Payment prediction failed',
        };
      }
    }

    if (
      enhancementTypes.includes('all') ||
      enhancementTypes.includes('vendor')
    ) {
      try {
        // Simulate vendor profile update
        await new Promise((resolve) => setTimeout(resolve, 600));
        results.vendor = {
          status: 'completed',
          profileUpdated: true,
        };
      } catch (error) {
        results.vendor = {
          error:
            error instanceof Error
              ? error.message
              : 'Vendor update failed',
        };
      }
    }

    // Update job status to completed
    await db.invoiceEnhancementJob.update({
      where: { id: jobId },
      data: {
        status: 'completed',
        results: results,
        completedAt: new Date(),
      },
    });

    console.log(`Enhancement job ${jobId} completed successfully`);
  } catch (error) {
    console.error(`Enhancement job ${jobId} failed:`, error);

    // Update job status to failed
    await db.invoiceEnhancementJob.update({
      where: { id: jobId },
      data: {
        status: 'failed',
        error:
          error instanceof Error ? error.message : 'Unknown error',
        completedAt: new Date(),
      },
    });
  }
}
