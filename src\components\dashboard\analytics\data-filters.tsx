"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { formatCurrency } from "@/lib/utils";
import { getVendors, getCategories } from "@/lib/actions/analytics";
import { motion } from "motion/react";
import { Search, RefreshCw, Check } from "lucide-react";

interface DataFiltersProps {
  onFilterChange: (filters: FilterValues) => void;
  initialFilters?: FilterValues;
}

export interface FilterValues {
  vendor: string;
  category: string;
  status: string;
  search: string;
  amountRange: [number, number];
}

export function DataFilters({
  onFilterChange,
  initialFilters,
}: DataFiltersProps) {
  const [vendors, setVendors] = useState<{ id: string; name: string }[]>([]);
  const [categories, setCategories] = useState<{ id: string; name: string }[]>(
    []
  );
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<FilterValues>(
    initialFilters || {
      vendor: "all",
      category: "all",
      status: "all",
      search: "",
      amountRange: [0, 10000],
    }
  );
  const [isApplied, setIsApplied] = useState(false);

  useEffect(() => {
    async function loadFilterOptions() {
      setLoading(true);
      try {
        const [vendorsData, categoriesData] = await Promise.all([
          getVendors(),
          getCategories(),
        ]);

        // Add "All vendors" option
        const vendorsWithAll = [
          { id: "all", name: "All vendors" },
          ...vendorsData,
        ];

        // Add "All categories" option
        const categoriesWithAll = [
          { id: "all", name: "All categories" },
          ...categoriesData,
        ];

        setVendors(vendorsWithAll);
        setCategories(categoriesWithAll);
      } catch (error) {
        console.error("Failed to load filter options:", error);
      } finally {
        setLoading(false);
      }
    }

    loadFilterOptions();
  }, []);

  const handleFilterChange = (
    key: keyof FilterValues,
    value: string | number | [number, number]
  ) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    setIsApplied(false);
  };

  const handleApplyFilters = () => {
    onFilterChange(filters);
    setIsApplied(true);

    // Reset the applied state after a short delay
    setTimeout(() => {
      setIsApplied(false);
    }, 2000);
  };

  const handleResetFilters = () => {
    const resetFilters = {
      vendor: "all",
      category: "all",
      status: "all",
      search: "",
      amountRange: [0, 10000],
    };
    setFilters(resetFilters as FilterValues);
    onFilterChange(resetFilters as FilterValues);
  };

  return (
    <motion.div
      initial={{ opacity: 0, height: 0 }}
      animate={{ opacity: 1, height: "auto" }}
      exit={{ opacity: 0, height: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card className="border border-primary/10 bg-card/50 backdrop-blur-sm shadow-lg">
        <CardContent className="pt-6">
          <div className="grid gap-6 md:grid-cols-4">
            <div className="space-y-2">
              <Label htmlFor="vendor" className="text-sm font-medium">
                Vendor
              </Label>
              <Select
                value={filters.vendor}
                onValueChange={(value) => handleFilterChange("vendor", value)}
                disabled={loading}
              >
                <SelectTrigger
                  id="vendor"
                  className="bg-background/50 border-primary/20"
                >
                  <SelectValue placeholder="All vendors" />
                </SelectTrigger>
                <SelectContent>
                  {vendors.map((vendor) => (
                    <SelectItem key={vendor.id} value={vendor.id}>
                      {vendor.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="category" className="text-sm font-medium">
                Category
              </Label>
              <Select
                value={filters.category}
                onValueChange={(value) => handleFilterChange("category", value)}
                disabled={loading}
              >
                <SelectTrigger
                  id="category"
                  className="bg-background/50 border-primary/20"
                >
                  <SelectValue placeholder="All categories" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status" className="text-sm font-medium">
                Status
              </Label>
              <Select
                value={filters.status}
                onValueChange={(value) => handleFilterChange("status", value)}
              >
                <SelectTrigger
                  id="status"
                  className="bg-background/50 border-primary/20"
                >
                  <SelectValue placeholder="All statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All statuses</SelectItem>
                  <SelectItem value="PAID">Paid</SelectItem>
                  <SelectItem value="PENDING">Pending</SelectItem>
                  <SelectItem value="OVERDUE">Overdue</SelectItem>
                  <SelectItem value="CANCELLED">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="search" className="text-sm font-medium">
                Search
              </Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="Search invoices..."
                  value={filters.search}
                  onChange={(e) => handleFilterChange("search", e.target.value)}
                  className="pl-10 bg-background/50 border-primary/20"
                />
              </div>
            </div>

            <div className="space-y-2 md:col-span-4">
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium">Amount Range</Label>
                <span className="text-sm text-muted-foreground">
                  {formatCurrency(filters.amountRange[0])} -{" "}
                  {formatCurrency(filters.amountRange[1])}
                </span>
              </div>
              <Slider
                value={filters.amountRange}
                max={10000}
                step={100}
                onValueChange={(value) =>
                  handleFilterChange("amountRange", value as [number, number])
                }
                className="py-4"
              />
            </div>

            <div className="flex items-center space-x-2 md:col-span-4">
              <Button
                onClick={handleApplyFilters}
                className="bg-primary hover:bg-primary/90 transition-all duration-300"
              >
                {isApplied ? (
                  <>
                    <Check className="mr-2 h-4 w-4" />
                    Applied
                  </>
                ) : (
                  "Apply Filters"
                )}
              </Button>
              <Button
                variant="outline"
                onClick={handleResetFilters}
                className="border-primary/20 hover:bg-primary/10 transition-all duration-300"
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                Reset
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
