'use client';

import type { Attachment, Message } from 'ai';
import { useChat } from 'ai/react';
import { useState } from 'react';
import useSWR, { useSWRConfig } from 'swr';

import { ChatHeader } from '@/components/ai-agent/chat-header';
import type { Vote } from '@prisma/client';
import { fetcher, generateUUID } from '@/lib/utils';

import { Artifact } from './artifact';
import { MultimodalInput } from './multimodal-input';
import { Messages } from './messages';
import { VisibilityType } from './visibility-selector';
import { useArtifactSelector } from '@/hooks/use-artifact';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';

export function Chat({
  id,
  initialMessages,
  selectedChatModel,
  selectedVisibilityType,
  isReadonly,
}: {
  id: string;
  initialMessages: Array<Message>;
  selectedChatModel: string;
  selectedVisibilityType: VisibilityType;
  isReadonly: boolean;
}) {
  const { mutate } = useSWRConfig();
  const router = useRouter();

  const {
    messages,
    setMessages,
    handleSubmit,
    input,
    setInput,
    append,
    isLoading,
    stop,
    reload,
  } = useChat({
    id,
    body: { id, selectedChatModel: selectedChatModel },
    initialMessages,
    experimental_throttle: 100,
    sendExtraMessageFields: true,
    generateId: generateUUID,
    onFinish: () => {
      mutate('/api/history');
    },
    onError: (error) => {
      // Check if this is a usage limit error (429 status)
      if (
        error?.message?.includes('Chat limit exceeded') ||
        error?.message?.includes('429') ||
        (error as any)?.status === 429
      ) {
        // Redirect to subscription page for usage limit errors
        const message =
          'You have reached your chat limit. Please upgrade your plan to continue.';
        const encodedMessage = encodeURIComponent(message);
        router.push(
          `/dashboard/subscription?limit_exceeded=chat&message=${encodedMessage}`
        );
        return;
      }

      // For other errors, show generic error toast
      toast.error('An error occurred, please try again!');
    },
  });

  const { data: votes } = useSWR<Array<Vote>>(
    `/api/vote?chatId=${id}`,
    fetcher
  );

  const [attachments, setAttachments] = useState<Array<Attachment>>(
    []
  );
  const isArtifactVisible = useArtifactSelector(
    (state) => state.isVisible
  );

  // Check if we're in the initial state (no messages)
  const isInitialState = messages.length === 0;

  // Function to handle form submission
  const onFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSubmit(e);
  };

  return (
    <>
      <div className="flex flex-col flex-1 min-w-0 h-full">
        <ChatHeader
          chatId={id}
          selectedModelId={selectedChatModel}
          selectedVisibilityType={selectedVisibilityType}
          isReadonly={isReadonly}
        />

        {isInitialState ? (
          // Initial state layout - centered content with welcome message and input
          <div className="flex flex-col flex-1 justify-center items-center overflow-y-auto">
            <div className="w-full max-w-4xl px-4">
              <Messages
                chatId={id}
                isLoading={isLoading}
                votes={votes}
                messages={messages}
                setMessages={setMessages}
                reload={reload}
                isReadonly={isReadonly}
                isArtifactVisible={isArtifactVisible}
                append={append}
              />

              {!isReadonly && (
                <div className="mt-4 w-full">
                  <form
                    className="flex flex-col mx-auto gap-2 w-full"
                    onSubmit={onFormSubmit}
                  >
                    <MultimodalInput
                      chatId={id}
                      input={input}
                      setInput={setInput}
                      handleSubmit={handleSubmit}
                      isLoading={isLoading}
                      stop={stop}
                      attachments={attachments}
                      setAttachments={setAttachments}
                      messages={messages}
                      setMessages={setMessages}
                      append={append}
                      className="bg-background/95 backdrop-blur-md border-border/40 shadow-md"
                    />
                  </form>
                </div>
              )}
            </div>
          </div>
        ) : (
          // Regular layout after messages are sent
          <>
            <div className="flex-1 overflow-y-auto relative">
              <div className="absolute inset-0">
                <Messages
                  chatId={id}
                  isLoading={isLoading}
                  votes={votes}
                  messages={messages}
                  setMessages={setMessages}
                  reload={reload}
                  isReadonly={isReadonly}
                  isArtifactVisible={isArtifactVisible}
                  append={append}
                />
              </div>
            </div>

            {!isReadonly && (
              <div className="sticky z-10 w-full">
                <form
                  className="flex mx-auto px-4 md:px-6 bg-gradient-to-t from-background/95 via-background/90 to-background/80 backdrop-blur-md pb-3 md:pb-4 pt-2 gap-2 w-full max-w-full md:max-w-4xl border-t border-border/20 shadow-[0_-4px_20px_rgba(0,0,0,0.1)]"
                  onSubmit={onFormSubmit}
                >
                  <MultimodalInput
                    chatId={id}
                    input={input}
                    setInput={setInput}
                    handleSubmit={handleSubmit}
                    isLoading={isLoading}
                    stop={stop}
                    attachments={attachments}
                    setAttachments={setAttachments}
                    messages={messages}
                    setMessages={setMessages}
                    append={append}
                  />
                </form>
              </div>
            )}
          </>
        )}
      </div>

      {isArtifactVisible && (
        <div className="border-l border-border/20 h-full">
          <Artifact
            chatId={id}
            input={input}
            setInput={setInput}
            handleSubmit={handleSubmit}
            isLoading={isLoading}
            stop={stop}
            attachments={attachments}
            setAttachments={setAttachments}
            append={append}
            messages={messages}
            setMessages={setMessages}
            reload={reload}
            votes={votes}
            isReadonly={isReadonly}
          />
        </div>
      )}
    </>
  );
}
