{"name": "front", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "prisma db push && next build", "start": "next start", "lint": "next lint", "postinstall": "npx prisma generate", "db:migrate": "npx prisma migrate deploy", "db:reset": "npx prisma migrate reset --force", "db:seed": "node scripts/seed-database.js", "db:check-plans": "node scripts/check-plans.js", "db:update-limits": "node scripts/update-plan-limits.js", "db:setup": "npm run db:migrate && npm run db:seed"}, "dependencies": {"@ai-sdk/anthropic": "1.2.10", "@ai-sdk/groq": "^1.2.9", "@clerk/nextjs": "^6.13.0", "@codemirror/lang-python": "^6.2.0", "@codemirror/state": "^6.5.2", "@codemirror/theme-one-dark": "^6.1.2", "@codemirror/view": "^6.36.8", "@hookform/resolvers": "^5.0.1", "@lemonsqueezy/lemonsqueezy.js": "^4.0.0", "@lemonsqueezy/wedges": "^1.4.0", "@paddle/paddle-js": "^1.4.2", "@pdf-lib/fontkit": "^1.1.1", "@polar-sh/nextjs": "^0.3.23", "@polar-sh/sdk": "^0.32.7", "@prisma/client": "6.5.0", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "@react-email/components": "^0.0.36", "@react-email/tailwind": "^1.0.4", "@react-pdf/renderer": "^4.3.0", "@ricky0123/vad-react": "^0.0.28", "@tanstack/react-table": "^8.21.3", "@types/fs-extra": "^11.0.4", "@types/jspdf": "^2.0.0", "@vercel/analytics": "^1.5.0", "@vercel/blob": "^0.23.4", "@vercel/speed-insights": "^1.2.0", "ai": "^4.2.8", "apexcharts": "^4.5.0", "chart.js": "^4.4.8", "chartjs-node-canvas": "^5.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cobe": "^0.6.3", "codemirror": "^6.0.1", "cron-parser": "^5.1.1", "cronstrue": "^2.58.0", "csv-writer": "^1.6.0", "date-fns": "^4.1.0", "diff-match-patch": "^1.0.5", "exceljs": "^4.4.0", "exif-js": "^2.3.0", "fast-deep-equal": "^3.1.3", "fs-extra": "^11.3.0", "google-auth-library": "^9.15.1", "googleapis": "^148.0.0", "html-pdf-node": "^1.0.8", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.485.0", "motion": "^12.6.2", "nanoid": "^5.0.6", "next": "15.2.4", "next-themes": "^0.4.6", "onnxruntime-web": "^1.21.1", "orderedmap": "^2.1.1", "papaparse": "^5.5.2", "pdfkit": "^0.16.0", "prisma": "^6.5.0", "prosemirror": "^0.11.1", "prosemirror-example-setup": "^1.2.3", "prosemirror-inputrules": "^1.5.0", "prosemirror-markdown": "^1.13.2", "prosemirror-model": "^1.25.1", "prosemirror-schema-basic": "^1.2.4", "prosemirror-schema-list": "^1.5.1", "prosemirror-state": "^1.4.3", "prosemirror-view": "^1.39.2", "react": "^19.0.0", "react-apexcharts": "^1.7.0", "react-chartjs-2": "^5.3.0", "react-data-grid": "7.0.0-beta.52", "react-day-picker": "8.10.1", "react-dom": "^19.0.0", "react-hook-form": "^7.56.1", "react-markdown": "^10.1.0", "react-textarea-autosize": "^8.5.9", "recharts": "^2.12.0", "remark-gfm": "^4.0.1", "resend": "^4.2.0", "rimraf": "^6.0.1", "sharp": "^0.34.1", "sonner": "^2.0.2", "svix": "^1.64.1", "swr": "^2.3.3", "tailwind-merge": "^3.0.2", "tesseract.js": "^6.0.1", "tw-animate-css": "^1.2.5", "usehooks-ts": "^3.1.1", "uuid": "^11.1.0", "xlsx": "^0.18.5", "zod": "^3.24.2", "zod-form-data": "^2.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@tailwindcss/typography": "^0.5.16", "@types/node": "^20", "@types/papaparse": "^5.3.16", "@types/pdfkit": "^0.13.9", "@types/react": "^19", "@types/react-dom": "^19", "@types/recharts": "^1.8.29", "eslint": "^9", "eslint-config-next": "15.2.4", "tailwindcss": "^4", "typescript": "^5"}}