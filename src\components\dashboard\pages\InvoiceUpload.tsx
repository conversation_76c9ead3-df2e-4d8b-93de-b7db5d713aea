"use client";

import { useState, useEffect } from "react";
import DashboardLayout from "@/components/layout/DashboardLayout";
import { Card, CardContent } from "@/components/ui/card";
import { getUserIdFromClerkId } from "@/lib/actions/user";
import { useRouter } from "next/navigation";
import { useAuth } from "@clerk/nextjs";
import { FileUp, Inbox, Sparkles } from "lucide-react";
import EmailIntegrationTab from "@/components/dashboard/settings/email-integration-tab";
import AIFeaturesCard from "@/components/dashboard/invoice/AIFeaturesCard";
import { InvoiceUploader } from "@/components/dashboard/pages/upload/invoice-uploader";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useRef } from "react";
import { useToast } from "@/components/ui/use-toast";

// Global state to store email files temporarily
const pendingEmailFiles: File[] = [];

const InvoiceUpload = () => {
  const [dbUserId, setDbUserId] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("file");
  const { userId: clerkUserId } = useAuth();
  const router = useRouter();
  const { toast } = useToast();
  
  // Create ref to store invoice uploader instance for communication between tabs
  const invoiceUploaderRef = useRef<{
    handleFilesFromEmail: (files: File[]) => void;
  } | null>(null);

  useEffect(() => {
    const fetchUserId = async () => {
      if (!clerkUserId) return;
      
      try {
        // Call server action instead of db directly
        const userId = await getUserIdFromClerkId(clerkUserId);
        if (userId) {
          setDbUserId(userId);
        }
      } catch (error) {
        console.error("Error fetching user ID:", error);
      }
    };
    
    fetchUserId();
  }, [clerkUserId]);

  // Process any pending email files when the ref becomes available
  useEffect(() => {
    // Check if there are pending files and the ref is available
    if (pendingEmailFiles.length > 0 && invoiceUploaderRef.current) {
      console.log(`Processing ${pendingEmailFiles.length} pending email files`);
      invoiceUploaderRef.current.handleFilesFromEmail([...pendingEmailFiles]);
      
      // Clear the pending files after processing
      pendingEmailFiles.length = 0;
      
      // Switch to file tab
      setActiveTab("file");
    }
  }, [activeTab]); // Check when tabs change or component remounts

  const navigateToAISettings = () => {
    router.push("/dashboard/settings/ai");
  };

  // Handle email attachments being received
  const handleEmailAttachments = (files: File[]) => {
    console.log("InvoiceUpload received email attachments", files.length, "files");
    
    // Try to forward the files to the InvoiceUploader component
    if (invoiceUploaderRef.current) {
      console.log("Forwarding email files to InvoiceUploader ref");
      
      // Switch to file tab first to ensure the component is mounted
      setActiveTab("file");
      
      // Use a small timeout to ensure the tab switch completes
      setTimeout(() => {
        if (invoiceUploaderRef.current) {
          invoiceUploaderRef.current.handleFilesFromEmail(files);
        } else {
          console.log("Ref still not available after timeout, storing files for later");
          // Store the files for later processing
          pendingEmailFiles.push(...files);
          toast({
            title: "Files ready for processing",
            description: "Switch to the File Upload tab to process your attachments"
          });
        }
      }, 100);
    } else {
      console.log("InvoiceUploader ref is not available, storing files for later");
      // Store the files for later processing
      pendingEmailFiles.push(...files);
      
      toast({
        title: "Files ready for processing",
        description: "Switch to the File Upload tab to process your attachments"
      });
    }
  };

  return (
    <DashboardLayout>
      <div className="flex flex-col space-y-6">
        {/* Header with advanced options */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Invoice Management</h1>
            <p className="text-muted-foreground">
              Upload, process and manage your invoices with AI assistance
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={() => router.push("/dashboard/invoices")}>
              <Inbox className="mr-2 h-4 w-4" />
              View All Invoices
            </Button>
            <Button variant="outline" onClick={navigateToAISettings}>
              <Sparkles className="mr-2 h-4 w-4" />
              AI Settings
            </Button>
          </div>
        </div>
        
        <Separator />
        
        {/* Main content area */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-6">
            <Card className="border shadow-sm overflow-hidden">
              <Tabs 
                value={activeTab} 
                onValueChange={setActiveTab} 
                className="w-full"
              >
                <TabsList className="w-full p-0 bg-muted/20 rounded-none">
                  <TabsTrigger 
                    value="file" 
                    className="flex-1 rounded-none data-[state=active]:bg-background border-r border-border/30"
                  >
                    <FileUp className="h-4 w-4 mr-2" />
                    File Upload
                  </TabsTrigger>
                  <TabsTrigger 
                    value="email" 
                    className="flex-1 rounded-none data-[state=active]:bg-background"
                  >
                    <Inbox className="h-4 w-4 mr-2" />
                    Email Integration
                  </TabsTrigger>
                </TabsList>

                <CardContent className="p-0">
                  <TabsContent value="file" className="mt-0 p-6">
                    <InvoiceUploader ref={invoiceUploaderRef} />
                  </TabsContent>

                  <TabsContent value="email" className="mt-0 p-6">
                    {dbUserId ? (
                      <EmailIntegrationTab 
                        userId={dbUserId} 
                        provider="gmail" 
                        onEmailAttachmentsReceived={handleEmailAttachments}
                      />
                    ) : (
                      <div className="p-4 text-center">
                        Fetching user details... Please wait.
                      </div>
                    )}
                  </TabsContent>
                </CardContent>
              </Tabs>
            </Card>
          </div>

          <div>
            <AIFeaturesCard onConfigureAI={navigateToAISettings} />
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default InvoiceUpload;
