import ExcelJS from "exceljs";
import type { Report, ReportData } from "@prisma/client";

interface ReportDataItem extends Omit<ReportData, 'id' | 'createdAt' | 'reportId' | 'invoiceId'> {
  id?: string;
  reportId?: string;
  invoiceId?: string | null;
  language?: string; // Add language field for RTL support
}

interface ReportWithData extends Report {
  data: ReportDataItem[];
  language?: string; // Add language field for RTL support
}

// Helper function to format dates in a clean format (keeping numbers in English)
function formatDate(date: Date | string): string {
  const dateObj = date instanceof Date ? date : new Date(date);
  
  // Use en-US for number formatting
  const options: Intl.DateTimeFormatOptions = { 
    year: 'numeric', 
    month: '2-digit', 
    day: '2-digit'
  };
  
  // Always use en-US for dates to keep numbers in English format
  return dateObj.toLocaleDateString('en-US', options);
}


/**
 * Generates an Excel report based on report data
 * @param report The report with data items
 * @param includeCompanyBranding Whether to include company branding
 * @returns Buffer containing the Excel data
 */
export async function generateReportExcel(
  report: ReportWithData,
  includeCompanyBranding = true
): Promise<Buffer> {
  const workbook = new ExcelJS.Workbook();
  workbook.creator = "Billix Reporting System";
  workbook.created = new Date();
  
  // Detect if Arabic language is being used
  const isArabic = report.language === "ar" || report.data.some(item => item.language === "ar");
  
  const summarySheet = workbook.addWorksheet("Summary", {
    properties: { tabColor: { argb: "3C5A99" } },
  });
  
  if (includeCompanyBranding) {
    addBranding(summarySheet);
  }
  
  summarySheet.mergeCells("A2:F2");
  const titleCell = summarySheet.getCell("A2");
  titleCell.value = report.title;
  titleCell.font = { size: 16, bold: true };
  titleCell.alignment = { horizontal: "center" };
  
  summarySheet.getCell("A4").value = "Report Type:";
  summarySheet.getCell("B4").value = formatReportType(report.reportType);
  summarySheet.getCell("A5").value = "Generated On:";
  summarySheet.getCell("B5").value = formatDate(new Date());
  summarySheet.getCell("A6").value = "Date Range:";
  summarySheet.getCell("B6").value = "Based on when invoices were added to the system";
  
  // Apply RTL alignment for Arabic
  if (isArabic) {
    [titleCell, summarySheet.getCell("B4"), summarySheet.getCell("B5"), summarySheet.getCell("B6")].forEach(cell => {
      cell.alignment = {
        ...cell.alignment,
        readingOrder: 'rtl',
        horizontal: 'right'
      };
    });
  }
  
  if (report.description) {
    summarySheet.mergeCells("A8:F8");
    const descriptionTitleCell = summarySheet.getCell("A8");
    descriptionTitleCell.value = "Description";
    descriptionTitleCell.font = { size: 12, bold: true };
    
    summarySheet.mergeCells("A9:F11");
    const descriptionCell = summarySheet.getCell("A9");
    descriptionCell.value = report.description;
    descriptionCell.alignment = { wrapText: true };
    
    if (isArabic) {
      descriptionCell.alignment = {
        ...descriptionCell.alignment,
        readingOrder: 'rtl',
        horizontal: 'right'
      };
    }
  }
  
  switch (report.reportType) {
    case "EXPENSES":
      addExpensesReport(workbook, report, isArabic);
      break;
    case "VENDOR_ANALYSIS":
      addVendorAnalysisReport(workbook, report, isArabic);
      break;
    case "CATEGORY_ANALYSIS":
      addCategoryAnalysisReport(workbook, report, isArabic);
      break;
    case "CASH_FLOW":
      addCashFlowReport(workbook, report, isArabic);
      break;
    default:
      addDefaultReport(workbook, report, isArabic);
  }
  
  workbook.eachSheet((sheet) => {
    if (sheet.columns) {
      sheet.columns.forEach((column) => {
        if (column && column.eachCell) {
          let maxLength = 0;
          column.eachCell({ includeEmpty: false }, (cell) => {
            const columnLength = cell.value ? cell.value.toString().length : 10;
            if (columnLength > maxLength) {
              maxLength = columnLength;
            }
          });
          column.width = Math.min(maxLength + 2, 30);
        }
      });
    }
  });
  
  const buffer = await workbook.xlsx.writeBuffer() as unknown as Buffer;
  return buffer;
}

/**
 * Adds company branding to the worksheet
 */
function addBranding(sheet: ExcelJS.Worksheet): void {
  sheet.mergeCells("A1:B1");
  const logoCell = sheet.getCell("A1");
  logoCell.value = "BILLIX";
  logoCell.font = { size: 14, bold: true, color: { argb: "FFFFFF" } };
  logoCell.fill = {
    type: "pattern",
    pattern: "solid",
    fgColor: { argb: "3C5A99" }, // Brand color
  };
  logoCell.alignment = { horizontal: "center", vertical: "middle" };
  
  for (let col = 1; col <= 10; col++) {
    const cell = sheet.getCell(1, col);
    if (col > 2) {
      cell.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: "DDDDDD" },
      };
    }
    cell.border = {
      bottom: { style: "thin", color: { argb: "BBBBBB" } },
    };
  }
}

/**
 * Formats the report type for display
 */
function formatReportType(type: string): string {
  switch (type) {
    case "EXPENSES":
      return "Expenses Report";
    case "VENDOR_ANALYSIS":
      return "Vendor Analysis";
    case "CATEGORY_ANALYSIS":
      return "Category Analysis";
    case "CASH_FLOW":
      return "Cash Flow Analysis";
    default:
      return "Custom Report";
  }
}

/**
 * Adds expenses report data to the workbook
 */
function addExpensesReport(workbook: ExcelJS.Workbook, report: ReportWithData, isArabic: boolean): void {
  const expenseData = report.data.filter(item => item.dataPoint === "expense");
  const dailyExpenseData = report.data.filter(item => item.dataPoint === "daily_expense");
  const averageExpense = report.data.find(item => item.dataPoint === "average_expense");
  
  const summarySheet = workbook.getWorksheet("Summary");
  if (!summarySheet) return;
  
  let rowIndex = summarySheet.lastRow ? summarySheet.lastRow.number + 2 : 12;
  
  summarySheet.mergeCells(`A${rowIndex}:F${rowIndex}`);
  const summaryTitleCell = summarySheet.getCell(`A${rowIndex}`);
  summaryTitleCell.value = "Expense Summary";
  summaryTitleCell.font = { size: 14, bold: true };
  rowIndex += 1;
  
  const totalExpenses = expenseData.reduce((sum, item) => sum + item.value, 0);
  summarySheet.getCell(`A${rowIndex}`).value = "Total Expenses:";
  summarySheet.getCell(`B${rowIndex}`).value = totalExpenses;
  summarySheet.getCell(`B${rowIndex}`).numFmt = "$#,##0.00";
  
  // Apply RTL alignment for Arabic
  if (isArabic) {
    summarySheet.getCell(`B${rowIndex}`).alignment = { readingOrder: 'rtl', horizontal: 'right' };
  }
  
  rowIndex += 1;
  
  if (averageExpense) {
    summarySheet.getCell(`A${rowIndex}`).value = "Average Expense:";
    summarySheet.getCell(`B${rowIndex}`).value = averageExpense.value;
    summarySheet.getCell(`B${rowIndex}`).numFmt = "$#,##0.00";
    
    // Apply RTL alignment for Arabic
    if (isArabic) {
      summarySheet.getCell(`B${rowIndex}`).alignment = { readingOrder: 'rtl', horizontal: 'right' };
    }
    
    rowIndex += 1;
  }
  
  summarySheet.getCell(`A${rowIndex}`).value = "Number of Expenses:";
  summarySheet.getCell(`B${rowIndex}`).value = expenseData.length;
  
  // Apply RTL alignment for Arabic
  if (isArabic) {
    summarySheet.getCell(`B${rowIndex}`).alignment = { readingOrder: 'rtl', horizontal: 'right' };
  }
  
  if (expenseData.length > 0) {
    const detailsSheet = workbook.addWorksheet("Expense Details", {
      properties: { tabColor: { argb: "5B9BD5" } },
      views: [{ rightToLeft: isArabic }]
    });
    
    detailsSheet.columns = [
      { header: "Date", key: "date" },
      { header: "Category", key: "category" },
      { header: "Amount", key: "amount" },
    ];
    
    const headerRow = detailsSheet.getRow(1);
    headerRow.font = { bold: true, color: { argb: "FFFFFF" } };
    headerRow.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "3C5A99" },
    };
    
    expenseData.forEach((item, index) => {
      const newRow = detailsSheet.addRow({
        date: item.label,
        category: item.category,
        amount: item.value,
      });
      
      detailsSheet.getCell(`C${index + 2}`).numFmt = "$#,##0.00";
      
      // Apply RTL alignment for Arabic
      if (isArabic) {
        newRow.eachCell((cell) => {
          cell.alignment = {
            readingOrder: 'rtl',
            horizontal: 'right'
          };
        });
      }
    });
  }
  
  if (dailyExpenseData.length > 0) {
    const dailySheet = workbook.addWorksheet("Daily Expenses", {
      properties: { tabColor: { argb: "A5A5A5" } },
      views: [{ rightToLeft: isArabic }]
    });
    
    dailySheet.columns = [
      { header: "Date", key: "date" },
      { header: "Total Amount", key: "amount" },
      { header: "Number of Invoices", key: "count" },
    ];
    
    const headerRow = dailySheet.getRow(1);
    headerRow.font = { bold: true, color: { argb: "FFFFFF" } };
    headerRow.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "3C5A99" },
    };
    
    dailyExpenseData.forEach((item, index) => {
      const dayCount = expenseData.filter(exp => exp.label === item.label).length;
      const newRow = dailySheet.addRow({
        date: item.label,
        amount: item.value,
        count: dayCount,
      });
      
      dailySheet.getCell(`B${index + 2}`).numFmt = "$#,##0.00";
      
      // Apply RTL alignment for Arabic
      if (isArabic) {
        newRow.eachCell((cell) => {
          cell.alignment = {
            readingOrder: 'rtl',
            horizontal: 'right'
          };
        });
      }
    });
  }
}

/**
 * Adds vendor analysis report data to the workbook
 */
function addVendorAnalysisReport(workbook: ExcelJS.Workbook, report: ReportWithData, isArabic: boolean): void {
  const vendorTotalData = report.data.filter(item => item.dataPoint === "vendor_total");
  const vendorCountData = report.data.filter(item => item.dataPoint === "vendor_count");
  const vendorAverageData = report.data.filter(item => item.dataPoint === "vendor_average");
  const topVendorData = report.data.filter(item => item.dataPoint === "top_vendor");
  
  const summarySheet = workbook.getWorksheet("Summary");
  if (!summarySheet) return;
  
  let rowIndex = summarySheet.lastRow ? summarySheet.lastRow.number + 2 : 12;
  
  summarySheet.mergeCells(`A${rowIndex}:F${rowIndex}`);
  const summaryTitleCell = summarySheet.getCell(`A${rowIndex}`);
  summaryTitleCell.value = "Vendor Analysis Summary";
  summaryTitleCell.font = { size: 14, bold: true };
  rowIndex += 1;
  
  const uniqueVendors = new Set(vendorTotalData.map(item => item.label));
  summarySheet.getCell(`A${rowIndex}`).value = "Total Vendors:";
  summarySheet.getCell(`B${rowIndex}`).value = uniqueVendors.size;
  
  // Apply RTL alignment for Arabic
  if (isArabic) {
    summarySheet.getCell(`B${rowIndex}`).alignment = { readingOrder: 'rtl', horizontal: 'right' };
  }
  
  rowIndex += 1;
  
  const totalSpend = vendorTotalData.reduce((sum, item) => sum + item.value, 0);
  summarySheet.getCell(`A${rowIndex}`).value = "Total Spend:";
  summarySheet.getCell(`B${rowIndex}`).value = totalSpend;
  summarySheet.getCell(`B${rowIndex}`).numFmt = "$#,##0.00";
  
  // Apply RTL alignment for Arabic
  if (isArabic) {
    summarySheet.getCell(`B${rowIndex}`).alignment = { readingOrder: 'rtl', horizontal: 'right' };
  }
  
  if (topVendorData.length > 0) {
    const topVendorsSheet = workbook.addWorksheet("Top Vendors", {
      properties: { tabColor: { argb: "5B9BD5" } },
      views: [{ rightToLeft: isArabic }]
    });
    
    topVendorsSheet.columns = [
      { header: "Rank", key: "rank" },
      { header: "Vendor", key: "vendor" },
      { header: "Total Spend", key: "spend" },
      { header: "% of Total", key: "percentage" },
    ];
    
    const headerRow = topVendorsSheet.getRow(1);
    headerRow.font = { bold: true, color: { argb: "FFFFFF" } };
    headerRow.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "3C5A99" },
    };
    
    topVendorData.forEach((item, index) => {
      const percentage = (item.value / totalSpend) * 100;
      const labelParts = item.label ? item.label.split(":") : ["", "Unknown"];
      
      const newRow = topVendorsSheet.addRow({
        rank: labelParts[0],
        vendor: labelParts[1] ? labelParts[1].trim() : "Unknown",
        spend: item.value,
        percentage: percentage,
      });
      
      topVendorsSheet.getCell(`C${index + 2}`).numFmt = "$#,##0.00";
      topVendorsSheet.getCell(`D${index + 2}`).numFmt = "0.00%";
      
      // Apply RTL alignment for Arabic
      if (isArabic) {
        newRow.eachCell((cell) => {
          cell.alignment = {
            readingOrder: 'rtl',
            horizontal: 'right'
          };
        });
      }
    });
  }
  
  if (vendorTotalData.length > 0) {
    const vendorDetailsSheet = workbook.addWorksheet("Vendor Details", {
      properties: { tabColor: { argb: "A5A5A5" } },
      views: [{ rightToLeft: isArabic }]
    });
    
    vendorDetailsSheet.columns = [
      { header: "Vendor", key: "vendor" },
      { header: "Total Spend", key: "spend" },
      { header: "Invoice Count", key: "count" },
      { header: "Average Invoice", key: "average" },
    ];
    
    const headerRow = vendorDetailsSheet.getRow(1);
    headerRow.font = { bold: true, color: { argb: "FFFFFF" } };
    headerRow.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "3C5A99" },
    };
    
    vendorTotalData.forEach((item, index) => {
      const count = vendorCountData[index]?.value || 0;
      const average = vendorAverageData[index]?.value || 0;
      
      const newRow = vendorDetailsSheet.addRow({
        vendor: item.label,
        spend: item.value,
        count: count,
        average: average,
      });
      
      vendorDetailsSheet.getCell(`B${index + 2}`).numFmt = "$#,##0.00";
      vendorDetailsSheet.getCell(`D${index + 2}`).numFmt = "$#,##0.00";
      
      // Apply RTL alignment for Arabic
      if (isArabic) {
        newRow.eachCell((cell) => {
          cell.alignment = {
            readingOrder: 'rtl',
            horizontal: 'right'
          };
        });
      }
    });
  }
}

/**
 * Adds category analysis report data to the workbook
 */
function addCategoryAnalysisReport(workbook: ExcelJS.Workbook, report: ReportWithData, isArabic: boolean): void {
  const categoryTotalData = report.data.filter(item => item.dataPoint === "category_total");
  const categoryPercentageData = report.data.filter(item => item.dataPoint === "category_percentage");
  const categoryCountData = report.data.filter(item => item.dataPoint === "category_count");
  const categoryAverageData = report.data.filter(item => item.dataPoint === "category_average");
  const topCategoryData = report.data.filter(item => item.dataPoint === "top_category");
  
  const summarySheet = workbook.getWorksheet("Summary");
  if (!summarySheet) return;
  
  let rowIndex = summarySheet.lastRow ? summarySheet.lastRow.number + 2 : 12;
  
  summarySheet.mergeCells(`A${rowIndex}:F${rowIndex}`);
  const summaryTitleCell = summarySheet.getCell(`A${rowIndex}`);
  summaryTitleCell.value = "Category Analysis Summary";
  summaryTitleCell.font = { size: 14, bold: true };
  rowIndex += 1;
  
  const uniqueCategories = new Set(categoryTotalData.map(item => item.label));
  summarySheet.getCell(`A${rowIndex}`).value = "Total Categories:";
  summarySheet.getCell(`B${rowIndex}`).value = uniqueCategories.size;
  
  // Apply RTL alignment for Arabic
  if (isArabic) {
    summarySheet.getCell(`B${rowIndex}`).alignment = { readingOrder: 'rtl', horizontal: 'right' };
  }
  
  rowIndex += 1;
  
  const totalSpend = categoryTotalData.reduce((sum, item) => sum + item.value, 0);
  summarySheet.getCell(`A${rowIndex}`).value = "Total Spend:";
  summarySheet.getCell(`B${rowIndex}`).value = totalSpend;
  summarySheet.getCell(`B${rowIndex}`).numFmt = "$#,##0.00";
  
  // Apply RTL alignment for Arabic
  if (isArabic) {
    summarySheet.getCell(`B${rowIndex}`).alignment = { readingOrder: 'rtl', horizontal: 'right' };
  }
  
  if (topCategoryData.length > 0) {
    const topCategoriesSheet = workbook.addWorksheet("Top Categories", {
      properties: { tabColor: { argb: "5B9BD5" } },
      views: [{ rightToLeft: isArabic }]
    });
    
    topCategoriesSheet.columns = [
      { header: "Rank", key: "rank" },
      { header: "Category", key: "category" },
      { header: "Total Spend", key: "spend" },
      { header: "% of Total", key: "percentage" },
    ];
    
    const headerRow = topCategoriesSheet.getRow(1);
    headerRow.font = { bold: true, color: { argb: "FFFFFF" } };
    headerRow.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "3C5A99" },
    };
    
    topCategoryData.forEach((item, index) => {
      const percentage = (item.value / totalSpend) * 100;
      const labelParts = item.label ? item.label.split(":") : ["", "Unknown"];
      
      const newRow = topCategoriesSheet.addRow({
        rank: labelParts[0],
        category: labelParts[1] ? labelParts[1].trim() : "Unknown",
        spend: item.value,
        percentage: percentage,
      });
      
      topCategoriesSheet.getCell(`C${index + 2}`).numFmt = "$#,##0.00";
      topCategoriesSheet.getCell(`D${index + 2}`).numFmt = "0.00%";
      
      // Apply RTL alignment for Arabic
      if (isArabic) {
        newRow.eachCell((cell) => {
          cell.alignment = {
            readingOrder: 'rtl',
            horizontal: 'right'
          };
        });
      }
    });
  }
  
  if (categoryTotalData.length > 0) {
    const categoryDetailsSheet = workbook.addWorksheet("Category Details", {
      properties: { tabColor: { argb: "A5A5A5" } },
      views: [{ rightToLeft: isArabic }]
    });
    
    categoryDetailsSheet.columns = [
      { header: "Category", key: "category" },
      { header: "Total Spend", key: "spend" },
      { header: "% of Total", key: "percentage" },
      { header: "Invoice Count", key: "count" },
      { header: "Average Invoice", key: "average" },
    ];
    
    const headerRow = categoryDetailsSheet.getRow(1);
    headerRow.font = { bold: true, color: { argb: "FFFFFF" } };
    headerRow.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "3C5A99" },
    };
    
    categoryTotalData.forEach((item, index) => {
      const percentage = categoryPercentageData[index]?.value || 0;
      const count = categoryCountData[index]?.value || 0;
      const average = categoryAverageData[index]?.value || 0;
      
      const newRow = categoryDetailsSheet.addRow({
        category: item.label,
        spend: item.value,
        percentage: percentage,
        count: count,
        average: average,
      });
      
      categoryDetailsSheet.getCell(`B${index + 2}`).numFmt = "$#,##0.00";
      categoryDetailsSheet.getCell(`C${index + 2}`).numFmt = "0.00%";
      categoryDetailsSheet.getCell(`E${index + 2}`).numFmt = "$#,##0.00";
      
      // Apply RTL alignment for Arabic
      if (isArabic) {
        newRow.eachCell((cell) => {
          cell.alignment = {
            readingOrder: 'rtl',
            horizontal: 'right'
          };
        });
      }
    });
  }
}

/**
 * Adds cash flow report data to the workbook
 */
function addCashFlowReport(workbook: ExcelJS.Workbook, report: ReportWithData, isArabic: boolean): void {
  const monthlyCashFlowData = report.data.filter(item => item.dataPoint === "monthly_cashflow");
  const cumulativeCashFlowData = report.data.filter(item => item.dataPoint === "cumulative_cashflow");
  const monthlyChangeData = report.data.filter(item => item.dataPoint === "monthly_change");
  const monthlyChangePercentData = report.data.filter(item => item.dataPoint === "monthly_change_percent");
  const quarterlyCashFlowData = report.data.filter(item => item.dataPoint === "quarterly_cashflow");
  
  const summarySheet = workbook.getWorksheet("Summary");
  if (!summarySheet) return;
  
  let rowIndex = summarySheet.lastRow ? summarySheet.lastRow.number + 2 : 12;
  
  summarySheet.mergeCells(`A${rowIndex}:F${rowIndex}`);
  const summaryTitleCell = summarySheet.getCell(`A${rowIndex}`);
  summaryTitleCell.value = "Cash Flow Summary";
  summaryTitleCell.font = { size: 14, bold: true };
  rowIndex += 1;
  
  const totalCashFlow = monthlyCashFlowData.reduce((sum, item) => sum + item.value, 0);
  summarySheet.getCell(`A${rowIndex}`).value = "Total Cash Flow:";
  summarySheet.getCell(`B${rowIndex}`).value = totalCashFlow;
  summarySheet.getCell(`B${rowIndex}`).numFmt = "$#,##0.00";
  
  // Apply RTL alignment for Arabic
  if (isArabic) {
    summarySheet.getCell(`B${rowIndex}`).alignment = { readingOrder: 'rtl', horizontal: 'right' };
  }
  
  rowIndex += 1;
  
  const avgMonthlyCashFlow = monthlyCashFlowData.length > 0 
    ? totalCashFlow / monthlyCashFlowData.length 
    : 0;
  summarySheet.getCell(`A${rowIndex}`).value = "Average Monthly Cash Flow:";
  summarySheet.getCell(`B${rowIndex}`).value = avgMonthlyCashFlow;
  summarySheet.getCell(`B${rowIndex}`).numFmt = "$#,##0.00";
  
  // Apply RTL alignment for Arabic
  if (isArabic) {
    summarySheet.getCell(`B${rowIndex}`).alignment = { readingOrder: 'rtl', horizontal: 'right' };
  }
  
  if (monthlyCashFlowData.length > 0) {
    const monthlySheet = workbook.addWorksheet("Monthly Cash Flow", {
      properties: { tabColor: { argb: "5B9BD5" } },
      views: [{ rightToLeft: isArabic }]
    });
    
    monthlySheet.columns = [
      { header: "Month", key: "month" },
      { header: "Cash Flow", key: "cashflow" },
      { header: "Cumulative", key: "cumulative" },
      { header: "Change", key: "change" },
      { header: "Change %", key: "changePercent" },
    ];
    
    const headerRow = monthlySheet.getRow(1);
    headerRow.font = { bold: true, color: { argb: "FFFFFF" } };
    headerRow.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "3C5A99" },
    };
    
    monthlyCashFlowData.forEach((item, index) => {
      const cumulative = cumulativeCashFlowData[index]?.value || 0;
      const change = monthlyChangeData[index]?.value || 0;
      const changePercent = monthlyChangePercentData[index]?.value || 0;
      
      const newRow = monthlySheet.addRow({
        month: item.label,
        cashflow: item.value,
        cumulative: cumulative,
        change: change,
        changePercent: changePercent,
      });
      
      monthlySheet.getCell(`B${index + 2}`).numFmt = "$#,##0.00";
      monthlySheet.getCell(`C${index + 2}`).numFmt = "$#,##0.00";
      monthlySheet.getCell(`D${index + 2}`).numFmt = "$#,##0.00";
      monthlySheet.getCell(`E${index + 2}`).numFmt = "0.00%";
      
      // Apply RTL alignment for Arabic
      if (isArabic) {
        newRow.eachCell((cell) => {
          cell.alignment = {
            readingOrder: 'rtl',
            horizontal: 'right'
          };
        });
      }
    });
  }
  
  if (quarterlyCashFlowData.length > 0) {
    const quarterlySheet = workbook.addWorksheet("Quarterly Cash Flow", {
      properties: { tabColor: { argb: "A5A5A5" } },
      views: [{ rightToLeft: isArabic }]
    });
    
    quarterlySheet.columns = [
      { header: "Quarter", key: "quarter" },
      { header: "Cash Flow", key: "cashflow" },
      { header: "% of Total", key: "percentage" },
    ];
    
    const headerRow = quarterlySheet.getRow(1);
    headerRow.font = { bold: true, color: { argb: "FFFFFF" } };
    headerRow.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "3C5A99" },
    };
    
    quarterlyCashFlowData.forEach((item, index) => {
      const percentage = (item.value / totalCashFlow) * 100;
      
      const newRow = quarterlySheet.addRow({
        quarter: item.label,
        cashflow: item.value,
        percentage: percentage,
      });
      
      quarterlySheet.getCell(`B${index + 2}`).numFmt = "$#,##0.00";
      quarterlySheet.getCell(`C${index + 2}`).numFmt = "0.00%";
      
      // Apply RTL alignment for Arabic
      if (isArabic) {
        newRow.eachCell((cell) => {
          cell.alignment = {
            readingOrder: 'rtl',
            horizontal: 'right'
          };
        });
      }
    });
  }
}

/**
 * Adds default report data to the workbook
 */
function addDefaultReport(workbook: ExcelJS.Workbook, report: ReportWithData, isArabic: boolean): void {
  const basicData = report.data.filter(item => item.dataPoint === "amount");
  const summaryData = report.data.filter(item => 
    ["total_amount", "average_amount", "invoice_count", "max_amount"].includes(item.dataPoint)
  );
  
  const summarySheet = workbook.getWorksheet("Summary");
  if (!summarySheet) return;
  
  let rowIndex = summarySheet.lastRow ? summarySheet.lastRow.number + 2 : 12;
  
  summarySheet.mergeCells(`A${rowIndex}:F${rowIndex}`);
  const summaryTitleCell = summarySheet.getCell(`A${rowIndex}`);
  summaryTitleCell.value = "Report Summary";
  summaryTitleCell.font = { size: 14, bold: true };
  rowIndex += 1;
  
  summaryData.forEach(item => {
    summarySheet.getCell(`A${rowIndex}`).value = item.label + ":";
    summarySheet.getCell(`B${rowIndex}`).value = item.value;
    
    if (item.dataPoint === "total_amount" || item.dataPoint === "average_amount" || item.dataPoint === "max_amount") {
      summarySheet.getCell(`B${rowIndex}`).numFmt = "$#,##0.00";
    }
    
    // Apply RTL alignment for Arabic
    if (isArabic) {
      summarySheet.getCell(`B${rowIndex}`).alignment = { readingOrder: 'rtl', horizontal: 'right' };
    }
    
    rowIndex += 1;
  });
  
  if (basicData.length > 0) {
    const dataSheet = workbook.addWorksheet("Data Details", {
      properties: { tabColor: { argb: "5B9BD5" } },
      views: [{ rightToLeft: isArabic }]
    });
    
    dataSheet.columns = [
      { header: "Invoice", key: "invoice" },
      { header: "Category", key: "category" },
      { header: "Amount", key: "amount" },
    ];
    
    const headerRow = dataSheet.getRow(1);
    headerRow.font = { bold: true, color: { argb: "FFFFFF" } };
    headerRow.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "3C5A99" },
    };
    
    basicData.forEach((item, index) => {
      const newRow = dataSheet.addRow({
        invoice: item.label,
        category: item.category,
        amount: item.value,
      });
      
      dataSheet.getCell(`C${index + 2}`).numFmt = "$#,##0.00";
      
      // Apply RTL alignment for Arabic
      if (isArabic) {
        newRow.eachCell((cell) => {
          cell.alignment = {
            readingOrder: 'rtl',
            horizontal: 'right'
          };
        });
      }
    });
  }
}

