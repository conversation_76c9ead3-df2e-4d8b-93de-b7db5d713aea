import { tool } from 'ai';
import { z } from 'zod';
import db from '@/db/db';
import { getAuthenticatedUserId } from '@/lib/auth-helpers';

export const cancelScheduledReport = tool({
  description: 'Cancels a previously created report schedule, stopping future automated deliveries.',
  parameters: z.object({
    scheduleId: z.string().describe('The ID of the report schedule to cancel'),
  }),
  execute: async ({ scheduleId }) => {
    try {
      // Get the authenticated user ID
      const userId = await getAuthenticatedUserId();
      
      // Find the schedule in the database
      const schedule = await db.scheduledReport.findUnique({
        where: { id: scheduleId },
        include: { report: true }
      });
      
      if (!schedule) {
        return {
          success: false,
          message: `Schedule with ID ${scheduleId} not found. Please check the ID and try again.`,
        };
      }
      
      // Check if the schedule belongs to the current user
      if (schedule.userId !== userId) {
        return {
          success: false,
          message: 'You do not have permission to cancel this schedule.',
        };
      }
      
      // Cancel the schedule (soft delete)
      await db.scheduledReport.update({
        where: { id: scheduleId },
        data: {
          active: false,
          updatedAt: new Date(),
        },
      });
      
      return {
        id: scheduleId,
        title: schedule.report.title,
        message: `Report schedule "${schedule.report.title}" has been cancelled successfully.`,
      };
    } catch (error) {
      console.error('Error cancelling scheduled report:', error);
      return {
        success: false,
        message: 'Failed to cancel scheduled report. Please try again.',
        error: error instanceof Error ? error.message : String(error),
      };
    }
  },
});
