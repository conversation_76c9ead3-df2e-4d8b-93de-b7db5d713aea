"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "@/components/ui/dialog";
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  TabsTrigger,
} from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { toast } from "sonner";
import { InvoiceStatus } from "@/lib/types";
import { getCategories } from "@/lib/actions/analytics";
import { updateInvoice } from "@/actions/invoice-actions";

// Import our tab components
import { BasicInfoTab } from "./form-components/BasicInfoTab";
import { LineItemsTab } from "./form-components/LineItemsTab";
import { VendorInfoTab } from "./form-components/VendorInfoTab";
import { CustomerInfoTab } from "./form-components/CustomerInfoTab";
import { AdditionalInfoTab } from "./form-components/AdditionalInfoTab";

interface LineItem {
  id?: string;
  description: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  taxRate: number | null;
  taxAmount: number | null;
  discount: number | null;
  productSku: string | null;
  notes: string | null;
  attributes: Record<string, unknown>;
}

interface Invoice {
  id: string;
  invoiceNumber?: string;
  status?: string;
  issueDate?: string | Date;
  dueDate?: string | Date;
  amount?: number;
  currency?: string;
  vendorName?: string;
  notes?: string;
  category?: { id: string; name: string };
  lineItems?: LineItem[];
  extractedData?: {
    vendor?: {
      name?: string;
      address?: string;
      email?: string;
      phone?: string;
      taxId?: string;
    };
    customer?: {
      name?: string;
      address?: string;
      email?: string;
      phone?: string;
    };
    financials?: {
      subtotal?: string;
      tax?: string;
      shipping?: string;
      discount?: string;
      total?: string;
    };
    termsAndConditions?: string;
    paymentTerms?: string;
    payment?: {
      method?: string;
      details?: string;
    };
  };
}

interface InvoiceEditFormProps {
  invoice: Invoice;
  isOpen: boolean;
  onClose: () => void;
}

export function InvoiceEditForm({
  invoice,
  isOpen,
  onClose,
}: InvoiceEditFormProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [categories, setCategories] = useState<{ id: string; name: string }[]>([]);
  const [activeTab, setActiveTab] = useState("basic");

  // Basic invoice data
  const [basicData, setBasicData] = useState({
    invoiceNumber: invoice.invoiceNumber || "",
    status: invoice.status || "PENDING",
    issueDate: invoice.issueDate ? new Date(invoice.issueDate) : null,
    dueDate: invoice.dueDate ? new Date(invoice.dueDate) : null,
    amount: invoice.amount?.toString() || "",
    currency: invoice.currency || "USD",
    vendorName: invoice.vendorName || "",
    notes: invoice.notes || "",
    categoryId: invoice.category?.id || "",
  });

  // Line items
  const [lineItems, setLineItems] = useState<LineItem[]>(
    invoice.lineItems && invoice.lineItems.length > 0
      ? invoice.lineItems.map((item: LineItem) => ({
          id: item.id,
          description: item.description || "",
          quantity: item.quantity || 0,
          unitPrice: item.unitPrice || 0,
          totalPrice: item.totalPrice || 0,
          taxRate: item.taxRate || null,
          taxAmount: item.taxAmount || null,
          discount: item.discount || null,
          productSku: item.productSku || null,
          notes: item.notes || null,
          attributes: item.attributes || {},
        }))
      : [
          {
            description: "",
            quantity: 1,
            unitPrice: 0,
            totalPrice: 0,
            taxRate: null,
            taxAmount: null,
            discount: null,
            productSku: null,
            notes: null,
            attributes: {},
          },
        ]
  );

  // Vendor information
  const [vendorInfo, setVendorInfo] = useState({
    name: invoice.extractedData?.vendor?.name || invoice.vendorName || "",
    address: invoice.extractedData?.vendor?.address || "",
    email: invoice.extractedData?.vendor?.email || "",
    phone: invoice.extractedData?.vendor?.phone || "",
    taxId: invoice.extractedData?.vendor?.taxId || "",
  });

  // Customer information
  const [customerInfo, setCustomerInfo] = useState({
    name: invoice.extractedData?.customer?.name || "",
    address: invoice.extractedData?.customer?.address || "",
    email: invoice.extractedData?.customer?.email || "",
    phone: invoice.extractedData?.customer?.phone || "",
  });

  // Financial information
  const [financialInfo, setFinancialInfo] = useState({
    subtotal: invoice.extractedData?.financials?.subtotal || "",
    tax: invoice.extractedData?.financials?.tax || "",
    shipping: invoice.extractedData?.financials?.shipping || "",
    discount: invoice.extractedData?.financials?.discount || "",
    total: invoice.extractedData?.financials?.total || "",
  });

  // Additional information
  const [additionalInfo, setAdditionalInfo] = useState({
    termsAndConditions: invoice.extractedData?.termsAndConditions || "",
    paymentTerms: invoice.extractedData?.paymentTerms || "",
    paymentMethod: invoice.extractedData?.payment?.method || "",
    paymentDetails: invoice.extractedData?.payment?.details || "",
  });

  // Fetch categories when component mounts
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const categoriesData = await getCategories();
        setCategories(categoriesData);
      } catch (error) {
        console.error("Error fetching categories:", error);
      }
    };

    fetchCategories();
  }, []);

  // Handle basic data changes
  const handleBasicInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setBasicData((prev) => ({ ...prev, [name]: value }));
  };

  const handleBasicSelectChange = (name: string, value: string) => {
    setBasicData((prev) => ({ ...prev, [name]: value }));
  };

  const handleBasicDateChange = (name: string, date: Date | null) => {
    setBasicData((prev) => ({ ...prev, [name]: date }));
  };

  // Handle line item changes
  const handleLineItemChange = (index: number, field: string, value: unknown) => {
    const updatedItems = [...lineItems];
    updatedItems[index] = { ...updatedItems[index], [field]: value };

    // Auto-calculate total price if quantity or unit price changes
    if (field === "quantity" || field === "unitPrice") {
      const quantity = field === "quantity" ? parseFloat(String(value)) || 0 : parseFloat(String(updatedItems[index].quantity)) || 0;
      const unitPrice = field === "unitPrice" ? parseFloat(String(value)) || 0 : parseFloat(String(updatedItems[index].unitPrice)) || 0;
      updatedItems[index].totalPrice = quantity * unitPrice;
    }

    setLineItems(updatedItems);
  };

  const addLineItem = () => {
    setLineItems([
      ...lineItems,
      {
        description: "",
        quantity: 1,
        unitPrice: 0,
        totalPrice: 0,
        taxRate: null,
        taxAmount: null,
        discount: null,
        productSku: "",
        notes: "",
        attributes: {},
      },
    ]);
  };

  const removeLineItem = (index: number) => {
    if (lineItems.length > 1) {
      const updatedItems = [...lineItems];
      updatedItems.splice(index, 1);
      setLineItems(updatedItems);
    } else {
      toast.error("You must have at least one line item");
    }
  };

  // Handle vendor info changes
  const handleVendorInfoChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setVendorInfo((prev) => ({ ...prev, [name]: value }));
  };

  // Handle customer info changes
  const handleCustomerInfoChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setCustomerInfo((prev) => ({ ...prev, [name]: value }));
  };

  // Handle financial info changes
  const handleFinancialInfoChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFinancialInfo((prev) => ({ ...prev, [name]: value }));
  };

  // Handle additional info changes
  const handleAdditionalInfoChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setAdditionalInfo((prev) => ({ ...prev, [name]: value }));
  };

  // Submit form
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    setIsLoading(true);

    try {
      // Prepare extracted data
      const extractedData = {
        ...invoice.extractedData,
        vendor: {
          ...invoice.extractedData?.vendor,
          name: vendorInfo.name,
          address: vendorInfo.address,
          email: vendorInfo.email,
          phone: vendorInfo.phone,
          taxId: vendorInfo.taxId,
        },
        customer: {
          ...invoice.extractedData?.customer,
          name: customerInfo.name,
          address: customerInfo.address,
          email: customerInfo.email,
          phone: customerInfo.phone,
        },
        financials: {
          ...invoice.extractedData?.financials,
          subtotal: financialInfo.subtotal,
          tax: financialInfo.tax,
          shipping: financialInfo.shipping,
          discount: financialInfo.discount,
          total: financialInfo.total,
        },
        termsAndConditions: additionalInfo.termsAndConditions,
        paymentTerms: additionalInfo.paymentTerms,
        payment: {
          ...invoice.extractedData?.payment,
          method: additionalInfo.paymentMethod,
          details: additionalInfo.paymentDetails,
        },
      };

      // Convert amount to number
      const amount = basicData.amount ? parseFloat(basicData.amount) : null;

      // Prepare data for update
      const updateData = {
        invoiceNumber: basicData.invoiceNumber,
        status: basicData.status as InvoiceStatus,
        issueDate: basicData.issueDate,
        dueDate: basicData.dueDate,
        amount,
        currency: basicData.currency,
        vendorName: basicData.vendorName || vendorInfo.name,
        notes: basicData.notes,
        categoryId: basicData.categoryId || null,
        lineItems,
        extractedData,
      };

      const result = await updateInvoice(invoice.id, updateData);

      if (result.success) {
        toast.success("Invoice updated successfully");
        router.refresh();
        onClose();
      } else {
        throw new Error(result.error || "Failed to update invoice");
      }
    } catch (error) {
      console.error("Error updating invoice:", error);
      toast.error("Failed to update invoice. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle>Edit Invoice</DialogTitle>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 overflow-hidden flex flex-col">
          <TabsList className="grid grid-cols-5">
            <TabsTrigger value="basic">Basic Info</TabsTrigger>
            <TabsTrigger value="lineItems">Line Items</TabsTrigger>
            <TabsTrigger value="vendor">Vendor</TabsTrigger>
            <TabsTrigger value="customer">Customer</TabsTrigger>
            <TabsTrigger value="additional">Additional</TabsTrigger>
          </TabsList>

          <ScrollArea className="flex-1 mt-4">
            <form id="invoiceEditForm" onSubmit={handleSubmit} className="space-y-4 px-1">
              <TabsContent value="basic" className="space-y-4 mt-0">
                <BasicInfoTab
                  basicData={basicData}
                  categories={categories}
                  handleBasicInputChange={handleBasicInputChange}
                  handleBasicSelectChange={handleBasicSelectChange}
                  handleBasicDateChange={handleBasicDateChange}
                />
              </TabsContent>

              <TabsContent value="lineItems" className="space-y-4 mt-0">
                <LineItemsTab
                  lineItems={lineItems}
                  handleLineItemChange={handleLineItemChange}
                  addLineItem={addLineItem}
                  removeLineItem={removeLineItem}
                />
              </TabsContent>

              <TabsContent value="vendor" className="space-y-4 mt-0">
                <VendorInfoTab
                  vendorInfo={vendorInfo}
                  handleVendorInfoChange={handleVendorInfoChange}
                />
              </TabsContent>

              <TabsContent value="customer" className="space-y-4 mt-0">
                <CustomerInfoTab
                  customerInfo={customerInfo}
                  handleCustomerInfoChange={handleCustomerInfoChange}
                />
              </TabsContent>

              <TabsContent value="additional" className="space-y-4 mt-0">
                <AdditionalInfoTab
                  financialInfo={financialInfo}
                  additionalInfo={additionalInfo}
                  handleFinancialInfoChange={handleFinancialInfoChange}
                  handleAdditionalInfoChange={handleAdditionalInfoChange}
                />
              </TabsContent>
            </form>
          </ScrollArea>
        </Tabs>

        <DialogFooter>
          <Button type="button" variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button type="submit" form="invoiceEditForm" disabled={isLoading}>
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              "Save Changes"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}