import PDFDocument from 'pdfkit';
import { ReportType } from '@/lib/report-types';
import { formatCurrency, formatDate } from '@/lib/utils';
import { ChartJSNodeCanvas } from 'chartjs-node-canvas';

// Define interfaces for report data
interface ReportData {
  [key: string]: unknown;
}

interface InvoiceSummaryData {
  summary: {
    total: number;
    totalAmount: number;
    paid: number;
    pending: number;
    overdue: number;
  };
  invoices: Array<{
    invoiceNumber?: string;
    vendorName?: string;
    issueDate?: string | Date;
    amount?: number;
    status?: string;
  }>;
}

/**
 * Generates a PDF report based on the provided data
 * @param title The title of the report
 * @param reportType The type of report
 * @param data The data for the report
 * @param includeCharts Whether to include charts in the report
 * @returns A buffer containing the PDF data
 */
export async function generatePDF(
  title: string,
  reportType: string,
  data: ReportData,
  includeCharts: boolean = true
): Promise<Buffer> {
  return new Promise((resolve, reject) => {
    try {
      // Create a new PDF document
      const doc = new PDFDocument({
        margin: 50,
        size: 'A4',
      });

      // Buffer to store PDF data
      const buffers: Buffer[] = [];
      doc.on('data', buffers.push.bind(buffers));
      doc.on('end', () => {
        const pdfData = Buffer.concat(buffers);
        resolve(pdfData);
      });

      // Add report header
      addReportHeader(doc, title, reportType);

      // Add report content based on type
      switch (reportType) {
        case ReportType.INVOICE_SUMMARY:
          // @ts-expect-error - Type assertion needed for runtime functionality
          addInvoiceSummaryContent(doc, data as InvoiceSummaryData, includeCharts);
          break;
        case ReportType.CASH_FLOW:
          addCashFlowContent(doc, data, includeCharts);
          break;
        case ReportType.PROFIT_LOSS:
          addProfitLossContent(doc, data, includeCharts);
          break;
        case ReportType.VENDOR_ANALYSIS:
          addVendorAnalysisContent(doc, data, includeCharts);
          break;
        case ReportType.CATEGORY_BREAKDOWN:
          addCategoryBreakdownContent(doc, data, includeCharts);
          break;
        case ReportType.BALANCE_SHEET:
          addBalanceSheetContent(doc, data, includeCharts);
          break;
        default:
          addGenericContent(doc, data, includeCharts);
      }

      // Add footer with page numbers
      addFooter(doc);

      // Finalize the PDF
      doc.end();
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * Adds a header to the report
 */
function addReportHeader(doc: PDFKit.PDFDocument, title: string, reportType: string) {
  // Add logo
  // doc.image('public/logo.png', 50, 45, { width: 50 });

  // Add title
  doc.fontSize(20).font('Helvetica-Bold').text(title, { align: 'center' });
  doc.moveDown();
  
  // Add report type and date
  doc.fontSize(12).font('Helvetica').text(`Report Type: ${reportType}`, { align: 'center' });
  doc.fontSize(10).text(`Generated on: ${formatDate(new Date())}`, { align: 'center' });
  
  doc.moveDown(2);
}

/**
 * Adds a footer with page numbers
 */
function addFooter(doc: PDFKit.PDFDocument) {
  const totalPages = doc.bufferedPageRange().count;
  for (let i = 0; i < totalPages; i++) {
    doc.switchToPage(i);
    
    // Add page number at the bottom
    doc.fontSize(8)
       .text(
         `Page ${i + 1} of ${totalPages}`,
         0,
         doc.page.height - 50,
         { align: 'center' }
       );
  }
}

/**
 * Generates a chart image using Chart.js
 */
async function generateChartImage(
  chartConfig: unknown,
  width: number = 500,
  height: number = 300
): Promise<Buffer> {
  const chartJSNodeCanvas = new ChartJSNodeCanvas({
    width,
    height,
    backgroundColour: 'white',
  });
  
  // Use a type assertion for chartConfig to match Chart.js expectations
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  return await chartJSNodeCanvas.renderToBuffer(chartConfig as any);
}

/**
 * Adds invoice summary content to the report
 */
async function addInvoiceSummaryContent(
  doc: PDFKit.PDFDocument,
  data: InvoiceSummaryData,
  includeCharts: boolean
) {
  // Add summary section
  doc.fontSize(16).font('Helvetica-Bold').text('Invoice Summary', { underline: true });
  doc.moveDown();
  
  const { summary, invoices } = data;
  
  doc.fontSize(12).font('Helvetica');
  doc.text(`Total Invoices: ${summary.total}`);
  doc.text(`Total Amount: ${formatCurrency(summary.totalAmount)}`);
  doc.text(`Paid Invoices: ${summary.paid}`);
  doc.text(`Pending Invoices: ${summary.pending}`);
  doc.text(`Overdue Invoices: ${summary.overdue}`);
  
  doc.moveDown(2);
  
  // Add chart if requested
  if (includeCharts) {
    try {
      // Create chart data for invoice status
      const chartConfig = {
        type: 'pie',
        data: {
          labels: ['Paid', 'Pending', 'Overdue'],
          datasets: [{
            data: [summary.paid, summary.pending, summary.overdue],
            backgroundColor: ['#36A2EB', '#FFCE56', '#FF6384'],
          }]
        },
        options: {
          plugins: {
            title: {
              display: true,
              text: 'Invoice Status Distribution'
            }
          }
        }
      };
      
      const chartImage = await generateChartImage(chartConfig);
      doc.image(chartImage, {
        fit: [500, 300],
        align: 'center',
      });
      
      doc.moveDown(2);
    } catch (error) {
      doc.text('Chart generation failed', { align: 'center' });
      doc.moveDown();
    }
  }
  
  // Add invoice table
  doc.fontSize(14).font('Helvetica-Bold').text('Recent Invoices', { underline: true });
  doc.moveDown();
  
  // Table headers
  const tableTop = doc.y;
  const invoiceHeaders = ['Invoice #', 'Vendor', 'Date', 'Amount', 'Status'];
  const columnWidth = 100;
  
  doc.fontSize(10).font('Helvetica-Bold');
  invoiceHeaders.forEach((header, i) => {
    doc.text(header, 50 + (i * columnWidth), tableTop, { width: columnWidth, align: 'left' });
  });
  
  // Table rows
  doc.font('Helvetica');
  let rowTop = tableTop + 20;
  
  // Only show the first 10 invoices to avoid making the PDF too long
  const recentInvoices = invoices.slice(0, 10);
  
  recentInvoices.forEach((invoice: InvoiceSummaryData['invoices'][0]) => {
    // Check if we need a new page
    if (rowTop > doc.page.height - 100) {
      doc.addPage();
      rowTop = 50;
      
      // Repeat headers on new page
      doc.fontSize(10).font('Helvetica-Bold');
      invoiceHeaders.forEach((header, i) => {
        doc.text(header, 50 + (i * columnWidth), rowTop, { width: columnWidth, align: 'left' });
      });
      doc.font('Helvetica');
      rowTop += 20;
    }
    
    doc.text(invoice.invoiceNumber || '-', 50, rowTop, { width: columnWidth, align: 'left' });
    doc.text(invoice.vendorName || '-', 50 + columnWidth, rowTop, { width: columnWidth, align: 'left' });
    doc.text(invoice.issueDate ? formatDate(new Date(invoice.issueDate)) : '-', 50 + (2 * columnWidth), rowTop, { width: columnWidth, align: 'left' });
    doc.text(invoice.amount ? formatCurrency(invoice.amount) : '-', 50 + (3 * columnWidth), rowTop, { width: columnWidth, align: 'left' });
    doc.text(invoice.status || '-', 50 + (4 * columnWidth), rowTop, { width: columnWidth, align: 'left' });
    
    rowTop += 20;
  });
}

// Implement other report content generators
function addCashFlowContent(
  doc: PDFKit.PDFDocument, 
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  _data: ReportData, 
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  _includeCharts: boolean
) {
  // Cash flow report implementation
  doc.fontSize(16).font('Helvetica-Bold').text('Cash Flow Report', { underline: true });
  doc.moveDown();
  
  // Add implementation details here
}

function addProfitLossContent(
  doc: PDFKit.PDFDocument, 
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  _data: ReportData, 
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  _includeCharts: boolean
) {
  // Profit and loss report implementation
  doc.fontSize(16).font('Helvetica-Bold').text('Profit & Loss Report', { underline: true });
  doc.moveDown();
  
  // Add implementation details here
}

function addVendorAnalysisContent(
  doc: PDFKit.PDFDocument, 
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  _data: ReportData, 
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  _includeCharts: boolean
) {
  // Vendor analysis report implementation
  doc.fontSize(16).font('Helvetica-Bold').text('Vendor Analysis Report', { underline: true });
  doc.moveDown();
  
  // Add implementation details here
}

function addCategoryBreakdownContent(
  doc: PDFKit.PDFDocument, 
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  _data: ReportData, 
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  _includeCharts: boolean
) {
  // Category breakdown report implementation
  doc.fontSize(16).font('Helvetica-Bold').text('Category Breakdown Report', { underline: true });
  doc.moveDown();
  
  // Add implementation details here
}

function addBalanceSheetContent(
  doc: PDFKit.PDFDocument, 
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  _data: ReportData, 
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  _includeCharts: boolean
) {
  // Balance sheet report implementation
  doc.fontSize(16).font('Helvetica-Bold').text('Balance Sheet Report', { underline: true });
  doc.moveDown();
  
  // Add implementation details here
}

function addGenericContent(
  doc: PDFKit.PDFDocument, 
  data: ReportData, 
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  _includeCharts: boolean
) {
  // Generic report implementation for unknown report types
  doc.fontSize(16).font('Helvetica-Bold').text('Report', { underline: true });
  doc.moveDown();
  
  doc.fontSize(12).font('Helvetica').text('This report contains generic data.');
  doc.moveDown();
  
  // Display the data as JSON if available
  if (data) {
    doc.fontSize(10).font('Courier').text(JSON.stringify(data, null, 2));
  }
}
