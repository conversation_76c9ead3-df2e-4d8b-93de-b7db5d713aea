"use server";

import { getCheckoutURL, processWebhookEvent } from "@/actions/lemon-actions";
import { redirect } from "next/navigation";
import db from "@/db/db";

export async function handleUpgrade(variantId: number) {
  if (!variantId || isNaN(variantId) || variantId <= 0) {
    throw new Error("Please provide a valid variantId.");
  }
  
  const url = await getCheckoutURL(variantId);
  if (url) {
    redirect(url);
  }
}

export async function reprocessWebhookEvents(limit?: number) {
  try {
    // Get all unprocessed webhook events
    const webhookEvents = await db.webhookEvent.findMany({
      where: { processed: false },
      orderBy: { createdAt: 'asc' },
      take: limit || 10 // Use provided limit or default to 10
    });
    
    if (webhookEvents.length === 0) {
      return { success: true, message: "No unprocessed webhook events found" };
    }
    
    // Process each webhook event
    const results = [];
    for (const event of webhookEvents) {
      try {
        await processWebhookEvent({
          id: event.id,
          body: event.body,
          eventName: event.eventName
        });
        results.push({ id: event.id, success: true });
      } catch (error) {
        results.push({ id: event.id, success: false, error: String(error) });
      }
    }
    
    return { 
      success: true, 
      message: `Processed ${webhookEvents.length} webhook events`,
      results
    };
  } catch (error) {
    return { success: false, message: String(error) };
  }
}

export async function updatePaymentMethod() {
  // TODO: Implement payment method update logic
  // This is a placeholder for the actual implementation
  return;
}
