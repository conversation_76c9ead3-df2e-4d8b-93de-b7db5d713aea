'use server';

import { auth } from '@clerk/nextjs/server';
import db from '@/db/db';
import { type InvoiceData } from '@/types/invoice';

/**
 * Parse a date string in various formats
 * Handles DD/MM/YYYY, MM/DD/YYYY, YYYY-MM-DD and other common formats
 */
function parseDate(dateStr?: string): Date | undefined {
  if (!dateStr) return undefined;

  // Try direct parsing first
  const directDate = new Date(dateStr);
  if (!isNaN(directDate.getTime())) {
    return directDate;
  }

  // Handle DD/MM/YYYY format
  const ddmmyyyy = /^(\d{1,2})[\/\-\.](\d{1,2})[\/\-\.](\d{2,4})$/;
  const match = dateStr.match(ddmmyyyy);
  if (match) {
    const [, day, month, year] = match;
    // Adjust year if needed (20 -> 2020)
    const fullYear = year.length === 2 ? `20${year}` : year;
    const isoDate = `${fullYear}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
    const parsedDate = new Date(isoDate);

    if (!isNaN(parsedDate.getTime())) {
      return parsedDate;
    }
  }

  // Try other formats if the first format doesn't work
  const formats = [
    // month/day/year
    {
      regex: /^(\d{1,2})[\/\-\.](\d{1,2})[\/\-\.](\d{2,4})$/,
      handler: (m: RegExpMatchArray) => {
        const [, month, day, year] = m;
        const fullYear = year.length === 2 ? `20${year}` : year;
        return `${fullYear}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
      },
    },
    // Various text representations (Jan 1, 2022, etc)
    {
      regex:
        /^([A-Za-z]{3,9})\s+(\d{1,2})(?:st|nd|rd|th)?,?\s+(\d{2,4})$/,
      handler: (m: RegExpMatchArray) => {
        const [, month, day, year] = m;
        const monthNum = new Date(`${month} 1, 2000`).getMonth() + 1;
        const fullYear = year.length === 2 ? `20${year}` : year;
        return `${fullYear}-${monthNum.toString().padStart(2, '0')}-${day.padStart(2, '0')}`;
      },
    },
  ];

  for (const format of formats) {
    const match = dateStr.match(format.regex);
    if (match) {
      try {
        const isoDate = format.handler(match);
        const parsedDate = new Date(isoDate);
        if (!isNaN(parsedDate.getTime())) {
          return parsedDate;
        }
      } catch (e) {
        // Continue to next format if this one fails
        console.error('Error parsing date:', e);
      }
    }
  }

  // If all else fails, return undefined rather than an invalid date
  console.warn(`Unable to parse date string: ${dateStr}`);
  return undefined;
}

/**
 * Server action to save an extracted invoice to the database
 */
export async function saveInvoiceToDatabase(
  invoiceData: InvoiceData
) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return {
        success: false,
        error: 'Authentication required',
      };
    }

    // Format dates (if present) to Date objects
    const issueDate = parseDate(invoiceData.date);
    const dueDate = parseDate(invoiceData.dueDate);

    // For additionalFields.issueDate if it exists
    let additionalIssueDate;
    if (invoiceData.additionalFields?.issueDate) {
      const issueDateField = invoiceData.additionalFields.issueDate;
      if (typeof issueDateField === 'string') {
        additionalIssueDate = parseDate(issueDateField);
      }
    }

    // Use the additional issueDate as a fallback if the primary one is missing
    const finalIssueDate = issueDate || additionalIssueDate;

    // Parse amount from financial data
    let amount: number | undefined;
    if (invoiceData.financials?.total) {
      // Remove currency symbol and commas, then convert to number
      const totalStr = invoiceData.financials.total.replace(
        /[^0-9.-]+/g,
        ''
      );
      amount = parseFloat(totalStr);
    }

    // Get currency from financials
    const currency = invoiceData.financials?.currency || 'USD';

    // Get selected category and vendor type from meta data with better handling
    const selectedCategory =
      invoiceData.meta?.suggestions?.selectedCategory;
    const selectedVendorType =
      invoiceData.meta?.suggestions?.selectedVendorType;

    // Process category name - if it exists, make sure it's valid
    let categoryToUse = selectedCategory || null;
    if (categoryToUse) {
      // Make sure we strip any HTML or other problematic characters
      categoryToUse = categoryToUse.replace(/<[^>]*>?/gm, '').trim();

      // If category is empty after cleaning, don't use it
      if (!categoryToUse) {
        categoryToUse = null;
      }
    }

    // Only use direct category match if no selection was made by the user
    if (
      !categoryToUse &&
      invoiceData.meta?.suggestions?.categories?.length === 1 &&
      invoiceData.meta?.suggestions?.categories[0].confidence >= 90
    ) {
      categoryToUse =
        invoiceData.meta?.suggestions?.categories[0].name;
    }

    // Process vendor type name - handle the "Vendor: " prefix case
    let vendorTypeToUse = selectedVendorType || null;
    let extractedVendorName = null;

    if (vendorTypeToUse) {
      // Check if it's a "Vendor: XYZ" format and extract the vendor name
      if (vendorTypeToUse.startsWith('Vendor: ')) {
        extractedVendorName = vendorTypeToUse.substring(8).trim();
        vendorTypeToUse = 'Vendor';
      }

      // Make sure we strip any HTML or other problematic characters
      vendorTypeToUse = vendorTypeToUse
        .replace(/<[^>]*>?/gm, '')
        .trim();

      // If vendor type is empty after cleaning, don't use it
      if (!vendorTypeToUse) {
        vendorTypeToUse = null;
        extractedVendorName = null;
      }
    }

    // Only use direct vendor match if no selection was made by the user
    if (
      !vendorTypeToUse &&
      invoiceData.meta?.suggestions?.vendorTypes?.length === 1 &&
      invoiceData.meta?.suggestions?.vendorTypes[0].confidence >= 90
    ) {
      vendorTypeToUse =
        invoiceData.meta?.suggestions?.vendorTypes[0].name;

      // Check if this direct match is a "Vendor: XYZ" format
      if (vendorTypeToUse.startsWith('Vendor: ')) {
        extractedVendorName = vendorTypeToUse.substring(8).trim();
        vendorTypeToUse = 'Vendor';
      }
    }

    // Determine invoice type based on context (default to PURCHASE)
    let invoiceType = 'PURCHASE';
    if (
      vendorTypeToUse === 'Customer' ||
      invoiceData.meta?.suggestions?.vendorTypes?.some(
        (v) => v.name === 'Customer' && v.confidence > 70
      )
    ) {
      invoiceType = 'SALES';
    }

    // Duplicate checking has been removed as requested by user
    // Invoice will be saved directly without duplicate validation

    // Get user's organization
    const user = await db.user.findUnique({
      where: { clerkId: userId },
      include: { organizations: true },
    });
    if (!user || !user.organizations.length) {
      return {
        success: false,
        error: 'No organization found for this user.',
      };
    }
    const organizationId = user.organizations[0].id;

    // Create the invoice record in the database
    const invoice = await db.invoice.create({
      data: {
        // Basic invoice details
        invoiceNumber: invoiceData.invoiceNumber,
        title: `Invoice ${invoiceData.invoiceNumber || ''}`.trim(),
        vendorName: invoiceData.vendor?.name,
        issueDate: finalIssueDate,
        dueDate,
        amount: amount || 0,
        currency,
        status: 'PENDING',
        invoiceType,
        organizationId,
        userId: user.id,

        // Store file URLs
        originalFileUrl:
          typeof invoiceData.fileUrl === 'string'
            ? invoiceData.fileUrl
            : undefined,
        thumbnailUrl:
          typeof invoiceData.thumbnailUrl === 'string'
            ? invoiceData.thumbnailUrl
            : undefined,

        // Store full extracted data as JSON
        extractedData: JSON.parse(JSON.stringify(invoiceData)),

        // Store language code if available
        languageCode: invoiceData.meta?.language,

        // Extraction confidence
        extractionConfidence: invoiceData.meta?.confidence?.overall,

        // Create line items if available
        lineItems: invoiceData.lineItems
          ? {
              create: invoiceData.lineItems.map((item) => {
                // Parse numbers from strings with type checking
                const safeToString = (
                  val: string | number | null | undefined
                ): string => {
                  if (val === null || val === undefined) return '0';
                  return typeof val === 'string' ? val : String(val);
                };

                const quantity = parseFloat(
                  safeToString(item.quantity).replace(
                    /[^0-9.-]+/g,
                    ''
                  ) || '0'
                );
                const unitPrice = parseFloat(
                  safeToString(item.unitPrice).replace(
                    /[^0-9.-]+/g,
                    ''
                  ) || '0'
                );
                const totalPrice = parseFloat(
                  safeToString(item.amount).replace(
                    /[^0-9.-]+/g,
                    ''
                  ) || '0'
                );

                // Parse tax rate if available
                let taxRate: number | undefined;
                if (item.taxRate) {
                  const taxRateStr = safeToString(
                    item.taxRate
                  ).replace(/[^0-9.-]+/g, '');
                  if (taxRateStr)
                    taxRate = parseFloat(taxRateStr) / 100; // Convert percentage to decimal
                }

                // Parse discount if available
                let discount: number | undefined;
                if (item.discount) {
                  const discountStr = safeToString(
                    item.discount
                  ).replace(/[^0-9.-]+/g, '');
                  if (discountStr) {
                    discount = parseFloat(discountStr);
                    if (safeToString(item.discount).includes('%'))
                      discount = discount / 100; // Convert percentage to decimal
                  }
                }

                return {
                  description: item.description || '',
                  quantity,
                  unitPrice,
                  totalPrice,
                  taxRate,
                  discount,
                  productSku: item.itemCode,
                  notes: '', // No notes in the extraction schema
                  attributes: item, // Store the original item data
                };
              }),
            }
          : undefined,
      },
      include: {
        lineItems: true,
      },
    });

    // If category is selected or directly matched, try to find or create category record
    if (categoryToUse) {
      // Check if category already exists
      const existingCategory = await db.category.findFirst({
        where: {
          name: categoryToUse,
          user: {
            clerkId: userId,
          },
        },
      });

      if (existingCategory) {
        // Connect existing category to invoice
        await db.invoice.update({
          where: { id: invoice.id },
          data: {
            category: {
              connect: { id: existingCategory.id },
            },
          },
        });
      } else {
        // Create a new category and connect it
        // Generate random color for new category
        const colors = [
          '#4CAF50',
          '#2196F3',
          '#F44336',
          '#FF9800',
          '#9C27B0',
          '#607D8B',
        ];
        const randomColor =
          colors[Math.floor(Math.random() * colors.length)];

        const newCategory = await db.category.create({
          data: {
            name: categoryToUse,
            color: randomColor,
            user: {
              connect: {
                clerkId: userId,
              },
            },
          },
        });

        await db.invoice.update({
          where: { id: invoice.id },
          data: {
            category: {
              connect: { id: newCategory.id },
            },
          },
        });
      }
    }

    // Vendor handling
    if (invoiceData.vendor?.name || extractedVendorName) {
      // Prioritize the extracted vendor name from "Vendor: XYZ" if available
      const vendorName =
        extractedVendorName || invoiceData.vendor?.name || '';

      if (vendorName) {
        // Check if vendor already exists
        const existingVendor = await db.vendor.findFirst({
          where: {
            name: vendorName,
            user: {
              clerkId: userId,
            },
          },
        });

        if (!existingVendor) {
          const vendor = await db.vendor.create({
            data: {
              name: vendorName,
              email: invoiceData?.vendor?.email,
              phone: invoiceData?.vendor?.phone,
              website: invoiceData?.vendor?.website,
              address: invoiceData?.vendor?.address,
              notes: vendorTypeToUse
                ? `Vendor Type: ${vendorTypeToUse}`
                : undefined,
              user: {
                connect: {
                  clerkId: userId,
                },
              },
            },
          });

          // Connect vendor to invoice
          await db.invoice.update({
            where: { id: invoice.id },
            data: {
              vendor: {
                connect: { id: vendor.id },
              },
            },
          });
        } else {
          // Connect existing vendor to invoice
          await db.invoice.update({
            where: { id: invoice.id },
            data: {
              vendor: {
                connect: { id: existingVendor.id },
              },
            },
          });
        }
      }
    }

    return {
      success: true,
      data: invoice,
    };
  } catch (error) {
    console.error('Error saving invoice to database:', error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : 'Failed to save invoice to database',
    };
  }
}
