import { prisma } from "@/lib/prisma";
import { InvoiceData } from "@/types/invoice";
import {
    VendorProfile,
    LearningFeedback,
} from "@/types/enhanced-invoice";

/**
 * Store user corrections as learning feedback
 */
export async function storeLearningFeedback(
    invoiceId: string,
    userId: string,
    originalData: InvoiceData,
    correctedData: InvoiceData
): Promise<void> {
    try {
        // Find differences between original and corrected data
        const feedback: LearningFeedback[] = [];

        // Check basic fields
        const basicFields = ['invoiceNumber', 'date', 'dueDate'];
        for (const field of basicFields) {
            if (originalData[field] !== correctedData[field]) {
                feedback.push({
                    id: crypto.randomUUID(),
                    invoiceId,
                    field,
                    originalValue: String(originalData[field] || ''),
                    correctedValue: String(correctedData[field] || ''),
                    confidence: 0.8, // Default confidence
                    timestamp: new Date()
                });
            }
        }

        // Check vendor info
        if (originalData.vendor && correctedData.vendor) {
            const vendorFields = ['name', 'address', 'taxId', 'email', 'phone'];
            for (const field of vendorFields) {
                if (originalData.vendor[field] !== correctedData.vendor[field]) {
                    feedback.push({
                        id: crypto.randomUUID(),
                        invoiceId,
                        field: `vendor.${field}`,
                        originalValue: String(originalData.vendor[field] || ''),
                        correctedValue: String(correctedData.vendor[field] || ''),
                        confidence: 0.8,
                        timestamp: new Date()
                    });
                }
            }
        }

        // Check line items (simplified - in reality would need more complex diffing)
        if (originalData.lineItems && correctedData.lineItems) {
            // For simplicity, just check if line items count changed
            if (originalData.lineItems.length !== correctedData.lineItems.length) {
                feedback.push({
                    id: crypto.randomUUID(),
                    invoiceId,
                    field: 'lineItems',
                    originalValue: JSON.stringify(originalData.lineItems),
                    correctedValue: JSON.stringify(correctedData.lineItems),
                    confidence: 0.7,
                    timestamp: new Date()
                });
            }
        }

        // Store feedback in user's AI settings
        if (feedback.length > 0) {
            const aiSettings = await prisma.aISettings.findUnique({
                where: { userId }
            });

            if (aiSettings) {
                // Get existing feedback or initialize empty array
                const existingFeedback = (aiSettings.learningFeedback as unknown as LearningFeedback[]) || [];

                // Update AI settings with new feedback
                await prisma.aISettings.update({
                    where: { userId },
                    data: {
                        learningFeedback: JSON.parse(JSON.stringify([...existingFeedback, ...feedback]))
                    }
                });

                // Also store in invoice history
                await prisma.invoiceHistory.create({
                    data: {
                        invoiceId,
                        userId,
                        action: 'UPDATE',
                        changes: feedback.reduce((acc, item) => {
                            acc[item.field] = {
                                from: item.originalValue,
                                to: item.correctedValue
                            };
                            return acc;
                        }, {} as Record<string, { from: string; to: string }>)
                    }
                });
            }
        }
    } catch (error) {
        console.error('Error storing learning feedback:', error);
    }
}

/**
 * Create or update vendor profile based on invoice data
 */
export async function updateVendorProfile(
    vendorId: string,
    userId: string,
    invoiceData: InvoiceData
): Promise<VendorProfile | null> {
    try {
        // Check if profile exists
        const profile = await prisma.vendorProfile.findFirst({
            where: {
                vendorId,
                userId
            }
        });

        // Extract document format patterns
        const documentFormat = detectDocumentFormat(invoiceData);

        // Extract field mappings
        const fieldMappings = extractFieldMappings(invoiceData);

        // Extract common line items
        const commonLineItems = invoiceData.lineItems || [];

        if (profile) {
            // Update existing profile
            const updatedProfile = await prisma.vendorProfile.update({
                where: { id: profile.id },
                data: {
                    documentFormat,
                    extractionPatterns: {
                        // Merge with existing patterns
                        ...(profile.extractionPatterns as Record<string, unknown> || {}),
                        invoiceNumberPattern: detectInvoiceNumberPattern(invoiceData.invoiceNumber),
                        dateFormat: detectDateFormat(invoiceData.date)
                    },
                    fieldMappings,
                    commonLineItems: commonLineItems.length > 0 ? commonLineItems : undefined,
                    updatedAt: new Date()
                }
            });

            return updatedProfile as unknown as VendorProfile;
        } else {
            // Create new profile
            const newProfile = await prisma.vendorProfile.create({
                data: {
                    vendorId,
                    userId,
                    documentFormat,
                    extractionPatterns: {
                        invoiceNumberPattern: detectInvoiceNumberPattern(invoiceData.invoiceNumber),
                        dateFormat: detectDateFormat(invoiceData.date)
                    },
                    fieldMappings,
                    commonLineItems: commonLineItems.length > 0 ? commonLineItems : undefined
                }
            });

            return newProfile as unknown as VendorProfile;
        }
    } catch (error) {
        console.error('Error updating vendor profile:', error);
        return null;
    }
}

/**
 * Detect document format patterns
 */
function detectDocumentFormat(invoiceData: InvoiceData): string {
    // Simple heuristic - can be expanded
    if (invoiceData.vendor?.name && invoiceData.vendor.name.includes('Amazon')) {
        return 'AMAZON';
    } else if (invoiceData.vendor?.name && invoiceData.vendor.name.includes('Microsoft')) {
        return 'MICROSOFT';
    } else {
        return 'STANDARD';
    }
}

/**
 * Extract field mappings from invoice data
 */
function extractFieldMappings(invoiceData: InvoiceData): Record<string, string> {
    const mappings: Record<string, string> = {};

    // Map standard fields to their actual names in the document
    if (invoiceData.meta?.language) {
        switch (invoiceData.meta.language) {
            case 'en':
                mappings.invoiceNumber = 'Invoice Number';
                mappings.date = 'Invoice Date';
                mappings.dueDate = 'Due Date';
                break;
            case 'es':
                mappings.invoiceNumber = 'Número de Factura';
                mappings.date = 'Fecha de Factura';
                mappings.dueDate = 'Fecha de Vencimiento';
                break;
            case 'fr':
                mappings.invoiceNumber = 'Numéro de Facture';
                mappings.date = 'Date de Facture';
                mappings.dueDate = 'Date d\'échéance';
                break;
            case 'de':
                mappings.invoiceNumber = 'Rechnungsnummer';
                mappings.date = 'Rechnungsdatum';
                mappings.dueDate = 'Fälligkeitsdatum';
                break;
            default:
                mappings.invoiceNumber = 'Invoice Number';
                mappings.date = 'Invoice Date';
                mappings.dueDate = 'Due Date';
        }
    }

    return mappings;
}

/**
 * Detect invoice number pattern
 */
function detectInvoiceNumberPattern(invoiceNumber?: string): string {
    if (!invoiceNumber) return '';

    // Check for common patterns
    if (/^[A-Z]{2,4}-\d{4,6}$/.test(invoiceNumber)) {
        return 'LETTERS-NUMBERS';
    } else if (/^\d{4,6}$/.test(invoiceNumber)) {
        return 'NUMBERS_ONLY';
    } else if (/^[A-Z]{2,4}\d{4,6}$/.test(invoiceNumber)) {
        return 'LETTERS_NUMBERS';
    } else {
        return 'CUSTOM';
    }
}

/**
 * Detect date format
 */
function detectDateFormat(date?: string): string {
    if (!date) return '';

    // Check for common date formats
    if (/^\d{4}-\d{2}-\d{2}$/.test(date)) {
        return 'YYYY-MM-DD';
    } else if (/^\d{2}\/\d{2}\/\d{4}$/.test(date)) {
        return 'MM/DD/YYYY';
    } else if (/^\d{2}\.\d{2}\.\d{4}$/.test(date)) {
        return 'DD.MM.YYYY';
    } else {
        return 'CUSTOM';
    }
}