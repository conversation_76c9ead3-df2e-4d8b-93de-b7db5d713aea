import { auth } from '@clerk/nextjs/server';
import { redirect } from 'next/navigation';
import { checkUserSubscription } from '@/lib/subscription-helpers';
import PricingPageComponent from '@/components/pricing/PricingPage';

interface PricingPageProps {
  searchParams?: Promise<{
    return_url?: string;
    success?: string;
    session_id?: string;
  }>;
}

export default async function PricingPage({
  searchParams,
}: PricingPageProps) {
  const { userId } = await auth();
  const params = await searchParams;

  // If not authenticated, redirect to sign-in with return URL
  if (!userId) {
    const returnUrl = params?.return_url;
    const signInUrl = returnUrl
      ? `/sign-in?redirect_url=${encodeURIComponent(`/pricing?return_url=${encodeURIComponent(returnUrl)}`)}`
      : '/sign-in?redirect_url=/pricing';
    redirect(signInUrl);
  }

  // Don't redirect if user is coming from a successful checkout
  // This prevents the redirect loop when checkout completes
  const isFromSuccessfulCheckout =
    params?.success === 'true' && params?.session_id;

  if (!isFromSuccessfulCheckout) {
    // Check if user already has an active subscription
    const subscriptionStatus = await checkUserSubscription();

    if (subscriptionStatus.hasActiveSubscription) {
      // User already has a subscription, redirect to dashboard or return URL
      const returnUrl = params?.return_url;
      redirect(returnUrl || '/dashboard');
    }
  }

  // Get return URL for after successful purchase
  const returnUrl = params?.return_url;

  return (
    <div className="min-h-screen bg-black">
      <PricingPageComponent returnUrl={returnUrl} />
    </div>
  );
}
