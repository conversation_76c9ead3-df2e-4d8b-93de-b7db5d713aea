"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { InvoiceDataDisplay } from "@/components/dashboard/pages/upload/invoice-data-display";
import type { InvoiceData } from "@/types/invoice";

interface SavedInvoiceCardProps {
  invoice: InvoiceData;
  index: number;
  onUpdate: (index: number, data: InvoiceData) => void;
}

export function SavedInvoiceCard({ invoice, index, onUpdate }: SavedInvoiceCardProps) {
  const [isDetailsOpen, setIsDetailsOpen] = useState(false);
  
  return (
    <div className="mb-6 border rounded-lg overflow-hidden">
      <div className="p-4 bg-muted/30">
        <div className="flex flex-wrap justify-between items-center">
          <div className="space-y-1">
            <h3 className="font-medium">
              {invoice.vendor?.name || "Unknown Vendor"}
            </h3>
            <div className="flex flex-wrap gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">Invoice:</span>{" "}
                <span className="font-medium">
                  {invoice.invoiceNumber || "N/A"}
                </span>
              </div>
              <div>
                <span className="text-muted-foreground">Date:</span>{" "}
                <span className="font-medium">
                  {invoice.date || "N/A"}
                </span>
              </div>
              <div>
                <span className="text-muted-foreground">Amount:</span>{" "}
                <span className="font-medium">
                  {invoice.financials?.total || "N/A"}
                </span>
              </div>
            </div>
          </div>
          <Button 
            variant="ghost" 
            size="sm"
            onClick={() => setIsDetailsOpen(!isDetailsOpen)}
          >
            {isDetailsOpen ? "Hide Details" : "Show Details"}
          </Button>
        </div>
      </div>
      
      {isDetailsOpen && (
        <div className="p-4">
          <InvoiceDataDisplay
            data={invoice}
            onUpdate={(updatedData) => {
              onUpdate(index, updatedData);
            }}
          />
        </div>
      )}
    </div>
  );
} 