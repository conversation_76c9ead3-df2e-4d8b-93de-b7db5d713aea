import db from '@/db/db';

/**
 * Update existing plans with usage limits
 * Starter: 5 chats, 10 invoices
 * Business: 50 chats, 100 invoices
 * Enterprise: 500 chats, 1000 invoices
 */
export async function updatePlanLimits() {
  console.log('🔧 Updating plans with usage limits...');

  try {
    // Define the usage limits for each plan tier
    const planUpdates = [
      // Starter Plans
      {
        productName: 'Starter',
        chatLimit: 5,
        invoiceLimit: 10,
      },
      // Business Plans
      {
        productName: 'Business',
        chatLimit: 50,
        invoiceLimit: 100,
      },
      // Enterprise Plans
      {
        productName: 'Enterprise',
        chatLimit: 500,
        invoiceLimit: 1000,
      },
    ];

    let updatedCount = 0;

    for (const update of planUpdates) {
      // Update all plans with this product name (both monthly and yearly)
      const result = await db.plan.updateMany({
        where: {
          productName: update.productName,
        },
        data: {
          chatLimit: update.chatLimit,
          invoiceLimit: update.invoiceLimit,
        },
      });

      console.log(
        `✅ Updated ${result.count} ${update.productName} plans with limits: ${update.chatLimit} chats, ${update.invoiceLimit} invoices`
      );
      updatedCount += result.count;
    }

    console.log(
      `🎉 Successfully updated ${updatedCount} plans with usage limits`
    );
    return { success: true, updated: updatedCount };
  } catch (error) {
    console.error('❌ Error updating plan limits:', error);
    return { success: false, error };
  }
}

/**
 * Check current plan limits
 */
export async function checkPlanLimits() {
  try {
    const plans = await db.plan.findMany({
      orderBy: { sort: 'asc' },
      select: {
        id: true,
        name: true,
        productName: true,
        price: true,
        interval: true,
        chatLimit: true,
        invoiceLimit: true,
      },
    });

    console.log('📊 Current plan limits:');
    plans.forEach((plan) => {
      console.log(
        `  ${plan.productName} (${plan.interval}): ${plan.chatLimit} chats, ${plan.invoiceLimit} invoices - $${(parseInt(plan.price) / 100).toFixed(2)}`
      );
    });

    return plans;
  } catch (error) {
    console.error('Error checking plan limits:', error);
    return [];
  }
}
