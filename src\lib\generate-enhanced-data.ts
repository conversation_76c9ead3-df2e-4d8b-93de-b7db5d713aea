import { Invoice, Prisma } from '@prisma/client';
import { addDays, differenceInDays, subDays } from 'date-fns';

// Define types for extracted data
interface ExtractedFinancials {
  subtotal?: string;
  tax?: string;
  shipping?: string;
  discount?: string;
  total?: string;
}

interface ExtractedVendor {
  name?: string;
  taxId?: string;
}

interface ExtractedPayment {
  terms?: string;
}

interface ExtractedData {
  vendor?: ExtractedVendor;
  financials?: ExtractedFinancials;
  payment?: ExtractedPayment;
}

// Define LineItem interface
interface LineItem {
  description?: string;
  quantity?: number;
  unitPrice?: number;
  totalPrice?: number;
}

// Type for invoice with extracted data
type InvoiceWithExtractedData = Invoice & {
  extractedData?: Prisma.JsonValue;
  lineItems?: LineItem[];
};

// Helper function to safely access extracted data
function getExtractedData(
  invoice: InvoiceWithExtractedData
): ExtractedData {
  if (!invoice.extractedData) return {};

  try {
    if (typeof invoice.extractedData === 'object') {
      return invoice.extractedData as unknown as ExtractedData;
    }
  } catch (e) {
    console.error('Error parsing extracted data:', e);
  }

  return {};
}

// Helper function to safely access financials
function getFinancials(
  invoice: InvoiceWithExtractedData
): ExtractedFinancials {
  const extractedData = getExtractedData(invoice);
  return extractedData.financials || {};
}

// Helper function to safely access vendor info
function getVendorInfo(
  invoice: InvoiceWithExtractedData
): ExtractedVendor {
  const extractedData = getExtractedData(invoice);
  return extractedData.vendor || {};
}

// Helper function to safely access payment info
function getPaymentInfo(
  invoice: InvoiceWithExtractedData
): ExtractedPayment {
  const extractedData = getExtractedData(invoice);
  return extractedData.payment || {};
}

// Helper function to safely access line items
function getLineItems(invoice: InvoiceWithExtractedData): LineItem[] {
  return invoice.lineItems || [];
}

export interface AuditIssue {
  type: string;
  severity: 'LOW' | 'MEDIUM' | 'HIGH';
  description: string;
  affectedFields?: string[];
}

export interface RelatedDocument {
  id: string;
  type: string;
  documentNumber: string;
  fileUrl?: string;
  date?: string;
  amount?: number;
  currency?: string;
}

export interface PaymentPrediction {
  predictedPayDate: string;
  confidence: number;
  factors: string[];
  historicalAvgDays?: number;
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
}

/**
 * Analyzes invoice data to detect potential fraud or errors
 * @param invoice The invoice to analyze
 * @returns Fraud score between 0-100 (higher = more suspicious)
 */
function analyzeInvoiceForFraud(
  invoice: InvoiceWithExtractedData
): number {
  let fraudScore = 0;

  // Check for missing critical information
  if (!invoice.invoiceNumber) fraudScore += 15;
  if (!invoice.issueDate) fraudScore += 10;
  if (!invoice.dueDate) fraudScore += 5;
  if (!invoice.amount) fraudScore += 20;

  // Check for suspicious amounts
  if (invoice.amount) {
    // Unusually high amount
    if (invoice.amount > 50000) fraudScore += 25;
    // Round numbers are sometimes suspicious
    if (invoice.amount % 1000 === 0 && invoice.amount > 1000)
      fraudScore += 10;
  }

  // Check for suspicious dates
  if (invoice.issueDate && invoice.dueDate) {
    const issueDate = new Date(invoice.issueDate);
    const dueDate = new Date(invoice.dueDate);

    // Issue date in the future
    if (issueDate > new Date()) fraudScore += 30;

    // Due date before issue date
    if (dueDate < issueDate) fraudScore += 40;

    // Unusually long payment terms (over 90 days)
    const paymentTermDays = differenceInDays(dueDate, issueDate);
    if (paymentTermDays > 90) fraudScore += 15;
    if (paymentTermDays < 0) fraudScore += 30;
  }

  // Check for tax calculation issues
  const financials = getFinancials(invoice);

  if (financials.subtotal && financials.tax && financials.total) {
    const subtotal = parseFloat(financials.subtotal);
    const tax = parseFloat(financials.tax);
    const total = parseFloat(financials.total);

    // Check if total matches subtotal + tax (with small margin for rounding)
    if (Math.abs(subtotal + tax - total) > 1) {
      fraudScore += 25;
    }
  }

  // Check line items
  const lineItems = getLineItems(invoice);
  if (lineItems.length > 0) {
    let lineItemsTotal = 0;

    lineItems.forEach((item) => {
      // Check for missing descriptions
      if (!item.description) fraudScore += 5;

      // Check for price/quantity/total consistency
      if (item.quantity && item.unitPrice && item.totalPrice) {
        const calculatedTotal = item.quantity * item.unitPrice;
        if (Math.abs(calculatedTotal - item.totalPrice) > 0.1) {
          fraudScore += 15;
        }
      }

      // Sum up line items
      if (item.totalPrice) {
        lineItemsTotal += item.totalPrice;
      }
    });

    // Check if line items sum matches invoice total
    if (
      invoice.amount &&
      Math.abs(lineItemsTotal - invoice.amount) > 1
    ) {
      fraudScore += 20;
    }
  }

  // Cap the score at 100
  return Math.min(fraudScore, 100);
}

/**
 * Generates detailed audit issues based on invoice analysis
 * @param invoice The invoice to analyze
 * @param fraudScore The calculated fraud score
 * @returns Array of audit issues
 */
function generateAuditIssues(
  invoice: InvoiceWithExtractedData,
  fraudScore: number
): AuditIssue[] {
  const issues: AuditIssue[] = [];

  // Only generate issues if fraud score is above threshold
  if (fraudScore < 15) return [];

  // Check for missing critical information
  if (!invoice.invoiceNumber) {
    issues.push({
      type: 'MISSING_INVOICE_NUMBER',
      severity: 'HIGH',
      description:
        'Invoice number is missing, which is required for proper accounting and compliance',
      affectedFields: ['invoiceNumber'],
    });
  }

  if (!invoice.issueDate) {
    issues.push({
      type: 'MISSING_ISSUE_DATE',
      severity: 'MEDIUM',
      description:
        'Issue date is missing, which may affect tax period determination',
      affectedFields: ['issueDate'],
    });
  }

  // Check for suspicious amounts
  if (invoice.amount && invoice.amount > 50000) {
    issues.push({
      type: 'UNUSUALLY_HIGH_AMOUNT',
      severity: 'MEDIUM',
      description: `Invoice amount ($${invoice.amount.toLocaleString()}) is unusually high and requires additional verification`,
      affectedFields: ['amount'],
    });
  }

  // Check for suspicious dates
  if (invoice.issueDate && invoice.dueDate) {
    const issueDate = new Date(invoice.issueDate);
    const dueDate = new Date(invoice.dueDate);

    if (issueDate > new Date()) {
      issues.push({
        type: 'FUTURE_DATED_INVOICE',
        severity: 'HIGH',
        description:
          'Invoice is dated in the future, which may indicate fraud or error',
        affectedFields: ['issueDate'],
      });
    }

    if (dueDate < issueDate) {
      issues.push({
        type: 'INVALID_DUE_DATE',
        severity: 'HIGH',
        description:
          'Due date is before issue date, which is logically impossible',
        affectedFields: ['dueDate', 'issueDate'],
      });
    }
  }

  // Check for tax calculation issues
  const financials = getFinancials(invoice);

  if (financials.subtotal && financials.tax && financials.total) {
    const subtotal = parseFloat(financials.subtotal);
    const tax = parseFloat(financials.tax);
    const total = parseFloat(financials.total);

    if (Math.abs(subtotal + tax - total) > 1) {
      issues.push({
        type: 'TAX_CALCULATION_ERROR',
        severity: 'MEDIUM',
        description:
          'Total amount does not match subtotal plus tax, indicating possible calculation errors',
        affectedFields: [
          'financials.subtotal',
          'financials.tax',
          'financials.total',
        ],
      });
    }
  }

  // Check line items
  const lineItems = getLineItems(invoice);
  if (lineItems.length > 0) {
    let lineItemsTotal = 0;

    lineItems.forEach((item, index) => {
      if (item.quantity && item.unitPrice && item.totalPrice) {
        const calculatedTotal = item.quantity * item.unitPrice;
        if (Math.abs(calculatedTotal - item.totalPrice) > 0.1) {
          issues.push({
            type: 'LINE_ITEM_CALCULATION_ERROR',
            severity: 'MEDIUM',
            description: `Line item ${index + 1} (${item.description || 'No description'}) has inconsistent quantity, unit price, and total price`,
            affectedFields: [
              `lineItems[${index}].quantity`,
              `lineItems[${index}].unitPrice`,
              `lineItems[${index}].totalPrice`,
            ],
          });
        }
      }

      if (item.totalPrice) {
        lineItemsTotal += item.totalPrice;
      }
    });

    if (
      invoice.amount &&
      Math.abs(lineItemsTotal - invoice.amount) > 1
    ) {
      issues.push({
        type: 'LINE_ITEMS_TOTAL_MISMATCH',
        severity: 'HIGH',
        description: `Sum of line items (${lineItemsTotal.toFixed(2)}) does not match invoice total (${invoice.amount.toFixed(2)})`,
        affectedFields: ['amount', 'lineItems'],
      });
    }
  }

  // Check for vendor information
  const vendorInfo = getVendorInfo(invoice);
  if (!invoice.vendorName && !vendorInfo.name) {
    issues.push({
      type: 'MISSING_VENDOR_INFORMATION',
      severity: 'MEDIUM',
      description:
        'Vendor information is missing or incomplete, which may affect vendor analytics and payment processing',
      affectedFields: ['vendorName', 'extractedData.vendor'],
    });
  }

  // Check for tax ID
  if (!vendorInfo.taxId) {
    issues.push({
      type: 'MISSING_TAX_ID',
      severity: 'LOW',
      description:
        'Vendor tax ID is missing, which may be required for tax reporting in some jurisdictions',
      affectedFields: ['extractedData.vendor.taxId'],
    });
  }

  return issues;
}

/**
 * Generates realistic related documents based on invoice data
 * @param invoice The invoice to analyze
 * @returns Array of related documents
 */
function generateRelatedDocuments(
  invoice: InvoiceWithExtractedData
): RelatedDocument[] {
  const relatedDocuments: RelatedDocument[] = [];
  const issueDate = invoice.issueDate
    ? new Date(invoice.issueDate)
    : new Date();

  // Generate a purchase order that predates the invoice
  const poDate = subDays(issueDate, 15);
  const poNumber = `PO-${invoice.invoiceNumber?.replace(/[^0-9]/g, '') || '10000'}`;

  relatedDocuments.push({
    id: poNumber,
    type: 'PURCHASE_ORDER',
    documentNumber: poNumber,
    date: poDate.toISOString(),
    amount: invoice.amount ? invoice.amount * 1.02 : 1000, // Slightly higher than invoice (for contingencies)
    currency: invoice.currency || 'USD',
  });

  // For paid invoices, add a payment receipt
  if (invoice.status === 'PAID') {
    const receiptDate = new Date(); // Use current date as payment date
    const receiptNumber = `REC-${invoice.invoiceNumber?.replace(/[^0-9]/g, '') || '10000'}`;

    relatedDocuments.push({
      id: receiptNumber,
      type: 'PAYMENT_RECEIPT',
      documentNumber: receiptNumber,
      date: receiptDate.toISOString(),
      amount: invoice.amount || 0,
      currency: invoice.currency || 'USD',
    });
  }

  // If there's a shipping charge, add a shipping document
  const financials = getFinancials(invoice);
  if (financials.shipping && parseFloat(financials.shipping) > 0) {
    const shipmentDate = subDays(issueDate, 5);
    const trackingNumber = `SHIP-${invoice.id.substring(0, 8)}`;

    relatedDocuments.push({
      id: trackingNumber,
      type: 'SHIPPING_DOCUMENT',
      documentNumber: trackingNumber,
      date: shipmentDate.toISOString(),
      amount: parseFloat(financials.shipping),
      currency: invoice.currency || 'USD',
    });
  }

  return relatedDocuments;
}

/**
 * Generates realistic payment prediction based on invoice data
 * @param invoice The invoice to analyze
 * @returns Payment prediction data
 */
function generatePaymentPrediction(
  invoice: InvoiceWithExtractedData,
  fraudScore: number
): PaymentPrediction {
  // Default values
  let confidence = 0.85;
  let riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' = 'LOW';
  let historicalAvgDays = 30;

  // Determine risk level based on fraud score
  if (fraudScore > 60) {
    riskLevel = 'HIGH';
    confidence = 0.65;
  } else if (fraudScore > 30) {
    riskLevel = 'MEDIUM';
    confidence = 0.75;
  }

  // Calculate predicted payment date
  const dueDate = invoice.dueDate
    ? new Date(invoice.dueDate)
    : addDays(new Date(), 30);
  let predictedPayDate = dueDate;

  // Adjust based on risk level
  if (riskLevel === 'HIGH') {
    // High risk invoices tend to be paid late
    predictedPayDate = addDays(dueDate, 15);
    historicalAvgDays = 45;
  } else if (riskLevel === 'MEDIUM') {
    // Medium risk invoices may be paid slightly late
    predictedPayDate = addDays(dueDate, 5);
    historicalAvgDays = 35;
  } else {
    // Low risk invoices often paid on time or early
    predictedPayDate = subDays(dueDate, 2);
    historicalAvgDays = 28;
  }

  // Generate factors that influenced the prediction
  const factors = [];

  // Add relevant factors based on invoice data
  if (invoice.vendorName) {
    factors.push(`${invoice.vendorName} payment history analysis`);
  } else {
    factors.push('Vendor payment history analysis');
  }

  factors.push('Cash flow projection alignment');

  if (invoice.dueDate) {
    factors.push('Due date proximity assessment');
  }

  if (invoice.amount) {
    if (invoice.amount > 10000) {
      factors.push('High-value invoice prioritization');
    } else if (invoice.amount < 1000) {
      factors.push('Small invoice processing patterns');
    }
  }

  if (invoice.status === 'OVERDUE') {
    factors.push('Overdue status impact analysis');
  }

  const paymentInfo = getPaymentInfo(invoice);
  if (paymentInfo.terms) {
    factors.push(`Payment terms: ${paymentInfo.terms}`);
  } else {
    factors.push('Standard payment terms analysis');
  }

  return {
    predictedPayDate: predictedPayDate.toISOString(),
    confidence,
    factors: factors.slice(0, 5), // Limit to 5 factors
    historicalAvgDays,
    riskLevel,
  };
}

// Note: Exchange rates generation has been moved to backend API
// Currency conversion is now handled asynchronously after invoice processing

/**
 * Generates comprehensive enhanced data for an invoice
 * @param invoice The invoice to analyze
 * @returns Enhanced data structure with AI-powered insights
 */
export function generateEnhancedData(
  invoice: InvoiceWithExtractedData
) {
  // Analyze invoice for potential fraud or errors
  const fraudScore = analyzeInvoiceForFraud(invoice);

  // Determine audit status based on fraud score
  let auditStatus: 'PASS' | 'WARNING' | 'FAIL' = 'PASS';
  if (fraudScore > 60) {
    auditStatus = 'FAIL';
  } else if (fraudScore > 30) {
    auditStatus = 'WARNING';
  }

  // Generate detailed audit issues
  const issues = generateAuditIssues(invoice, fraudScore);

  // Generate related documents
  const relatedDocuments = generateRelatedDocuments(invoice);

  // Generate payment prediction
  const paymentPrediction = generatePaymentPrediction(
    invoice,
    fraudScore
  );

  // Note: Exchange rates are now fetched from backend API asynchronously
  // This reduces upload processing time significantly

  // Return the enhanced data structure
  return {
    meta: {
      audit: {
        status: auditStatus,
        issues,
        fraudIndicators: {
          score: fraudScore,
          flags: issues.map((issue) => issue.type),
        },
        taxCompliance: {
          status: fraudScore > 60 ? 'NON_COMPLIANT' : 'COMPLIANT',
          details:
            fraudScore > 60
              ? 'Potential tax compliance issues detected. Verify tax calculations and documentation.'
              : 'No significant tax compliance issues detected.',
        },
      },
      enhancedData: {
        auditStatus,
        fraudScore,
        relatedDocuments,
        paymentPrediction,
        // exchangeRates moved to async background processing
      },
    },
  };
}
