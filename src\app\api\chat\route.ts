import {
  type Message,
  createDataStreamResponse,
  smoothStream,
  streamText,
} from 'ai';
import { myProvider } from '@/lib/ai/models';
import { systemPrompt } from '@/lib/ai/prompts';
import { getCurrentUserId } from '@/lib/clerk-helpers';
import {
  generateUUID,
  getMostRecentUserMessage,
  sanitizeResponseMessages,
} from '@/lib/utils';

import { generateTitleFromUserMessage } from '@/app/dashboard/chat/actions';
import { createDocument } from '@/lib/ai/tools/create-document';
import { updateDocument } from '@/lib/ai/tools/update-document';
import { requestSuggestions } from '@/lib/ai/tools/request-suggestions';
import { getWeather } from '@/lib/ai/tools/get-weather';
import { listInvoices } from '@/lib/ai/tools/list-invoices';
import { getInvoiceDetails } from '@/lib/ai/tools/get-invoice-details';
import { createInvoice } from '@/lib/ai/tools/create-invoice';
import { updateInvoiceStatus } from '@/lib/ai/tools/update-invoice-status';
import { deleteInvoice } from '@/lib/ai/tools/delete-invoice';
import { categorizeInvoice } from '@/lib/ai/tools/categorize-invoice';
import { createFolder } from '@/lib/ai/tools/create-folder';
import { listFolders } from '@/lib/ai/tools/list-folders';
import { addInvoiceToFolder } from '@/lib/ai/tools/add-invoice-to-folder';
import { removeInvoiceFromFolder } from '@/lib/ai/tools/remove-invoice-from-folder';
import { getInvoiceStats } from '@/lib/ai/tools/get-invoice-stats';
import { getVendorSpendAnalysis } from '@/lib/ai/tools/get-vendor-spend-analysis';
import { getCategoryBreakdown } from '@/lib/ai/tools/get-category-breakdown';
import { forecastRevenue } from '@/lib/ai/tools/forecast-revenue';
import { computeCashFlow } from '@/lib/ai/tools/compute-cash-flow';
import { computeProfitAndLoss } from '@/lib/ai/tools/compute-profit-and-loss';
import { balanceSheetSnapshot } from '@/lib/ai/tools/balance-sheet-snapshot';
import { generateReport } from '@/lib/ai/tools/generate-report';
import { listReports } from '@/lib/ai/tools/list-reports';
import { downloadReport } from '@/lib/ai/tools/download-report';
import { scheduleReport } from '@/lib/ai/tools/schedule-report';
import { cancelScheduledReport } from '@/lib/ai/tools/cancel-scheduled-report';
import { sendReportEmail } from '@/lib/ai/tools/send-report-email';
import { emailReport } from '@/lib/ai/tools/email-report';
import db from '@/db/db';

export const maxDuration = 60;

export async function POST(request: Request) {
  const {
    id,
    messages,
    selectedChatModel,
  }: {
    id: string;
    messages: Array<Message>;
    selectedChatModel: string;
  } = await request.json();

  const userId = await getCurrentUserId();

  if (!userId) {
    return new Response('Unauthorized', { status: 401 });
  }

  // Check chat usage limits before processing
  const { checkCurrentUserChatUsage, incrementCurrentUserChatUsage } =
    await import('@/lib/services/usage-service');

  const usageCheck = await checkCurrentUserChatUsage();

  if (!usageCheck.allowed) {
    return new Response(usageCheck.message || 'Chat limit exceeded', {
      status: 429,
    });
  }

  const userMessage = getMostRecentUserMessage(messages);

  if (!userMessage) {
    return new Response('No user message found', { status: 400 });
  }

  // Check if chat exists
  const chat = await db.chat.findUnique({
    where: { id },
  });

  // If chat doesn't exist, create it
  if (!chat) {
    const title = await generateTitleFromUserMessage({
      message: userMessage,
    });
    await db.chat.create({
      data: {
        id,
        userId,
        title,
        createdAt: new Date(),
      },
    });
  }

  // Check if the message already exists (for edited messages)
  const existingMessage = await db.message.findUnique({
    where: { id: userMessage.id },
  });

  if (existingMessage) {
    // Update the existing message
    await db.message.update({
      where: { id: userMessage.id },
      data: {
        content: userMessage.content,
      },
    });
  } else {
    // Create a new message
    await db.message.create({
      data: {
        id: userMessage.id,
        chatId: id,
        role: userMessage.role,
        content: userMessage.content,
        createdAt: new Date(),
      },
    });
  }

  return createDataStreamResponse({
    execute: (dataStream) => {
      const result = streamText({
        model: myProvider.languageModel(selectedChatModel),
        system: systemPrompt({ selectedChatModel }),
        messages,
        maxSteps:
          selectedChatModel === 'chat-model-reasoning' ? 10 : 5, // Increase max steps for reasoning model
        experimental_activeTools:
          selectedChatModel === 'chat-model-reasoning'
            ? []
            : [
                'getWeather',
                'createDocument',
                'updateDocument',
                'requestSuggestions',
                'listInvoices',
                'getInvoiceDetails',
                'createInvoice',
                'updateInvoiceStatus',
                'deleteInvoice',
                'categorizeInvoice',
                'createFolder',
                'listFolders',
                'addInvoiceToFolder',
                'removeInvoiceFromFolder',
                'getInvoiceStats',
                'getVendorSpendAnalysis',
                'getCategoryBreakdown',
                'forecastRevenue',
                'computeCashFlow',
                'computeProfitAndLoss',
                'balanceSheetSnapshot',
                'generateReport',
                'listReports',
                'downloadReport',
                'scheduleReport',
                'cancelScheduledReport',
                'sendReportEmail',
                'emailReport',
              ],
        experimental_transform: smoothStream({ chunking: 'word' }),
        experimental_generateMessageId: generateUUID,
        tools: {
          getWeather,
          createDocument: createDocument({ userId, dataStream }),
          updateDocument: updateDocument({ userId, dataStream }),
          requestSuggestions: requestSuggestions({
            userId,
            dataStream,
          }),
          listInvoices,
          getInvoiceDetails,
          createInvoice: createInvoice({
            userId: userId || '',
            dataStream,
          }),
          updateInvoiceStatus,
          deleteInvoice,
          categorizeInvoice,
          createFolder,
          listFolders,
          addInvoiceToFolder,
          removeInvoiceFromFolder,
          getInvoiceStats,
          getVendorSpendAnalysis,
          getCategoryBreakdown,
          forecastRevenue,
          computeCashFlow,
          computeProfitAndLoss,
          balanceSheetSnapshot,
          generateReport: generateReport({
            userId: userId || '',
            dataStream,
          }),
          listReports,
          downloadReport,
          scheduleReport,
          cancelScheduledReport,
          sendReportEmail: sendReportEmail({
            userId: userId || '',
            dataStream,
          }),
          emailReport: emailReport({
            userId: userId || '',
            dataStream,
          }),
        },
        onFinish: async ({ response, reasoning }) => {
          if (userId) {
            try {
              const sanitizedResponseMessages =
                sanitizeResponseMessages({
                  messages: response.messages,
                  reasoning,
                });

              // Save all response messages
              await db.message.createMany({
                data: sanitizedResponseMessages.map((message) => {
                  return {
                    id: message.id,
                    chatId: id,
                    role: message.role,
                    content: JSON.stringify(message.content),
                    createdAt: new Date(),
                  };
                }),
              });

              // Increment chat usage counter after successful response
              await incrementCurrentUserChatUsage();
            } catch (error) {
              console.error('Failed to save chat messages:', error);
            }
          }
        },
        experimental_telemetry: {
          isEnabled: true,
          functionId:
            selectedChatModel === 'chat-model-reasoning'
              ? 'stream-text-reasoning'
              : 'stream-text',
        },
      });

      result.consumeStream();

      result.mergeIntoDataStream(dataStream, {
        sendReasoning: selectedChatModel === 'chat-model-reasoning',
      });
    },
    onError: () => {
      return 'Oops, an error occurred!';
    },
  });
}

export async function DELETE(request: Request) {
  const { searchParams } = new URL(request.url);
  const id = searchParams.get('id');

  if (!id) {
    return new Response('Not Found', { status: 404 });
  }

  const userId = await getCurrentUserId();

  if (!userId) {
    return new Response('Unauthorized', { status: 401 });
  }

  try {
    const chat = await db.chat.findUnique({
      where: { id },
    });

    if (!chat) {
      return new Response('Chat not found', { status: 404 });
    }

    if (chat.userId !== userId) {
      return new Response('Unauthorized', { status: 401 });
    }

    // Delete the chat (cascade will delete messages and votes)
    await db.chat.delete({
      where: { id },
    });

    return new Response('Chat deleted', { status: 200 });
  } catch (error) {
    console.error('Error deleting chat:', error);
    return new Response(
      'An error occurred while processing your request',
      {
        status: 500,
      }
    );
  }
}
