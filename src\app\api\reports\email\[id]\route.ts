import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import db from '@/db/db';
import { Resend } from 'resend';
import { ScheduledReportEmailTemplate } from '@/lib/email/templates/scheduled-report-email';

// Initialize Resend
const resend = new Resend(process.env.RESEND_API_KEY);

export async function POST(req: NextRequest, props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  try {
    const { userId: clerkUserId } = await auth();

    if (!clerkUserId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the user from the database
    const user = await db.user.findUnique({
      where: { clerkId: clerkUserId },
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const { id } = params;

    // Get email data from request body
    const { emailAddresses, subject, message, includeExcel = false } = await req.json();

    if (!emailAddresses || !Array.isArray(emailAddresses) || emailAddresses.length === 0) {
      return NextResponse.json(
        { error: 'Email addresses are required' },
        { status: 400 }
      );
    }

    // Find the report
    const report = await db.report.findUnique({
      where: {
        id,
        userId: user.id
      }
    });

    if (!report) {
      return NextResponse.json({ error: 'Report not found' }, { status: 404 });
    }

    // Check if the report has a file URL
    if (!report.fileUrl) {
      // Generate the report first
      const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/reports/${id}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${process.env.API_SECRET_KEY}`,
        },
      });

      if (!response.ok) {
        return NextResponse.json(
          { error: 'Failed to generate report' },
          { status: 500 }
        );
      }

      // Get the updated report with file URL
      const updatedReport = await db.report.findUnique({
        where: { id }
      });

      if (!updatedReport || !updatedReport.fileUrl) {
        return NextResponse.json(
          { error: 'Failed to generate report file' },
          { status: 500 }
        );
      }
    }

    // Get the report with file URL
    const reportWithUrl = await db.report.findUnique({
      where: { id }
    });

    if (!reportWithUrl || !reportWithUrl.fileUrl) {
      return NextResponse.json(
        { error: 'Report file not found' },
        { status: 404 }
      );
    }

    // Generate Excel URL if needed
    const excelUrl = includeExcel ? reportWithUrl.fileUrl.replace('.pdf', '.xlsx') : undefined;

    // Send email using Resend
    const { data, error } = await resend.emails.send({
      from: `Billix Reports <${process.env.EMAIL_FROM || '<EMAIL>'}>`,
      to: emailAddresses,
      subject: subject || `Your Report: ${reportWithUrl.title} is Ready`,
      react: ScheduledReportEmailTemplate({
        reportName: reportWithUrl.title,
        pdfUrl: reportWithUrl.fileUrl,
        excelUrl: excelUrl,
        userName: `${user.firstName || ''} ${user.lastName || ''}`.trim() || 'there',
        customMessage: message || 'Here is your requested report. You can download the PDF or Excel versions using the buttons below.',
        includeCompanyLogo: true,
        templateStyle: 'professional',
        primaryColor: '#3C5A99',
        includeMetadata: true,
        generatedDate: new Date(),
        appUrl: process.env.NEXT_PUBLIC_APP_URL || 'https://billix.io',
      }),
    });

    if (error) {
      console.error('Error sending email with Resend:', error);
      return NextResponse.json(
        { error: `Failed to send email: ${error.message}` },
        { status: 500 }
      );
    }

    // Log the email in the database
    await db.reportEmail.create({
      data: {
        reportId: id,
        userId: user.id,
        emailAddresses: emailAddresses.join(','),
        subject: subject || `Your Report: ${reportWithUrl.title} is Ready`,
        message: message || '',
        sentAt: new Date(),
      },
    });

    return NextResponse.json({
      success: true,
      messageId: data?.id,
      message: `Email sent successfully to ${emailAddresses.length} recipient(s)`,
    });
  } catch (error) {
    console.error('Error sending report email:', error);
    return NextResponse.json(
      { error: 'Failed to send report email' },
      { status: 500 }
    );
  }
}
