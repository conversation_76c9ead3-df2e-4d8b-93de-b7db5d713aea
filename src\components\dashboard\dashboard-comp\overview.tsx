'use client';

import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendItem,
} from '@/components/dashboard/dashboard-comp/chart';
import {
  Area,
  AreaChart,
  ResponsiveContainer,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
} from 'recharts';
import { useEffect, useState } from 'react';
import { useTheme } from 'next-themes';

export function Overview() {
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { theme } = useTheme();

  useEffect(() => {
    setLoading(true);
    fetch('/api/invoices/stats')
      .then(async (res) => {
        if (!res.ok) throw new Error('Failed to fetch chart data');
        const apiData = await res.json();
        // Map API monthly data to chart format
        const chartData = (apiData.monthly || []).map((m: any) => ({
          name: m.month.slice(0, 3),
          income: m.paid ?? 0,
          expenses: (m.pending ?? 0) + (m.overdue ?? 0),
        }));

        setData(chartData);
        setError(null);
      })
      .catch((err) => setError(err.message))
      .finally(() => setLoading(false));
  }, []);

  if (loading)
    return (
      <div className="text-center py-8 text-muted-foreground">
        Loading chart...
      </div>
    );
  if (error)
    return (
      <div className="text-center text-destructive py-8">{error}</div>
    );

  // Get CSS custom properties for theming
  const primaryColor = 'hsl(var(--primary))';
  const accentColor = 'hsl(var(--chart-2))';
  const borderColor = 'hsl(var(--border))';
  const textColor = theme === 'dark' ? '#ffffff' : '#000000';
  const mutedColor = 'hsl(var(--muted-foreground))';

  return (
    <div className="h-[400px] w-full min-h-[350px] px-1 py-2">
      <div className="mb-3 flex justify-center gap-8">
        <div className="flex items-center gap-2">
          <div className="h-3 w-3 rounded-full bg-primary" />
          <span className="text-sm font-medium text-foreground">
            Income
          </span>
        </div>
        <div className="flex items-center gap-2">
          <div
            className="h-3 w-3 rounded-full"
            style={{ backgroundColor: accentColor }}
          />
          <span className="text-sm font-medium text-foreground">
            Expenses
          </span>
        </div>
      </div>
      <ResponsiveContainer width="100%" height="90%">
        <AreaChart
          data={data}
          margin={{
            top: 20,
            right: 10,
            left: 45,
            bottom: 30,
          }}
        >
          <defs>
            <linearGradient
              id="colorIncome"
              x1="0"
              y1="0"
              x2="0"
              y2="1"
            >
              <stop
                offset="5%"
                stopColor={primaryColor}
                stopOpacity={0.8}
              />
              <stop
                offset="95%"
                stopColor={primaryColor}
                stopOpacity={0.1}
              />
            </linearGradient>
            <linearGradient
              id="colorExpenses"
              x1="0"
              y1="0"
              x2="0"
              y2="1"
            >
              <stop
                offset="5%"
                stopColor={accentColor}
                stopOpacity={0.8}
              />
              <stop
                offset="95%"
                stopColor={accentColor}
                stopOpacity={0.1}
              />
            </linearGradient>
          </defs>
          <CartesianGrid
            strokeDasharray="3 3"
            stroke={borderColor}
            vertical={false}
            horizontal={true}
            className="dashboard-chart-grid"
          />
          <XAxis
            dataKey="name"
            axisLine={false}
            tickLine={false}
            tick={{
              fill: textColor,
              fontSize: 12,
              fontWeight: 500,
            }}
          />
          <YAxis
            axisLine={false}
            tickLine={false}
            tick={{
              fill: textColor,
              fontSize: 12,
              fontWeight: 500,
            }}
            tickFormatter={(value) =>
              `$${(value / 1000).toFixed(0)}k`
            }
            width={40}
          />
          <Tooltip
            content={
              <CustomTooltip active={false} payload={[]} label="" />
            }
          />
          <Area
            type="monotone"
            dataKey="income"
            stroke={primaryColor}
            strokeWidth={2}
            fill="url(#colorIncome)"
            fillOpacity={0.6}
            className="dashboard-chart-income"
          />
          <Area
            type="monotone"
            dataKey="expenses"
            stroke={accentColor}
            strokeWidth={2}
            fill="url(#colorExpenses)"
            fillOpacity={0.6}
            className="dashboard-chart-expenses"
          />
        </AreaChart>
      </ResponsiveContainer>
    </div>
  );
}

function CustomTooltip({
  active,
  payload,
  label,
}: {
  active: boolean;
  payload: Array<{ value: number; dataKey: string }>;
  label: string;
}) {
  if (active && payload && payload.length) {
    return (
      <div className="rounded-lg border bg-background p-3 shadow-lg">
        <div className="font-medium text-foreground mb-2">
          {label}
        </div>
        <div className="flex flex-col gap-1">
          {payload.map((entry, index) => (
            <div key={index} className="flex items-center gap-2">
              <div
                className="h-3 w-3 rounded-full"
                style={{
                  backgroundColor:
                    entry.dataKey === 'income'
                      ? 'hsl(var(--primary))'
                      : 'hsl(var(--chart-2))',
                }}
              />
              <div className="text-muted-foreground capitalize">
                {entry.dataKey}:
              </div>
              <div className="font-medium text-foreground">
                ${entry.value.toLocaleString()}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return null;
}
