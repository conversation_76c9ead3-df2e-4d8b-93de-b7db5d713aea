import { tool } from 'ai';
import { z } from 'zod';
import db from '@/db/db';
import { getAuthenticatedUserId, checkPermission } from '@/lib/auth-helpers';

export const getInvoiceDetails = tool({
  description: 'Fetches full details for one invoice including line items, vendor info, notes, and metadata. Returns both the issue date (from the invoice document) and the creation date (when it was added to the system).',
  parameters: z.object({
    invoiceId: z.string().describe('The ID of the invoice to retrieve'),
  }),
  execute: async ({ invoiceId }) => {
    try {
      // Get the authenticated user ID
      const userId = await getAuthenticatedUserId();

      // Query the database for the invoice with the given ID
      let invoice = await db.invoice.findUnique({
        where: { id: invoiceId },
        include: {
          lineItems: true,
          category: true,
          vendor: true,
          folders: {
            include: {
              folder: true,
            },
          },
        },
      });

      // If not found by ID, try to find by invoice number
      if (!invoice) {
        invoice = await db.invoice.findFirst({
          where: {
            invoiceNumber: invoiceId,
            userId
          },
          include: {
            lineItems: true,
            category: true,
            vendor: true,
            folders: {
              include: {
                folder: true,
              },
            },
          },
        });
      }

      if (!invoice) {
        return {
          success: false,
          message: `Invoice with ID or number ${invoiceId} not found. Please check the ID and try again.`,
        };
      }

      // Check if the invoice belongs to the current user
      await checkPermission(invoice.userId);

      // Format the response
      return {
        id: invoice.id,
        number: invoice.invoiceNumber || '',
        vendorName: invoice.vendorName || '',
        amount: invoice.amount || 0,
        currency: invoice.currency || 'USD',
        status: invoice.status,
        category: invoice.category?.name,
        // Document dates (from the invoice itself)
        documentDates: {
          issueDate: invoice.issueDate ? invoice.issueDate.toISOString().split('T')[0] : null,
          dueDate: invoice.dueDate ? invoice.dueDate.toISOString().split('T')[0] : null,
        },
        // System dates (when the invoice was added/modified in the system)
        systemDates: {
          createdAt: invoice.createdAt.toISOString().split('T')[0],
          updatedAt: invoice.updatedAt.toISOString().split('T')[0],
          createdAtFull: invoice.createdAt.toISOString(),
          updatedAtFull: invoice.updatedAt.toISOString(),
        },
        notes: invoice.notes || '',
        lineItems: invoice.lineItems.map(item => ({
          id: item.id,
          description: item.description,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          amount: item.totalPrice,
        })),
        vendor: invoice.vendor ? {
          id: invoice.vendor.id,
          name: invoice.vendor.name,
          email: invoice.vendor.email,
          phone: invoice.vendor.phone,
          website: invoice.vendor.website,
        } : null,
        folders: invoice.folders.map(folderInvoice => ({
          id: folderInvoice.folder.id,
          name: folderInvoice.folder.name,
        })),
        // For backward compatibility
        issueDate: invoice.issueDate ? invoice.issueDate.toISOString().split('T')[0] : null,
        dueDate: invoice.dueDate ? invoice.dueDate.toISOString().split('T')[0] : null,
        createdAt: invoice.createdAt.toISOString(),
        updatedAt: invoice.updatedAt.toISOString(),
        systemDate: {
          current: new Date().toISOString(),
          currentFormatted: new Date().toISOString().split('T')[0],
          year: new Date().getFullYear(),
          month: new Date().getMonth() + 1, // JavaScript months are 0-indexed
          day: new Date().getDate()
        },
        message: "Note: For reports and analyses, the system uses the creation date (when the invoice was added to the system), not the issue date shown on the invoice."
      };
    } catch (error) {
      console.error('Error getting invoice details:', error);
      return {
        success: false,
        message: 'Failed to retrieve invoice details. Please try again.',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  },
});
