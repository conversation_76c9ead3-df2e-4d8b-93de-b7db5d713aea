import { tool } from 'ai';
import { z } from 'zod';
import db from '@/db/db';
import { getAuthenticatedUserId } from '@/lib/auth-helpers';
import { generateUUID } from '@/lib/utils';
import { ReportType, ChartType, ScheduleFrequency } from '@/lib/report-types';

export const scheduleReport = tool({
  description: 'Sets up an automated schedule (daily/weekly/monthly) to generate and email a chosen report type to a specified address.',
  parameters: z.object({
    reportType: z.enum([
      ReportType.EXPENSES,
      ReportType.VENDOR_ANALYSIS,
      ReportType.CATEGORY_ANALYSIS,
      ReportType.CASH_FLOW,
      ReportType.SALES,
      ReportType.TAX,
      ReportType.CUSTOM,
      // Include legacy types for backward compatibility
      ReportType.INVOICE_SUMMARY,
      ReportType.PROFIT_LOSS,
      ReportType.CATEGORY_BREAKDOWN,
      ReportType.BALANCE_SHEET,
    ]).describe('The type of report to generate'),
    title: z.string().describe('The title of the scheduled report'),
    description: z.string().optional().describe('Optional description for the scheduled report'),
    frequency: z.enum([
      ScheduleFrequency.DAILY,
      ScheduleFrequency.WEEKLY,
      ScheduleFrequency.MONTHLY,
      ScheduleFrequency.QUARTERLY,
    ]).describe('How often to generate and send the report'),
    dayOfWeek: z.number().min(0).max(6).optional().describe('Day of week (0-6, Sunday to Saturday) for weekly schedules'),
    dayOfMonth: z.number().min(1).max(31).optional().describe('Day of month (1-31) for monthly schedules'),
    timeOfDay: z.string().regex(/^([01]\d|2[0-3]):([0-5]\d)$/).describe('Time of day in 24-hour format (HH:MM)'),
    emailAddresses: z.array(z.string().email()).min(1).describe('Email addresses to send the report to'),
    includeCharts: z.boolean().default(true).optional().describe('Whether to include charts in the report'),
    chartTypes: z.array(z.enum([
      ChartType.BAR,
      ChartType.LINE,
      ChartType.PIE,
      ChartType.STACKED_BAR,
      ChartType.AREA,
    ])).optional().describe('Types of charts to include in the report'),
    format: z.enum(['PDF', 'CSV', 'EXCEL']).default('PDF').optional().describe('The format of the report'),
    relativePeriod: z.enum(['CURRENT_MONTH', 'PREVIOUS_MONTH', 'CURRENT_QUARTER', 'PREVIOUS_QUARTER', 'CURRENT_YEAR', 'PREVIOUS_YEAR', 'LAST_30_DAYS', 'LAST_90_DAYS']).optional().describe('Relative time period for the report data'),
  }),
  execute: async ({
    reportType,
    title,
    description,
    frequency,
    dayOfWeek,
    dayOfMonth,
    timeOfDay,
    emailAddresses,
    format = 'PDF',
  }) => {
    try {
      // Get the authenticated user ID
      const userId = await getAuthenticatedUserId();

      // Validate schedule parameters based on frequency
      if (frequency === ScheduleFrequency.WEEKLY && dayOfWeek === undefined) {
        return {
          success: false,
          message: 'Day of week is required for weekly schedules.',
        };
      }

      if (frequency === ScheduleFrequency.MONTHLY && dayOfMonth === undefined) {
        return {
          success: false,
          message: 'Day of month is required for monthly schedules.',
        };
      }

      // Generate a unique ID for the schedule
      const scheduleId = generateUUID();

      // Calculate the next run time
      const nextRunTime = calculateNextRunTime(frequency, dayOfWeek, dayOfMonth, timeOfDay);

      // First create a report
      const report = await db.report.create({
        data: {
          id: scheduleId,
          userId,
          title,
          description: description || '',
          reportType,
          format,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      });

      // Then create the schedule linked to the report
      await db.scheduledReport.create({
        data: {
          id: generateUUID(),
          userId,
          reportId: report.id,
          frequency,
          dayOfWeek,
          dayOfMonth,
          timeOfDay,
          emailAddresses: emailAddresses.join(','),
          nextRunTime,
          active: true,
        },
      });

      return {
        id: scheduleId,
        title,
        reportType,
        frequency,
        nextRunTime: nextRunTime.toISOString(),
        emailAddresses,
        message: `Report "${title}" scheduled successfully. It will be sent ${frequency.toLowerCase()} to ${emailAddresses.join(', ')}.`,
      };
    } catch (error) {
      console.error('Error scheduling report:', error);
      return {
        success: false,
        message: 'Failed to schedule report. Please try again.',
        error: error instanceof Error ? error.message : String(error),
      };
    }
  },
});

// Helper function to calculate the next run time based on schedule parameters
function calculateNextRunTime(
  frequency: ScheduleFrequency,
  dayOfWeek?: number,
  dayOfMonth?: number,
  timeOfDay?: string
): Date {
  const now = new Date();
  const [hours, minutes] = timeOfDay ? timeOfDay.split(':').map(Number) : [0, 0];

  const result = new Date(now);
  result.setHours(hours, minutes, 0, 0);

  // If the time is in the past, start from tomorrow
  if (result <= now) {
    result.setDate(result.getDate() + 1);
  }

  switch (frequency) {
    case ScheduleFrequency.DAILY:
      // Already set for the next day
      break;

    case ScheduleFrequency.WEEKLY:
      if (dayOfWeek !== undefined) {
        const currentDay = result.getDay();
        const daysToAdd = (dayOfWeek - currentDay + 7) % 7;
        result.setDate(result.getDate() + daysToAdd);
      }
      break;

    case ScheduleFrequency.MONTHLY:
      if (dayOfMonth !== undefined) {
        result.setDate(dayOfMonth);
        // If the day has already passed this month, move to next month
        if (result <= now) {
          result.setMonth(result.getMonth() + 1);
        }
      }
      break;

    case ScheduleFrequency.QUARTERLY:
      if (dayOfMonth !== undefined) {
        const currentMonth = result.getMonth();
        const currentQuarter = Math.floor(currentMonth / 3);
        const nextQuarterStartMonth = (currentQuarter + 1) * 3;

        result.setMonth(nextQuarterStartMonth);
        result.setDate(dayOfMonth);
      }
      break;
  }

  return result;
}
