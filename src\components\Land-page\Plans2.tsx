'use client';

import React, { useState, useEffect } from 'react';
import {
  FileText,
  BarChart2,
  Building2,
  ArrowRight,
  Sparkles,
} from 'lucide-react';
import { initializePaddle, Paddle } from '@paddle/paddle-js';
import { useUser } from '@clerk/nextjs';
import { toast } from 'sonner';
// import Colors from '../theme/Colors';

interface PlanFeature {
  text: string;
}

interface Plan {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  monthlyPrice: number;
  yearlyPrice: number;
  monthlyPriceId: string;
  yearlyPriceId: string;
  features: PlanFeature[];
  highlighted?: boolean;
}

export default function Plans2() {
  const [isMonthly, setIsMonthly] = useState(true);
  const [paddle, setPaddle] = useState<Paddle>();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [checkoutLoading, setCheckoutLoading] = useState<
    string | null
  >(null);
  const { isSignedIn } = useUser();

  useEffect(() => {
    console.log(
      'Initializing Paddle with environment:',
      process.env.NODE_ENV
    );
    console.log(
      'Paddle client key available:',
      !!process.env.NEXT_PUBLIC_PADDLE_CLIENT_KEY
    );

    initializePaddle({
      environment:
        process.env.NODE_ENV === 'production'
          ? 'production'
          : 'sandbox',
      token: process.env.NEXT_PUBLIC_PADDLE_CLIENT_KEY!,
    })
      .then((paddle) => {
        console.log('Paddle initialized successfully:', paddle);
        setPaddle(paddle);
        setLoading(false);
      })
      .catch((err) => {
        console.error('Paddle initialization error:', err);
        setError('Failed to initialize Paddle');
        setLoading(false);
      });
  }, []);

  // Pricing data in a structured format for easy modification
  const plans: Plan[] = [
    {
      id: 'starter',
      title: 'Starter',
      description:
        'Perfect for small businesses processing fewer than 100 invoices per month.',
      icon: <FileText className="w-5 h-5 sm:w-6 sm:h-6" />,
      monthlyPrice: 29,
      yearlyPrice: 290,
      monthlyPriceId: 'pri_01jz83p3v1rs5r3xskt9z4ww50', // TODO: Replace with actual Paddle monthly price ID
      yearlyPriceId: 'pri_01jz83xxfn40khba0cvdw42t1x', // TODO: Replace with actual Paddle yearly price ID
      features: [
        { text: 'AI-powered data extraction' },
        { text: 'Basic invoice categorization' },
        { text: 'Up to 100 invoice uploads/month' },
        { text: 'CSV & Excel export formats' },
        { text: 'Basic financial dashboard' },
        { text: 'Email support' },
      ],
    },
    {
      id: 'business',
      title: 'Business',
      description:
        'Ideal for growing businesses with advanced analytics needs and higher volume.',
      icon: <BarChart2 className="w-5 h-5 sm:w-6 sm:h-6" />,
      monthlyPrice: 79,
      yearlyPrice: 790,
      monthlyPriceId: 'pri_01jz83q8503fhrqh3gwsqa23zb', // TODO: Replace with actual Paddle monthly price ID
      yearlyPriceId: 'pri_01jz83yvxaamh0t9zymtykbqcz', // TODO: Replace with actual Paddle yearly price ID
      features: [
        { text: 'Advanced data extraction & OCR' },
        { text: 'Custom invoice categorization' },
        { text: 'Up to 1,000 invoice uploads/month' },
        { text: 'All export formats + integrations' },
        { text: 'Full financial analytics suite' },
        { text: 'Anomaly & fraud detection' },
      ],
      highlighted: true,
    },
    {
      id: 'enterprise',
      title: 'Enterprise',
      description:
        'Comprehensive solution for large organizations with custom requirements.',
      icon: <Building2 className="w-5 h-5 sm:w-6 sm:h-6" />,
      monthlyPrice: 199,
      yearlyPrice: 1990,
      monthlyPriceId: 'pri_01jz83r7fw92rb1pb9a3qt7ty3', // TODO: Replace with actual Paddle monthly price ID
      yearlyPriceId: 'pri_01jz83zhw7065b2dzyhcbx77h2', // TODO: Replace with actual Paddle yearly price ID
      features: [
        { text: 'Enterprise-grade data extraction' },
        { text: 'Custom AI model training' },
        { text: 'Unlimited invoice processing' },
        { text: 'Advanced API access' },
        { text: 'Predictive financial analytics' },
        { text: 'Dedicated account manager' },
      ],
    },
  ];

  // Calculate price based on billing cycle
  const getPrice = (plan: {
    monthlyPrice: number;
    yearlyPrice: number;
  }) => {
    return isMonthly ? plan.monthlyPrice : plan.yearlyPrice;
  };

  // Apply yearly discount message if applicable
  const getPriceLabel = () => {
    return isMonthly ? '/month' : '/year';
  };

  const switchBilling = () => {
    setIsMonthly(!isMonthly);
  };

  const handleCheckout = async (priceId: string, planId: string) => {
    console.log('handleCheckout called with:', {
      priceId,
      planId,
      paddleReady: !!paddle,
      isSignedIn,
    });

    if (!paddle) {
      console.error('Paddle not initialized');
      toast.error('Payment system not initialized');
      return;
    }

    if (!isSignedIn) {
      console.error('User not signed in');
      toast.error('Please sign in to subscribe');
      return;
    }

    setCheckoutLoading(planId);

    try {
      // Create checkout session on server side for better security
      const response = await fetch('/api/paddle/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          priceId,
          planId,
          billingCycle: isMonthly ? 'monthly' : 'yearly',
        }),
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(
          result.error || 'Failed to create checkout session'
        );
      }

      // Open Paddle checkout with the transaction data
      console.log(
        'Opening Paddle checkout with transaction ID:',
        result.data.id
      );

      try {
        await paddle.Checkout.open({
          transactionId: result.data.id,
          settings: {
            displayMode: 'overlay',
            theme: 'dark',
            successUrl: `${window.location.origin}/dashboard/subscription?success=true&session_id=${result.data.id}`,
          },
        });
        console.log('Paddle checkout opened successfully');
      } catch (paddleError) {
        console.error('Paddle checkout error:', paddleError);

        // Fallback: redirect to Paddle checkout URL if overlay fails
        if (result.data?.checkout?.url) {
          console.log(
            'Falling back to direct checkout URL:',
            result.data.checkout.url
          );
          window.location.href = result.data.checkout.url;
          return;
        }

        throw new Error('Failed to open Paddle checkout');
      }
    } catch (error) {
      console.error('Checkout error:', error);
      toast.error(
        error instanceof Error
          ? error.message
          : 'Failed to start checkout'
      );
    } finally {
      setCheckoutLoading(null);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-background">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-4"></div>
          <span className="text-white">
            Loading payment options...
          </span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-background">
        <div className="text-center">
          <span className="text-red-500 mb-4 block">{error}</span>
          <button
            onClick={() => window.location.reload()}
            className="text-white underline"
          >
            Try again
          </button>
        </div>
      </div>
    );
  }

  return (
    <section className="py-12 sm:py-16 md:py-20 px-4 bg-black overflow-hidden">
      <div className="max-w-7xl mx-auto">
        <div className="relative mb-8 sm:mb-12">
          <div className="inline-flex items-center gap-2 px-3 sm:px-4 py-1.5 sm:py-2 rounded-full bg-neutral-900 border border-neutral-700 mb-3 sm:mb-4">
            <Sparkles className="w-3 h-3 sm:w-4 sm:h-4 text-white" />
            <span className="text-xs sm:text-sm text-white font-medium">
              Pricing Plans
            </span>
          </div>
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-white mb-2">
            Choose Your Plan
          </h2>
          <p className="text-sm md:text-base text-gray-400 max-w-lg">
            Select the right plan for your invoice management and
            financial analytics needs.
          </p>
          <div
            className="absolute top-20 right-0 text-[180px] font-bold select-none hidden lg:block z-0 overflow-hidden"
            style={{
              background:
                'linear-gradient(to bottom, rgba(255,255,255,0.2), transparent)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text',
              color: 'transparent',
              maxWidth: '100%',
            }}
          >
            PRICING
          </div>
        </div>
        <div className="flex justify-center gap-4 mb-8 sm:mb-12">
          <div className="bg-neutral-800 rounded-full p-1 inline-flex">
            <div className="relative flex w-48 h-10">
              <div
                className="absolute top-0 left-0 w-1/2 h-full bg-black rounded-full transition-all duration-300"
                style={{
                  transform: `translateX(${isMonthly ? '0%' : '100%'})`,
                }}
              />
              <button
                className={`w-1/2 px-4 sm:px-6 py-1.5 sm:py-2 text-xs sm:text-sm rounded-full relative z-10 transition-colors duration-300 ${isMonthly ? 'text-white' : 'text-gray-400'}`}
                onClick={switchBilling}
              >
                Monthly
              </button>
              <button
                className={`w-1/2 px-4 sm:px-6 py-1.5 sm:py-2 text-xs sm:text-sm rounded-full relative z-10 transition-colors duration-300 ${!isMonthly ? 'text-white' : 'text-gray-400'}`}
                onClick={switchBilling}
              >
                Annual
              </button>
            </div>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-white">
          {plans.map((plan) => (
            <div
              key={plan.id}
              className="relative flex flex-col justify-between p-6 min-h-[500px] bg-neutral-900/60 hover:bg-neutral-800/60 backdrop-blur-xl rounded-[20px] shadow-[0_0_0_0,inset_0_0_30px_rgba(200,200,200,0.1)] border border-neutral-700 overflow-hidden transition-colors duration-300"
              style={{ zIndex: 2 }}
            >
              <div className="flex flex-col gap-2 mb-6">
                <div className="flex items-center gap-2 mb-2">
                  <div className="bg-black p-2 rounded-lg inline-block">
                    {plan.icon}
                  </div>
                  <h3 className="text-lg sm:text-xl font-bold">
                    {plan.title}
                  </h3>
                  {plan.highlighted && (
                    <span className="ml-2 px-2 py-0.5 rounded-full text-xs font-semibold bg-blue-500/10 text-blue-300 border border-blue-400/30">
                      Most popular
                    </span>
                  )}
                </div>
                <div className="flex items-end gap-2">
                  <span className="text-4xl font-bold text-white">
                    ${getPrice(plan)}
                  </span>
                  <span className="text-base text-gray-400">
                    {getPriceLabel()}
                  </span>
                </div>
                {!isMonthly && (
                  <div className="text-xs text-green-400">
                    Save ${plan.monthlyPrice * 2} annually
                  </div>
                )}
                <p className="text-sm text-gray-400 mt-2">
                  {plan.description}
                </p>
              </div>
              <div className="flex-1 flex flex-col justify-between">
                <button
                  className={`w-full text-base font-semibold cursor-pointer transition-all duration-200 py-3 rounded-lg mb-6 flex items-center justify-center gap-2 ${plan.highlighted ? 'bg-white text-black hover:bg-gray-100 group' : 'bg-black text-white hover:bg-neutral-800 group'}`}
                  onClick={() =>
                    handleCheckout(
                      isMonthly
                        ? plan.monthlyPriceId
                        : plan.yearlyPriceId,
                      plan.id
                    )
                  }
                  disabled={checkoutLoading === plan.id}
                >
                  {checkoutLoading === plan.id
                    ? 'Processing...'
                    : 'Get started'}{' '}
                  <ArrowRight className="w-4 h-4 transition-transform group-hover:translate-x-1" />
                </button>
                <div>
                  <ul className="space-y-3 mt-2">
                    {plan.features.map((feature, index) => (
                      <li
                        key={index}
                        className="flex items-center gap-3"
                      >
                        <span className="inline-flex items-center justify-center bg-neutral-800 border border-neutral-600 rounded-lg p-1.5">
                          <svg
                            className="w-3 h-3 text-white"
                            viewBox="0 0 12 12"
                            fill="none"
                          >
                            <path
                              d="M10 3L4.5 8.5L2 6"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                        </span>
                        <span className="text-base text-gray-300">
                          {feature.text}
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
