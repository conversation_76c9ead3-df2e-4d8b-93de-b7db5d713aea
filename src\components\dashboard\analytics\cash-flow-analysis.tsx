"use client";

import { useEffect, useState } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ChartContainer, ChartTooltip } from "@/components/ui/chart";
import {
  CartesianGrid,
  XAxis,
  YAxis,
  ResponsiveContainer,
  ComposedChart,
  Bar,
  Line,
} from "recharts";
import { formatCurrency } from "@/lib/utils";
import { getCashFlowAnalysis } from "@/lib/actions/analytics";
import type { FilterValues } from "./data-filters";
import type { CashFlowAnalysis } from "@/types/analytics";
import { Skeleton } from "@/components/ui/skeleton";
import { motion } from "motion/react";
import {
  TrendingUp,
  Wallet,
  ArrowUpRight,
  ArrowDownRight,
  DollarSign,
  Activity,
} from "lucide-react";
import Colors from '@/components/theme/Colors';

interface CashFlowAnalysisProps {
  dateRange: {
    from: Date;
    to: Date;
  };
  filters: FilterValues;
}

export function CashFlowAnalysisComponent({
  dateRange,
  filters,
}: CashFlowAnalysisProps) {
  const [cashFlowData, setCashFlowData] = useState<CashFlowAnalysis | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function loadCashFlowData() {
      setLoading(true);
      setError(null);
      try {
        const data = await getCashFlowAnalysis(
          dateRange.from,
          dateRange.to,
          filters
        );
        setCashFlowData(data);
      } catch (err) {
        console.error("Failed to load cash flow analysis:", err);
        setError("Failed to load cash flow data");
      } finally {
        setLoading(false);
      }
    }

    loadCashFlowData();
  }, [dateRange, filters]);

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid gap-4 md:grid-cols-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-4 w-24" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-20" />
                <Skeleton className="h-3 w-16 mt-2" />
              </CardContent>
            </Card>
          ))}
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-48" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-[400px] w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !cashFlowData) {
    return (
      <Card className="border-destructive/20">
        <CardHeader>
          <CardTitle className="text-destructive">Error</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-destructive/80">
            {error || "Failed to load cash flow data"}
          </p>
        </CardContent>
      </Card>
    );
  }

  // Combine inflows and outflows for chart
  const combinedData = cashFlowData.monthlyInflows.map((inflow) => {
    const outflow = cashFlowData.monthlyOutflows.find(
      (o) => o.month === inflow.month
    ) || { value: 0 };
    return {
      month: inflow.month,
      inflows: inflow.value,
      outflows: outflow.value,
      netFlow: inflow.value - outflow.value,
    };
  });

  const chartConfig = {
    inflows: {
      label: "Cash Inflows",
      color: "hsl(var(--chart-1))",
    },
    outflows: {
      label: "Cash Outflows",
      color: "hsl(var(--chart-2))",
    },
    netFlow: {
      label: "Net Cash Flow",
      color: "hsl(var(--chart-3))",
    },
  };

  return (
    <div className="space-y-6">
      {/* Cash Flow Overview Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card className={`border border-[${Colors.primary}]/10 bg-[${Colors.background}]/50 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300`}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Net Cash Flow
              </CardTitle>
              <Wallet className={`h-4 w-4 text-[${Colors.text}]`} />
            </CardHeader>
            <CardContent>
              <div
                className={`text-2xl font-bold ${
                  cashFlowData.netCashFlow >= 0
                    ? `text-[${Colors.success}]`
                    : `text-[${Colors.error}]`
                }`}
              >
                {formatCurrency(cashFlowData.netCashFlow)}
              </div>
              <p className="text-xs text-muted-foreground">
                {cashFlowData.netCashFlow >= 0 ? "Positive" : "Negative"} cash
                flow
              </p>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <Card className={`border border-[${Colors.primary}]/10 bg-[${Colors.background}]/50 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300`}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Inflows
              </CardTitle>
              <ArrowUpRight className={`h-4 w-4 text-[${Colors.success}]`} />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold text-[${Colors.success}]`}>
                {formatCurrency(
                  cashFlowData.monthlyInflows.reduce(
                    (sum, item) => sum + item.value,
                    0
                  )
                )}
              </div>
              <p className="text-xs text-muted-foreground">Money coming in</p>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <Card className={`border border-[${Colors.primary}]/10 bg-[${Colors.background}]/50 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300`}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Outflows
              </CardTitle>
              <ArrowDownRight className={`h-4 w-4 text-[${Colors.error}]`} />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold text-[${Colors.error}]`}>
                {formatCurrency(
                  cashFlowData.monthlyOutflows.reduce(
                    (sum, item) => sum + item.value,
                    0
                  )
                )}
              </div>
              <p className="text-xs text-muted-foreground">Money going out</p>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
        >
          <Card className={`border border-[${Colors.primary}]/10 bg-[${Colors.background}]/50 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300`}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Burn Rate</CardTitle>
              <Activity className={`h-4 w-4 text-[${Colors.text}]`} />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold text-[${Colors.warning}]`}>
                {formatCurrency(cashFlowData.burnRate)}
              </div>
              <p className="text-xs text-muted-foreground">
                Monthly average outflow
              </p>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Cash Flow Chart */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.4 }}
      >
        <Card className={`border border-[${Colors.primary}]/10 bg-[${Colors.background}]/50 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300`}>
          <CardHeader>
            <CardTitle className="text-xl font-bold flex items-center gap-2">
              <TrendingUp className={`h-5 w-5 text-[${Colors.primary}]`} />
              Cash Flow Analysis
            </CardTitle>
            <p className="text-sm text-muted-foreground">
              Monthly cash inflows vs outflows and net cash flow
            </p>
          </CardHeader>
          <CardContent>
            <ChartContainer config={chartConfig} className="h-[400px]">
              <ResponsiveContainer width="100%" height="100%">
                <ComposedChart data={combinedData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis tickFormatter={(value) => formatCurrency(value)} />
                  <ChartTooltip
                    formatter={(value, name) => [
                      formatCurrency(value as number),
                      name === "inflows"
                        ? "Cash Inflows"
                        : name === "outflows"
                          ? "Cash Outflows"
                          : "Net Cash Flow",
                    ]}
                  />
                  <Bar dataKey="inflows" fill={Colors.success} name="inflows" />
                  <Bar dataKey="outflows" fill={Colors.error} name="outflows" />
                  <Line
                    type="monotone"
                    dataKey="netFlow"
                    stroke={Colors.primary}
                    strokeWidth={3}
                    name="netFlow"
                  />
                </ComposedChart>
              </ResponsiveContainer>
            </ChartContainer>
          </CardContent>
        </Card>
      </motion.div>

      {/* Additional Metrics */}
      <div className="grid gap-6 md:grid-cols-2">
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.5 }}
        >
          <Card className={`border border-[${Colors.primary}]/10 bg-[${Colors.background}]/50 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300`}>
            <CardHeader>
              <CardTitle className="text-lg font-bold flex items-center gap-2">
                <DollarSign className={`h-5 w-5 text-[${Colors.primary}]`} />
                Liquidity Ratio
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className={`text-3xl font-bold text-[${Colors.info}]`}>
                {cashFlowData.liquidityRatio.toFixed(2)}
              </div>
              <p className="text-sm text-muted-foreground mt-2">
                {cashFlowData.liquidityRatio >= 1
                  ? "Good liquidity position"
                  : "Consider improving cash inflows"}
              </p>
              <div className="mt-4 space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Cash Inflows</span>
                  <span className={`font-medium text-[${Colors.success}]`}>
                    {formatCurrency(
                      cashFlowData.monthlyInflows.reduce(
                        (sum, item) => sum + item.value,
                        0
                      )
                    )}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Cash Outflows</span>
                  <span className={`font-medium text-[${Colors.error}]`}>
                    {formatCurrency(
                      cashFlowData.monthlyOutflows.reduce(
                        (sum, item) => sum + item.value,
                        0
                      )
                    )}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
        >
          <Card className={`border border-[${Colors.primary}]/10 bg-[${Colors.background}]/50 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300`}>
            <CardHeader>
              <CardTitle className="text-lg font-bold flex items-center gap-2">
                <Activity className={`h-5 w-5 text-[${Colors.text}]`} />
                Cash Flow Health
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Overall Status</span>
                  <span
                    className={`px-2 py-1 rounded-full text-xs font-medium ${
                      cashFlowData.netCashFlow >= 0
                        ? `bg-[${Colors.success}] text-[${Colors.textLight}]`
                        : `bg-[${Colors.error}] text-[${Colors.textLight}]`
                    }`}
                  >
                    {cashFlowData.netCashFlow >= 0
                      ? "Healthy"
                      : "Needs Attention"}
                  </span>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Monthly Burn Rate</span>
                    <span className={`font-medium text-[${Colors.warning}]`}>
                      {formatCurrency(cashFlowData.burnRate)}
                    </span>
                  </div>

                  <div className="flex justify-between text-sm">
                    <span>Liquidity Ratio</span>
                    <span
                      className={`font-medium ${
                        cashFlowData.liquidityRatio >= 1
                          ? `text-[${Colors.success}]`
                          : `text-[${Colors.error}]`
                      }`}
                    >
                      {cashFlowData.liquidityRatio.toFixed(2)}
                    </span>
                  </div>
                </div>

                <div className="pt-2 border-t">
                  <p className="text-xs text-muted-foreground">
                    {cashFlowData.netCashFlow >= 0
                      ? "Your business is generating positive cash flow. Continue monitoring to maintain this healthy position."
                      : "Consider strategies to improve cash inflows or reduce outflows to achieve positive cash flow."}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}
