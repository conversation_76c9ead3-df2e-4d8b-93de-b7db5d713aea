import { But<PERSON> } from "@/components/ui/button";
import { FilePlus } from "lucide-react";
import Link from "next/link";
import UpdateInvoiceTypesButton from "@/app/dashboard/reports/update-invoice-types";

interface ReportHeaderProps {
  activeTab: string;
}

export function ReportHeader({ activeTab }: ReportHeaderProps) {
  const getTitle = () => {
    switch (activeTab) {
      case "templates":
        return "Report Templates";
      case "scheduled":
        return "Scheduled Reports";
      case "custom":
        return "Custom Reports";
      default:
        return "Reports Overview";
    }
  };

  return (
    <header className="sticky top-0 z-10 border-b bg-background">
      <div className="flex h-16 items-center justify-between px-6">
        <h1 className="text-xl font-semibold">{getTitle()}</h1>

        <div className="flex items-center gap-2 md:gap-4">
          <UpdateInvoiceTypesButton />
          <Link href="/dashboard/reports/new">
            <Button size="sm">
              <FilePlus className="mr-2 h-4 w-4" />
              New Report
            </Button>
          </Link>
        </div>
      </div>
    </header>
  );
}
