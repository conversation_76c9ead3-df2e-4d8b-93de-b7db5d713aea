import { NextRequest, NextResponse } from 'next/server';
import { auth, currentUser } from '@clerk/nextjs/server';
import db from '@/db/db';

const PADDLE_API_URL =
  process.env.PADDLE_ENVIRONMENT === 'production'
    ? 'https://api.paddle.com'
    : 'https://sandbox-api.paddle.com';

const PADDLE_HEADERS = {
  Authorization: `Bearer ${process.env.PADDLE_API_KEY}`,
  'Content-Type': 'application/json',
};

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const user = await currentUser();
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Get user from database
    const dbUser = await db.user.findUnique({
      where: { clerkId: userId },
    });

    if (!dbUser) {
      return NextResponse.json(
        { error: 'Database user not found' },
        { status: 404 }
      );
    }

    const body = await request.json();
    const { priceId, planId, billingCycle, returnUrl } = body;

    if (!priceId) {
      return NextResponse.json(
        { error: 'Price ID is required' },
        { status: 400 }
      );
    }

    // Create checkout session with Paddle
    const checkoutData = {
      items: [
        {
          price_id: priceId,
          quantity: 1,
        },
      ],
      customer_email: user.emailAddresses[0]?.emailAddress,
      custom_data: {
        user_id: dbUser.id,
        clerk_id: userId,
        plan_id: planId,
        billing_cycle: billingCycle,
        return_url: returnUrl,
      },
      checkout: {
        settings: {
          success_url: returnUrl
            ? `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/subscription?success=true&session_id={checkout.id}&return_url=${encodeURIComponent(returnUrl)}`
            : `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/subscription?success=true&session_id={checkout.id}`,
        },
      },
      discount_id: null, // Can be used for promotional codes
    };

    console.log('Creating Paddle checkout with data:', {
      priceId,
      userEmail: user.emailAddresses[0]?.emailAddress,
      userId: dbUser.id,
    });

    const response = await fetch(`${PADDLE_API_URL}/transactions`, {
      method: 'POST',
      headers: PADDLE_HEADERS,
      body: JSON.stringify(checkoutData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Paddle API error:', errorData);
      return NextResponse.json(
        {
          success: false,
          error: `Paddle API error: ${response.status}`,
          details: errorData,
        },
        { status: 400 }
      );
    }

    const result = await response.json();
    console.log(
      'Paddle checkout created successfully:',
      result.data?.id
    );

    return NextResponse.json({
      success: true,
      data: {
        ...result.data,
        // Ensure checkout URL is available for fallback
        checkout: {
          url:
            result.data?.checkout?.url ||
            `https://checkout.paddle.com/transaction/${result.data?.id}`,
        },
      },
    });
  } catch (error) {
    console.error('Error creating Paddle checkout:', error);
    return NextResponse.json(
      {
        success: false,
        error:
          error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
