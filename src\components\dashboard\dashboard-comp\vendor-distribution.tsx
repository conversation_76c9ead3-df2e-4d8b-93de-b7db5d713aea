import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendItem,
} from "@/components/dashboard/dashboard-comp/chart";
import { <PERSON>, Pie, Pie<PERSON>hart, ResponsiveContainer, Tooltip } from "recharts";
import { useEffect, useState } from "react";

const COLORS = ["#6359E9", "#00F6FF", "#01ADE6", "#4000FF", "#114F64"];

export function VendorDistribution() {
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    setLoading(true);
    fetch("/api/invoices/stats")
      .then(async (res) => {
        if (!res.ok) throw new Error("Failed to fetch vendor data");
        const apiData = await res.json();
        // Map API topVendors to chart format
        const chartData = (apiData.topVendors || []).map((v: any) => ({
          name: v.name,
          value: v.count,
        }));
        setData(chartData);
        setError(null);
      })
      .catch((err) => setError(err.message))
      .finally(() => setLoading(false));
  }, []);

  if (loading) return <div className="text-center py-8">Loading vendors...</div>;
  if (error) return <div className="text-center text-red-500 py-8">{error}</div>;

  return (
    <ChartContainer className="h-[300px] relative">
      {/* Glow effect */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-32 h-32 rounded-full bg-[#4000FF] opacity-20 blur-[50px]"></div>
      </div>
      
      <ResponsiveContainer width="100%" height="75%">
        <PieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            labelLine={false}
            outerRadius={80}
            innerRadius={50}
            fill="#8884d8"
            dataKey="value"
            strokeWidth={0}
          >
            {data.map((entry, index) => (
              <Cell
                key={`cell-${index}`}
                fill={COLORS[index % COLORS.length]}
                className={index === 0 ? "" : index === 1 ? "" : "opacity-50"}
              />
            ))}
          </Pie>
          <Tooltip content={<CustomTooltip active={false} payload={[]} />} />
        </PieChart>
      </ResponsiveContainer>
      <ChartLegend className="mt-2 justify-center gap-6 flex-wrap">
        {data.map((entry, index) => (
          <ChartLegendItem
            key={`legend-${index}`}
            name={entry.name}
            color={COLORS[index % COLORS.length]}
            className="text-sm"
            colorClassName="w-3 h-3 rounded-full"
          />
        ))}
      </ChartLegend>
    </ChartContainer>
  );
}

function CustomTooltip({
  active,
  payload,
}: {
  active: boolean;
  payload: Array<{ name: string; value: number; payload: { fill: string } }>;
}) {
  if (active && payload && payload.length) {
    return (
      <ChartTooltip className="bg-[#0B1739] border border-[#01ADE6]/30 shadow-lg shadow-[#00BBFF]/10">
        <ChartTooltipContent>
          <div className="font-medium text-white">{payload[0].name}</div>
          <div className="flex items-center gap-2 mt-2">
            <div
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: payload[0].payload.fill }}
            />
            <div className="text-[rgba(227,232,243,0.3)]">Percentage:</div>
            <div className="font-medium text-white">{payload[0].value}%</div>
          </div>
        </ChartTooltipContent>
      </ChartTooltip>
    );
  }

  return null;
}
