import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Minus, Plus } from 'lucide-react';

interface QuantityFieldProps {
  quantity: number;
  onQuantityChange: (quantity: number) => void;
  min?: number;
  max?: number;
}

export function QuantityField({ 
  quantity, 
  onQuantityChange, 
  min = 1, 
  max = 999 
}: QuantityFieldProps) {
  const handleDecrease = () => {
    if (quantity > min) {
      onQuantityChange(quantity - 1);
    }
  };

  const handleIncrease = () => {
    if (quantity < max) {
      onQuantityChange(quantity + 1);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value);
    if (!isNaN(value) && value >= min && value <= max) {
      onQuantityChange(value);
    }
  };

  return (
    <div className="flex items-center gap-2">
      <Button
        variant="outline"
        size="sm"
        onClick={handleDecrease}
        disabled={quantity <= min}
        className="h-8 w-8 p-0 border-neutral-700 bg-neutral-900 hover:bg-neutral-800 text-white disabled:opacity-50"
      >
        <Minus className="h-3 w-3" />
      </Button>
      
      <Input
        type="number"
        value={quantity}
        onChange={handleInputChange}
        min={min}
        max={max}
        className="w-16 h-8 text-center bg-neutral-900 border-neutral-700 text-white"
      />
      
      <Button
        variant="outline"
        size="sm"
        onClick={handleIncrease}
        disabled={quantity >= max}
        className="h-8 w-8 p-0 border-neutral-700 bg-neutral-900 hover:bg-neutral-800 text-white disabled:opacity-50"
      >
        <Plus className="h-3 w-3" />
      </Button>
    </div>
  );
}
