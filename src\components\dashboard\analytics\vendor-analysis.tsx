"use client";

import { useEffect, useState, use<PERSON><PERSON>back, memo } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { ChartContainer, ChartTooltip } from "@/components/ui/chart";
import {
  Bar,
  BarChart,
  CartesianGrid,
  Cell,
  ResponsiveContainer,
  XAxis,
  YAxis,
} from "recharts";
import { formatCurrency } from "@/lib/utils";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { getVendorAnalysis } from "@/lib/actions/analytics";
import type { FilterValues } from "./data-filters";
import { AlertCircle, Building2, Users } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface VendorAnalysisProps {
  dateRange: {
    from: Date;
    to: Date;
  };
  filters: FilterValues;
}

interface VendorData {
  id: string;
  name: string;
  value: number;
  percentage: number;
  color: string;
}

interface VendorPaymentHistory {
  id: string;
  vendor: string;
  date: string;
  amount: number;
  status: string;
}

interface ChartConfig {
  [key: string]: {
    label: string;
  };
}

const VendorBarChart = memo(
  ({
    topVendors,
    chartConfig,
    onBarMouseEnter,
    onBarMouseLeave,
    activeIndex,
  }: {
    topVendors: VendorData[];
    chartConfig: ChartConfig;
    onBarMouseEnter: (_: unknown, index: number) => void;
    onBarMouseLeave: () => void;
    activeIndex: number | undefined;
  }) => (
    <ChartContainer config={chartConfig} className="h-[400px]">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={topVendors}
          layout="vertical"
          margin={{
            top: 5,
            right: 30,
            left: 100,
            bottom: 5,
          }}
          onMouseMove={onBarMouseEnter}
          onMouseLeave={onBarMouseLeave}
        >
          <defs>
            {topVendors.map((entry, index) => (
              <linearGradient
                key={`gradient-${index}`}
                id={`vendorGradient-${index}`}
                x1="0"
                y1="0"
                x2="1"
                y2="0"
              >
                <stop offset="0%" stopColor={entry.color} stopOpacity={0.8} />
                <stop offset="100%" stopColor={entry.color} stopOpacity={0.3} />
              </linearGradient>
            ))}
          </defs>
          <CartesianGrid
            strokeDasharray="3 3"
            horizontal={true}
            vertical={false}
            stroke="hsl(var(--border))"
            opacity={0.3}
          />
          <XAxis
            type="number"
            tickFormatter={(value) => `$${value / 1000}k`}
            tickLine={false}
            axisLine={false}
            stroke="hsl(var(--foreground))"
            fontSize={12}
          />
          <YAxis
            type="category"
            dataKey="name"
            tickLine={false}
            axisLine={false}
            width={100}
            stroke="hsl(var(--foreground))"
            fontSize={12}
          />
          <ChartTooltip
            content={({ active, payload }) => {
              if (active && payload && payload.length) {
                const data = payload[0].payload;
                return (
                  <div className="rounded-lg border bg-background p-3 shadow-md">
                    <div className="grid grid-cols-2 gap-2">
                      <div className="flex flex-col">
                        <span className="text-[0.70rem] uppercase text-muted-foreground">
                          Vendor
                        </span>
                        <span className="font-bold text-foreground">
                          {data.name}
                        </span>
                      </div>
                      <div className="flex flex-col">
                        <span className="text-[0.70rem] uppercase text-muted-foreground">
                          Amount
                        </span>
                        <span className="font-bold">
                          {formatCurrency(data.value)}
                        </span>
                      </div>
                      <div className="flex flex-col">
                        <span className="text-[0.70rem] uppercase text-muted-foreground">
                          Percentage
                        </span>
                        <span className="font-bold">{data.percentage}%</span>
                      </div>
                    </div>
                  </div>
                );
              }
              return null;
            }}
          />
          <Bar
            dataKey="value"
            radius={[4, 4, 4, 4]}
            animationDuration={1500}
            animationBegin={300}
          >
            {topVendors.map((entry, index) => (
              <Cell
                key={`cell-${index}`}
                fill={`url(#vendorGradient-${index})`}
                stroke={entry.color}
                strokeWidth={1}
                className="transition-all duration-300"
                style={{
                  filter:
                    activeIndex === index
                      ? "drop-shadow(0px 0px 6px rgba(0, 0, 0, 0.3))"
                      : "none",
                  opacity:
                    activeIndex === undefined || activeIndex === index
                      ? 1
                      : 0.7,
                }}
              />
            ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    </ChartContainer>
  )
);

VendorBarChart.displayName = "VendorBarChart";

const PaymentHistoryTable = memo(
  ({ paymentHistory }: { paymentHistory: VendorPaymentHistory[] }) => {
    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 100; // Show up to 100 records per page instead of a limit

    // Calculate the records to display based on pagination
    const startIndex = (currentPage - 1) * itemsPerPage;
    const displayedRecords = paymentHistory.slice(
      startIndex,
      startIndex + itemsPerPage
    );
    const totalPages = Math.ceil(paymentHistory.length / itemsPerPage);

    return (
      <div className="rounded-md border border-primary/10 overflow-hidden">
        <div className="max-h-[400px] overflow-y-auto">
          <Table>
            <TableHeader className="bg-primary/5 sticky top-0 z-10">
              <TableRow>
                <TableHead className="font-semibold">Vendor</TableHead>
                <TableHead className="font-semibold">Date</TableHead>
                <TableHead className="text-right font-semibold">
                  Amount
                </TableHead>
                <TableHead className="font-semibold">Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {displayedRecords.map((payment, index) => (
                <TableRow
                  key={payment.id}
                  className={
                    index % 2 === 0 ? "bg-background/50" : "bg-background/80"
                  }
                >
                  <TableCell className="font-medium">
                    {payment.vendor}
                  </TableCell>
                  <TableCell>
                    {new Date(payment.date).toLocaleDateString(undefined, {
                      year: "numeric",
                      month: "short",
                      day: "numeric",
                    })}
                  </TableCell>
                  <TableCell className="text-right font-semibold">
                    {formatCurrency(payment.amount)}
                  </TableCell>
                  <TableCell>
                    <span
                      className={`inline-flex items-center rounded-full px-2.5 py-1 text-xs font-medium ${
                        payment.status === "PAID"
                          ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300"
                          : payment.status === "PENDING"
                            ? "bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300"
                            : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300"
                      }`}
                    >
                      {payment.status}
                    </span>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {totalPages > 1 && (
          <div className="flex items-center justify-between p-2 border-t">
            <div className="text-sm text-muted-foreground">
              Showing {startIndex + 1}-
              {Math.min(startIndex + itemsPerPage, paymentHistory.length)} of{" "}
              {paymentHistory.length} invoices
            </div>
            <div className="flex gap-1">
              <button
                className="px-2 py-1 text-sm rounded bg-primary/10 disabled:opacity-50"
                disabled={currentPage === 1}
                onClick={() => setCurrentPage((p) => Math.max(1, p - 1))}
              >
                Previous
              </button>
              <button
                className="px-2 py-1 text-sm rounded bg-primary/10 disabled:opacity-50"
                disabled={currentPage === totalPages}
                onClick={() =>
                  setCurrentPage((p) => Math.min(totalPages, p + 1))
                }
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>
    );
  }
);

PaymentHistoryTable.displayName = "PaymentHistoryTable";

export function VendorAnalysis({ dateRange, filters }: VendorAnalysisProps) {
  const [topVendors, setTopVendors] = useState<VendorData[]>([]);
  const [paymentHistory, setPaymentHistory] = useState<VendorPaymentHistory[]>(
    []
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeIndex, setActiveIndex] = useState<number | undefined>(undefined);

  useEffect(() => {
    let isMounted = true;

    async function loadVendorData() {
      setLoading(true);
      setError(null);

      try {
        const { vendorAnalysis, paymentHistory } = await getVendorAnalysis(
          dateRange.from,
          dateRange.to,
          filters
        );

        if (isMounted) {
          setTopVendors(vendorAnalysis);
          setPaymentHistory(paymentHistory);
        }
      } catch (err) {
        if (isMounted) {
          console.error("Failed to load vendor analysis:", err);
          setError("Failed to load vendor data. Please try again later.");
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    }

    loadVendorData();

    return () => {
      isMounted = false;
    };
  }, [dateRange, filters]);

  const chartConfig = {
    vendor: {
      label: "Vendor",
    },
  };

  const handleBarMouseEnter = useCallback((_: unknown, index: number) => {
    setActiveIndex(index);
  }, []);

  const handleBarMouseLeave = useCallback(() => {
    setActiveIndex(undefined);
  }, []);

  const renderNoData = useCallback(
    () => (
      <div className="flex h-full items-center justify-center">
        <p className="text-muted-foreground">
          No vendor data available for the selected filters
        </p>
      </div>
    ),
    []
  );

  const renderLoading = useCallback(
    () => (
      <div className="flex h-full items-center justify-center">
        <div className="animate-pulse flex flex-col items-center gap-2">
          <div className="h-8 w-8 rounded-full bg-primary/20"></div>
          <p className="text-muted-foreground">Loading chart data...</p>
        </div>
      </div>
    ),
    []
  );

  const renderError = useCallback(
    () => (
      <Alert variant="destructive" className="my-4">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    ),
    [error]
  );

  return (
    <div className="grid gap-6 md:grid-cols-2">
      <Card className="border border-primary/10 bg-card/50 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300">
        <CardHeader className="flex flex-row items-start justify-between">
          <div>
            <CardTitle className="text-xl font-bold flex items-center gap-2">
              <Building2 className="h-5 w-5 text-primary" />
              Top Vendors by Spend
            </CardTitle>
            <CardDescription>
              Vendors receiving the most business
            </CardDescription>
          </div>
        </CardHeader>
        <CardContent>
          {error ? (
            renderError()
          ) : (
            <>
              {loading ? (
                renderLoading()
              ) : topVendors.length === 0 ? (
                renderNoData()
              ) : (
                <VendorBarChart
                  topVendors={topVendors}
                  chartConfig={chartConfig}
                  onBarMouseEnter={handleBarMouseEnter}
                  onBarMouseLeave={handleBarMouseLeave}
                  activeIndex={activeIndex}
                />
              )}
            </>
          )}
        </CardContent>
      </Card>
      <Card className="border border-primary/10 bg-card/50 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300">
        <CardHeader className="flex flex-row items-start justify-between">
          <div>
            <CardTitle className="text-xl font-bold flex items-center gap-2">
              <Users className="h-5 w-5 text-primary" />
              Vendor Payment History
            </CardTitle>
            <CardDescription>Recent payments to vendors</CardDescription>
          </div>
        </CardHeader>
        <CardContent>
          {error ? (
            renderError()
          ) : (
            <>
              {loading ? (
                <div className="flex h-[400px] items-center justify-center">
                  <div className="animate-pulse flex flex-col items-center gap-2">
                    <div className="h-8 w-8 rounded-full bg-primary/20"></div>
                    <p className="text-muted-foreground">
                      Loading payment history...
                    </p>
                  </div>
                </div>
              ) : paymentHistory.length === 0 ? (
                <div className="flex h-[400px] items-center justify-center">
                  <p className="text-muted-foreground">
                    No payment history found for the selected filters
                  </p>
                </div>
              ) : (
                <PaymentHistoryTable paymentHistory={paymentHistory} />
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}