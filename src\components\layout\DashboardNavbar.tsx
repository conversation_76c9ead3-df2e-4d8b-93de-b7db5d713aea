'use client';

import { Button } from '@/components/ui/button';
import { ThemeSwitcher } from '@/components/theme/theme-switcher';
import { Bell, Menu } from 'lucide-react';
import { UserButton } from '@clerk/nextjs';

interface DashboardNavbarProps {
  onMenuClick?: () => void;
}

export default function DashboardNavbar({
  onMenuClick,
}: DashboardNavbarProps) {
  return (
    <header className="sticky top-0 z-30 flex h-16 items-center justify-between bg-white dark:bg-[#0B1739]/90 backdrop-blur-sm px-4 sm:px-6 border-b border-gray-200 dark:border-[#0097B1]/20">
      <div className="flex items-center gap-3">
        <Button
          variant="ghost"
          size="icon"
          onClick={onMenuClick}
          className="lg:hidden text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100"
        >
          <Menu className="h-5 w-5" />
          <span className="sr-only">Menu</span>
        </Button>
      </div>

      <div className="flex items-center gap-2 md:gap-3">
        <ThemeSwitcher />

        <Button
          variant="ghost"
          size="icon"
          className="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100"
        >
          <Bell className="h-5 w-5" />
          <span className="sr-only">Notifications</span>
        </Button>

        {/* Clerk UserButton */}
        <UserButton
          afterSignOutUrl="/"
          userProfileMode="modal"
          appearance={{
            elements: {
              userButtonAvatarBox: {
                width: '32px',
                height: '32px',
                '&:hover': {
                  opacity: 0.8,
                },
              },
              userButtonTrigger: {
                border: '1px solid transparent',
                borderRadius: '9999px',
                padding: '1px 8px 1px 1px',
                transition: 'all 0.2s',
                '&:hover': {
                  backgroundColor: 'var(--gray-100)',
                  'dark.backgroundColor': 'rgba(27, 56, 72, 0.5)',
                },
              },
              userButtonPopoverCard: {
                'dark.backgroundColor': '#0B1739',
                'dark.borderColor': 'rgba(0, 151, 177, 0.2)',
              },
              userButtonPopoverActionButton: {
                'dark.color': 'white',
                '&:hover': {
                  'dark.backgroundColor': 'rgba(27, 56, 72, 0.5)',
                },
              },
              userButtonPopoverActionButtonText: {
                'dark.color': 'white',
              },
              userButtonPopoverActionButtonIcon: {
                'dark.color': 'white',
              },
            },
          }}
        />
      </div>
    </header>
  );
}
